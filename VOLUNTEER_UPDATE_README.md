# 志愿填报系统更新说明

## 新增功能

### 1. 填报模式切换
- **院校优先模式**: 先选择院校，再从该院校的可填专业中选择具体专业
- **专业优先模式**: 直接选择专业，系统自动关联对应院校

### 2. 录取等级筛选
- **全部**: 显示所有院校/专业
- **冲**: 仅显示录取难度较大的院校/专业（冲刺志愿）
- **稳**: 仅显示录取概率适中的院校/专业（稳妥志愿）
- **保**: 仅显示录取概率较高的院校/专业（保底志愿）

### 3. 列表展示方式
- 改为列表形式展示，不再使用卡片形式
- 院校优先模式：显示院校列表，点击"查看专业"展开可填专业
- 专业优先模式：直接显示专业列表，可直接选择

## 技术实现

### 新增类型定义
```typescript
// 填报模式
export type VolunteerMode = 'university' | 'major'

// 录取概率等级
export type AdmissionLevel = 'all' | 'rush' | 'stable' | 'safe'

// 志愿专业信息
export interface VolunteerMajor {
  id: string
  code: string
  name: string
  category: string
  subCategory?: string
  university: {
    id: string
    name: string
  }
  admissionScore?: {
    year: number
    minScore: number
    avgScore: number
    ranking: number
  }[]
  features?: string[]
  description?: string
}
```

### 新增组件
- `UniversityListView.tsx`: 院校优先模式的列表视图
- `MajorListView.tsx`: 专业优先模式的列表视图

### 修改的组件
- `VolunteerApplicationPage.tsx`: 添加模式切换和等级筛选UI
- `VolunteerForm.tsx`: 支持两种模式的志愿显示
- `volunteer.ts`: 扩展类型定义

## 使用说明

### 1. 模式切换
在页面顶部可以看到两个模式切换按钮：
- **院校优先**: 适合对特定院校有偏好的考生
- **专业优先**: 适合对特定专业有明确目标的考生

### 2. 等级筛选
在页面顶部右侧可以选择录取等级：
- **全部**: 查看所有选项
- **冲**: 查看冲刺类志愿（录取难度大）
- **稳**: 查看稳妥类志愿（录取概率适中）
- **保**: 查看保底类志愿（录取概率高）

### 3. 院校优先模式操作流程
1. 在院校列表中浏览院校
2. 点击"查看专业"按钮展开该院校的可填专业
3. 在展开的专业列表中点击"选择"按钮添加到志愿表
4. 志愿表中会显示院校名称和选择的专业

### 4. 专业优先模式操作流程
1. 在专业列表中浏览专业
2. 直接点击专业的"选择"按钮添加到志愿表
3. 志愿表中会显示专业名称和对应的院校

## 数据结构变化

### 院校数据新增字段
```typescript
availableMajors?: VolunteerMajor[] // 可填报的专业列表
```

### 志愿选择数据结构变化
```typescript
export interface VolunteerChoice {
  id: string
  university?: VolunteerUniversity // 院校优先模式使用
  major?: VolunteerMajor // 专业优先模式使用
  majors: string[] // 院校优先模式下选择的专业
  priority: number
  isLocked: boolean
  notes?: string
  addedAt: Date
  mode: VolunteerMode // 填报模式
}
```

## 录取概率分析算法

系统根据考生分数和位次，结合院校/专业历年录取数据，自动计算录取概率：

- **保底(safe)**: 考生分数超过历年最低分20分以上，且位次领先1000位以上
- **稳妥(stable)**: 考生分数超过历年最低分10分以上，且位次领先500位以上
- **冲刺(rush)**: 考生分数接近或略低于历年最低分，位次在500位以内

## 注意事项

1. 两种模式可以随时切换，已添加的志愿会保留
2. 等级筛选只影响列表显示，不会删除已添加的志愿
3. 院校优先模式下，必须选择具体专业才能添加到志愿表
4. 专业优先模式下，直接选择专业即可添加到志愿表
5. 志愿表中会根据模式显示不同的信息格式

## 后续优化建议

1. 添加更多院校和专业数据
2. 完善录取概率分析算法
3. 添加专业详情页面
4. 支持批量导入院校专业数据
5. 添加志愿推荐功能
