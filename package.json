{"name": "fh-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "proxy": "cd server && npm run start", "proxy:dev": "cd server && npm run dev", "setup-proxy": "cd server && npm install", "dev:full": "concurrently \"npm run proxy:dev\" \"npm run dev\""}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/bootstrap": "^5.2.10", "bootstrap": "^5.3.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.525.0", "motion": "^12.23.6", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}