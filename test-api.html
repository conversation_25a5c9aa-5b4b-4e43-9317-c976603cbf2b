<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试一分一段API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>一分一段API测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="year">年份:</label>
            <select id="year">
                <option value="2024">2024</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="provincename">省份:</label>
            <select id="provincename">
                <option value="安徽">安徽</option>
                <option value="北京">北京</option>
                <option value="上海">上海</option>
                <option value="江苏">江苏</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="subjectselection">科目类型:</label>
            <select id="subjectselection">
                <option value="物理类">物理类</option>
                <option value="历史类">历史类</option>
                <option value="理科">理科</option>
                <option value="文科">文科</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="minscore">最低分数 (可选):</label>
            <input type="number" id="minscore" placeholder="如: 400">
        </div>
        
        <div class="form-group">
            <label for="maxscore">最高分数 (可选):</label>
            <input type="number" id="maxscore" placeholder="如: 600">
        </div>
        
        <div class="form-group">
            <label for="batchname">批次名称 (可选):</label>
            <select id="batchname">
                <option value="">全部批次</option>
                <option value="本科批">本科批</option>
                <option value="专科批">专科批</option>
                <option value="本科一批">本科一批</option>
                <option value="本科二批">本科二批</option>
            </select>
        </div>
        
        <button type="submit">查询</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const params = new URLSearchParams();
            
            // 添加必需参数
            params.append('year', document.getElementById('year').value);
            params.append('provincename', document.getElementById('provincename').value);
            params.append('subjectselection', document.getElementById('subjectselection').value);
            
            // 添加可选参数
            const minscore = document.getElementById('minscore').value;
            const maxscore = document.getElementById('maxscore').value;
            const batchname = document.getElementById('batchname').value;
            
            if (minscore) params.append('minscore', minscore);
            if (maxscore) params.append('maxscore', maxscore);
            if (batchname) params.append('batchname', batchname);
            
            const url = `http://localhost:3001/admin-api/system/score-segment/by-condition?${params.toString()}`;
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在查询...</p>';
            
            try {
                console.log('请求URL:', url);
                const response = await fetch(url);
                const data = await response.json();
                
                console.log('响应数据:', data);
                
                if (data.code === 0) {
                    let html = `<p class="success">查询成功！共获取到 ${data.data.length} 条数据</p>`;
                    
                    if (data.data.length > 0) {
                        html += `
                            <table>
                                <thead>
                                    <tr>
                                        <th>分数</th>
                                        <th>考生人数</th>
                                        <th>累计人数</th>
                                        <th>位次</th>
                                        <th>批次</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.data.slice(0, 10).forEach(item => {
                            html += `
                                <tr>
                                    <td>${item.examinationScore}</td>
                                    <td>${item.candidateCount}</td>
                                    <td>${item.totalCandidates.toLocaleString()}</td>
                                    <td>${item.ranking || item.rankingRange}</td>
                                    <td>${item.admissionBatchName}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        
                        if (data.data.length > 10) {
                            html += `<p>只显示前10条数据，共${data.data.length}条</p>`;
                        }
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<p class="error">查询失败: ${data.msg}</p>`;
                }
            } catch (error) {
                console.error('请求错误:', error);
                resultDiv.innerHTML = `<p class="error">请求失败: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
