/**
 * 测试录取分数线API
 */

const API_BASE_URL = 'http://localhost:3001/api/gugudata'
const API_KEY = 'VB8WXGDWJ47VAGW54MAVGYMGTBYZNJJ3'

// 测试省录取分数线API
async function testProvinceScoreLine() {
  console.log('\n🧪 测试省录取分数线API...')
  
  try {
    const params = new URLSearchParams({
      appkey: API_KEY,
      keyword: '安徽',
      year: '2024',
      category: '物理类'
    })
    
    const url = `${API_BASE_URL}/metadata/ceeprovince?${params.toString()}`
    console.log('🔍 请求URL:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 省录取分数线API测试成功')
    console.log('📊 状态码:', data.DataStatus?.StatusCode)
    console.log('📋 数据条数:', data.Data?.length || 0)
    
    if (data.Data && data.Data.length > 0) {
      console.log('📄 示例数据:')
      console.log('  省份:', data.Data[0].Province)
      console.log('  年份:', data.Data[0].Year)
      console.log('  类别:', data.Data[0].Category)
      console.log('  批次:', data.Data[0].ScoreBatch)
      console.log('  分数线:', data.Data[0].Score)
    }
    
    return data
  } catch (error) {
    console.error('❌ 省录取分数线API测试失败:', error)
    throw error
  }
}

// 测试高校录取分数线API
async function testCollegeScoreLine() {
  console.log('\n🧪 测试高校录取分数线API...')
  
  try {
    const params = new URLSearchParams({
      appkey: API_KEY,
      searchtype: 'PROVINCENAME',
      keyword: '安徽',
      pageindex: '1',
      pagesize: '5',
      year: '2024',
      type: '物理类'
    })
    
    const url = `${API_BASE_URL}/metadata/ceecollegeline?${params.toString()}`
    console.log('🔍 请求URL:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 高校录取分数线API测试成功')
    console.log('📊 状态码:', data.DataStatus?.StatusCode)
    console.log('📋 数据条数:', data.Data?.length || 0)
    console.log('📈 总数据量:', data.DataStatus?.DataTotalCount || 0)
    
    if (data.Data && data.Data.length > 0) {
      console.log('📄 示例数据:')
      console.log('  高校:', data.Data[0].CollegeName)
      console.log('  省份:', data.Data[0].Province)
      console.log('  年份:', data.Data[0].Year)
      console.log('  最低分:', data.Data[0].LowestScore)
      console.log('  最低位次:', data.Data[0].LowestRank)
      console.log('  是否985:', data.Data[0].Is985)
      console.log('  是否211:', data.Data[0].Is211)
    }
    
    return data
  } catch (error) {
    console.error('❌ 高校录取分数线API测试失败:', error)
    throw error
  }
}

// 测试按高校名称查询
async function testCollegeScoreLineByName() {
  console.log('\n🧪 测试按高校名称查询录取分数线...')
  
  try {
    const params = new URLSearchParams({
      appkey: API_KEY,
      searchtype: 'COLLEGENAME',
      keyword: '北京大学',
      pageindex: '1',
      pagesize: '5',
      year: '2024',
      enrollprovince: '安徽'
    })
    
    const url = `${API_BASE_URL}/metadata/ceecollegeline?${params.toString()}`
    console.log('🔍 请求URL:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 按高校名称查询测试成功')
    console.log('📊 状态码:', data.DataStatus?.StatusCode)
    console.log('📋 数据条数:', data.Data?.length || 0)
    
    if (data.Data && data.Data.length > 0) {
      console.log('📄 北京大学在安徽的录取数据:')
      data.Data.forEach((item, index) => {
        console.log(`  [${index + 1}] ${item.CollegeName} - ${item.TypeName}`)
        console.log(`      最低分: ${item.LowestScore}, 位次: ${item.LowestRank}`)
        console.log(`      批次: ${item.AdmissionBatch}`)
      })
    }
    
    return data
  } catch (error) {
    console.error('❌ 按高校名称查询测试失败:', error)
    throw error
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试录取分数线API...')
  console.log('=' * 50)
  
  try {
    // 测试省录取分数线
    await testProvinceScoreLine()
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试高校录取分数线（按省份）
    await testCollegeScoreLine()
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试高校录取分数线（按高校名称）
    await testCollegeScoreLineByName()
    
    console.log('\n🎉 所有API测试完成！')
    
  } catch (error) {
    console.error('\n💥 测试过程中出现错误:', error)
  }
}

// 如果是在Node.js环境中运行
if (typeof window === 'undefined') {
  // 需要安装 node-fetch: npm install node-fetch
  const fetch = require('node-fetch')
  runTests()
} else {
  // 在浏览器中运行
  runTests()
}
