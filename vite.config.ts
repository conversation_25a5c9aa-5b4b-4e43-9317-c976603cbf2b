import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vite"

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,
    hmr: {
      host: '0.0.0.0'
    }, // 热重载也允许外部访问
    proxy: {
      '/api/baidu': {
        target: 'https://qianfan.baidubce.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/baidu/, ''),
        headers: {
          'Origin': 'https://qianfan.baidubce.com'
        }
      },
      '/api/gugudata': {
        target: 'https://api.gugudata.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/gugudata/, ''),
        headers: {
          'Origin': 'https://api.gugudata.com'
        }
      }
    }
  }
})
