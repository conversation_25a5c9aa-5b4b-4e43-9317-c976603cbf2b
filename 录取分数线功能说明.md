# 录取分数线查询功能实现说明

## 功能概述

根据咕咕数据API文档，成功实现了两个主要的录取分数线查询功能：

1. **高考省录取分数线查询** - 基于 `ceeprovince` API
2. **高考高校录取分数线查询** - 基于 `ceecollegeline` API

## 实现的文件结构

```
src/
├── services/
│   └── scoreLineApi.ts              # API服务层，封装两个录取分数线API
├── components/
│   └── score/
│       ├── ScoreLinePage.tsx        # 主页面组件，包含选项卡切换
│       ├── ProvinceScoreLine.tsx    # 省录取分数线查询组件
│       └── CollegeScoreLine.tsx     # 高校录取分数线查询组件
└── App.tsx                          # 添加了新的路由处理
```

## 功能特性

### 1. 省录取分数线查询 (`ProvinceScoreLine.tsx`)

#### 主要功能：
- **省份筛选**：支持全国34个省份选择
- **年份筛选**：支持2014-2025年数据查询
- **科目类型筛选**：支持理科、文科、综合、物理类、历史类等
- **批次显示**：显示本科一批、本科二批、专科批等不同录取批次
- **分数线展示**：清晰显示各批次的录取分数线

#### 查询参数：
```typescript
interface ProvinceScoreLineFilters {
  keyword?: string    // 省份名称，如"安徽"
  year?: number      // 年份，如2024
  category?: string  // 科目类型，如"物理类"
}
```

#### 数据展示：
- 省份、年份、科目类型标签
- 录取批次彩色标签
- 录取分数线突出显示
- 专业说明（如有）

### 2. 高校录取分数线查询 (`CollegeScoreLine.tsx`)

#### 主要功能：
- **双重查询模式**：
  - 按省份查询：查看某省份所有高校的录取情况
  - 按高校查询：查看某高校在各省的录取情况
- **高级筛选**：
  - 年份筛选（2015-2025年）
  - 科目类型筛选
  - 最低分筛选
  - 录取批次筛选
- **分页功能**：支持5/10/15/20条每页的分页浏览
- **学校标识**：显示985/211/双一流等学校类型

#### 查询参数：
```typescript
interface CollegeScoreLineFilters {
  searchtype: 'PROVINCENAME' | 'COLLEGENAME'  // 查询类型
  keyword: string                             // 省份或高校名称
  pageindex?: number                          // 页码
  pagesize?: number                           // 每页条数
  year?: number                              // 年份
  min?: number                               // 最低分筛选
  type?: string                              // 科目类型
  // ... 更多高级筛选参数
}
```

#### 数据展示：
- 高校名称和学校类型标签（985/211/双一流）
- 最高分、平均分、最低分、最低位次
- 省控线、录取批次
- 所在城市、招生类型
- 分页导航

## API接口详情

### 1. 省录取分数线API
- **接口地址**：`https://api.gugudata.com/metadata/ceeprovince`
- **请求方式**：GET
- **代理地址**：`http://localhost:3001/api/gugudata/metadata/ceeprovince`

### 2. 高校录取分数线API
- **接口地址**：`https://api.gugudata.com/metadata/ceecollegeline`
- **请求方式**：GET
- **代理地址**：`http://localhost:3001/api/gugudata/metadata/ceecollegeline`

## 使用方法

### 1. 启动服务
```bash
# 启动代理服务器（如果未启动）
cd server
node proxy-server.js

# 启动前端开发服务器
npm run dev
```

### 2. 访问功能
1. 打开浏览器访问 `http://localhost:5176`
2. 在首页点击"查录取分数"按钮
3. 选择"省录取分数线"或"高校录取分数线"选项卡

### 3. 查询省录取分数线
1. 选择省份（如：安徽）
2. 选择年份（如：2024年）
3. 选择科目类型（如：物理类）
4. 点击"查询"按钮

### 4. 查询高校录取分数线
1. 选择查询类型：
   - "按省份查询"：查看某省所有高校录取情况
   - "按高校查询"：查看某高校在各省录取情况
2. 输入关键词（省份名或高校名）
3. 设置筛选条件（年份、科目类型等）
4. 点击"查询"按钮

## 技术实现

### 1. API服务层 (`scoreLineApi.ts`)
- 封装了两个API的调用逻辑
- 统一的参数构建和错误处理
- TypeScript类型定义确保类型安全
- 提供常用的省份、科目类型、录取批次枚举

### 2. 组件设计
- **模块化设计**：每个功能独立组件，便于维护
- **响应式布局**：适配不同屏幕尺寸
- **用户体验优化**：
  - 加载状态指示
  - 错误提示
  - 空状态处理
  - 分页导航

### 3. 数据展示
- **彩色标签**：不同类型使用不同颜色区分
- **层次化信息**：重要信息突出显示
- **表格布局**：清晰的数据组织结构

## 数据说明

### 1. 数据来源
- 官方教育部门和各省教育考试院
- 咕咕数据平台提供API服务
- 数据覆盖2014-2025年

### 2. 数据特点
- **实时更新**：数据定时自动更新
- **全面覆盖**：全国各省份和高校
- **多维度**：支持多种筛选条件
- **高质量**：经过校对的历史数据

### 3. 使用建议
- 建议结合多年数据进行参考
- 不作为唯一填报依据
- 注意特殊类型招生的单独标准
- 关注录取批次和科目要求

## 测试验证

创建了 `test-score-api.js` 测试文件，可以验证API功能：

```bash
# 在浏览器控制台运行测试
# 或者安装node-fetch后在Node.js中运行
node test-score-api.js
```

测试内容包括：
1. 省录取分数线API测试
2. 按省份查询高校录取分数线
3. 按高校名称查询录取分数线

## 错误处理

实现了完善的错误处理机制：
- API请求失败提示
- 网络错误处理
- 参数验证
- 用户友好的错误信息

## 后续优化建议

1. **数据缓存**：添加本地缓存减少API请求
2. **导出功能**：支持数据导出为Excel或PDF
3. **对比功能**：支持多年数据对比分析
4. **收藏功能**：支持收藏常用查询条件
5. **图表展示**：添加数据可视化图表
