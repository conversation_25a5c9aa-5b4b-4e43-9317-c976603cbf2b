# 大学搜索API集成说明

## 概述

本项目已集成咕咕数据的全国大学高校基础信息API，提供真实的大学数据查询功能。

## API文档

- **API提供商**: 咕咕数据 (GuGuData)
- **接口地址**: https://api.gugudata.com/location/college
- **文档地址**: https://www.gugudata.com/api/details/college

## 配置步骤

### 1. 获取API密钥

1. 访问 [咕咕数据官网](https://www.gugudata.com)
2. 注册账号并登录
3. 购买"全国大学高校基础信息"接口服务
4. 在开发者中心获取APPKEY

### 2. 配置环境变量

1. 复制 `.env.example` 文件为 `.env.local`
2. 将 `YOUR_APPKEY_HERE` 替换为实际的APPKEY

```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填入真实的APPKEY
```

### 3. 重启开发服务器

```bash
npm start
# 或
yarn start
```

## 功能特性

### 搜索功能
- **关键词搜索**: 支持大学名称、地区、旧称等模糊搜索
- **精确匹配**: 可选择精确匹配模式
- **分页查询**: 支持分页浏览，每页最多20条数据

### 筛选功能
- **院校类别**: 理工类、综合类、师范类等13个类别
- **院校性质**: 普通本科、独立学院、专科等
- **院校标识**: 985、211、双一流院校筛选
- **学制筛选**: 本科、专科等学制筛选
- **资质筛选**: 公办、民办、中外合作办学

### 数据展示
- **基本信息**: 院校名称、简称、代码、地址等
- **院校标识**: 985/211/双一流标识
- **排名信息**: 全国排名、类别排名
- **联系方式**: 官网、招生电话、邮箱
- **专业信息**: 开设专业大类和具体专业
- **其他信息**: 校徽、简介、学费等

## API参数说明

### 请求参数
- `keywords`: 搜索关键字（可选）
- `pagesize`: 每页数据量，最大20（默认10）
- `pageindex`: 页码（默认1）
- `keywordstrict`: 是否精确匹配（默认false）
- `collegecategory`: 院校类别筛选
- `collegetype`: 院校性质筛选
- `is985`: 是否985院校
- `is211`: 是否211院校
- `isdualclass`: 是否双一流院校
- `edulevel`: 学制筛选
- `collegeproperty`: 资质筛选

### 响应数据
- `DataStatus`: 接口状态信息
- `Data`: 大学数据数组
- `DataTotalCount`: 总数据量

## 文件结构

```
src/
├── types/university.ts          # 大学数据类型定义
├── services/universityApi.ts   # API服务封装
├── components/university/
│   ├── UniversitySearchPage.tsx    # 主搜索页面
│   ├── SearchFilters.tsx           # 搜索筛选组件
│   └── UniversityCard.tsx          # 大学卡片组件
```

## 注意事项

1. **API限制**: 每页最多返回20条数据
2. **请求频率**: 建议每秒请求不超过5次
3. **错误处理**: 已实现完整的错误处理和重试机制
4. **数据缓存**: API数据实时获取，无本地缓存

## 开发说明

### 添加新的筛选条件

1. 在 `src/types/university.ts` 中更新 `SearchFilters` 接口
2. 在 `src/services/universityApi.ts` 中更新 `buildQueryParams` 函数
3. 在 `src/components/university/SearchFilters.tsx` 中添加UI组件

### 自定义数据展示

修改 `src/components/university/UniversityCard.tsx` 组件来自定义大学信息的展示方式。

## 故障排除

### 常见错误

1. **504 APPKEY错误**: 检查环境变量配置是否正确
2. **503 权限超限**: 检查API套餐是否到期
3. **429 请求频率受限**: 降低请求频率
4. **网络错误**: 检查网络连接

### 调试方法

1. 打开浏览器开发者工具
2. 查看Network标签页中的API请求
3. 检查Console中的错误信息
4. 验证环境变量是否正确加载

## 更新日志

- **v1.0.0**: 初始版本，集成咕咕数据API
- 支持关键词搜索和多维度筛选
- 实现分页和错误处理
- 提供完整的数据展示界面
