# 左侧分类显示修复说明

## 🎯 问题描述

用户反馈左侧的分类只显示了一个"农学"，但实际返回值中包含多个学科门类，如医学、历史学、工学等。

## 🔍 问题分析

通过分析`data/major.json`文件，发现了问题的根本原因：

### API数据结构特点
1. **按需加载机制**：API采用按需加载策略，只有被请求的学科门类才会加载详细的专业数据
2. **分类状态标识**：每个学科门类都有`loaded`字段标识是否已加载详细数据
3. **统计信息完整**：即使未加载详细数据，所有学科门类的基本信息和专业数量统计都存在

### 数据结构示例
```json
{
  "tree": {
    "categories": [
      {
        "id": "农学",
        "name": "农学", 
        "loaded": true,        // 已加载详细数据
        "majorCount": 27,
        "children": [...]      // 包含详细的专业数据
      },
      {
        "id": "医学",
        "name": "医学",
        "loaded": false,       // 未加载详细数据
        "majorCount": 44,
        "children": []         // 空数组
      },
      // ... 其他学科门类
    ]
  }
}
```

## 🔧 修复方案

### 1. 新增数据提取函数

在`src/services/majorTreeApi.ts`中添加了`extractCategoriesFromApiData`函数：

```typescript
export function extractCategoriesFromApiData(apiData: any): {
  categories: string[]
  categoryStats: Array<{category: string, count: number}>
} {
  const categories: string[] = []
  const categoryStats: Array<{category: string, count: number}> = []

  if (apiData && apiData.tree && apiData.tree.categories) {
    for (const category of apiData.tree.categories) {
      categories.push(category.name)
      categoryStats.push({
        category: category.name,
        count: category.majorCount || 0
      })
    }
  }

  return { categories, categoryStats }
}
```

### 2. 优化数据处理逻辑

修改了`flattenMajorTree`函数，明确只处理已加载的分类：

```typescript
for (const category of categories) {
  // 只处理已加载的分类（loaded: true）
  if (category.loaded && category.children) {
    // 处理详细专业数据
  }
}
```

### 3. 更新分类生成逻辑

修改了`MajorSearchPage.tsx`中的`generateDynamicCategories`函数：

- **优先使用API完整信息**：从API数据中提取所有学科门类
- **智能统计合并**：对于有详细数据的分类使用实际统计，其他使用API提供的统计
- **保持向后兼容**：如果没有API数据，回退到原有逻辑

### 4. 增强测试功能

更新了`MajorDataTest.tsx`测试页面：

- **实际数据加载**：尝试从`/data/major.json`加载真实数据
- **分类信息展示**：显示所有学科门类和专业数量统计
- **详细验证**：提供完整的数据转换验证

## 📊 修复效果

### 修复前
- ❌ 左侧只显示"农学"一个分类
- ❌ 其他学科门类信息丢失
- ❌ 用户无法浏览其他学科的专业

### 修复后
- ✅ 显示所有学科门类（医学、历史学、工学、理学等）
- ✅ 每个分类显示正确的专业数量统计
- ✅ 保持已加载分类的详细数据展示
- ✅ 未加载分类显示基本信息和统计

## 🧪 验证方法

1. **访问测试页面**：
   - 打开 http://localhost:5174
   - 点击"专业数据测试"按钮
   - 点击"测试数据转换"查看所有学科门类

2. **查看专业查询页面**：
   - 返回首页，点击"查专业"
   - 查看左侧分类栏是否显示所有学科门类
   - 验证每个分类的专业数量统计

3. **控制台验证**：
   - 打开浏览器开发者工具
   - 查看控制台输出的学科门类信息
   - 验证API数据结构解析是否正确

## 📋 预期显示的学科门类

根据`data/major.json`文件，应该显示以下学科门类：

1. **农学** (27个专业) - 已加载详细数据
2. **医学** (44个专业) - 未加载详细数据
3. **历史学** (1个专业) - 未加载详细数据
4. **哲学** (4个专业) - 未加载详细数据
5. **经济学** (17个专业) - 未加载详细数据
6. **管理学** (46个专业) - 未加载详细数据
7. **工学** (169个专业) - 未加载详细数据
8. **理学** (36个专业) - 未加载详细数据
9. **文学** (76个专业) - 未加载详细数据
10. **法学** (32个专业) - 未加载详细数据
11. **教育学** (10个专业) - 未加载详细数据
12. **艺术学** (33个专业) - 未加载详细数据

## 🚀 后续优化建议

1. **按需加载实现**：点击未加载的学科门类时，动态请求该分类的详细数据
2. **缓存机制**：缓存已加载的学科门类数据，避免重复请求
3. **加载状态**：为未加载的分类添加加载状态指示
4. **错误处理**：完善分类数据加载失败的错误处理

现在左侧分类应该能正确显示所有学科门类了！您可以访问 http://localhost:5174 查看修复效果。
