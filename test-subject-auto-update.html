<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科目选择自动更新测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-case {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-step {
            margin: 8px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <h1>🧪 科目选择自动更新测试指南</h1>
    
    <p class="info">
        请在浏览器中打开 <strong>http://localhost:5176</strong>，进入查位次页面，按照以下测试用例验证科目选择是否正确自动更新。
    </p>

    <div class="test-case">
        <div class="test-title">测试用例 1: 北京 2025年 → 综合</div>
        <div class="test-step">1. 打开查位次页面</div>
        <div class="test-step">2. 选择省份: <strong>北京</strong></div>
        <div class="test-step">3. 选择年份: <strong>2025年</strong></div>
        <div class="test-step expected">4. 期望结果: 科目类型自动变为 "综合"</div>
        <div class="test-step">5. 输入分数: <strong>568</strong></div>
        <div class="test-step">6. 点击查询位次</div>
        <div class="test-step expected">7. 期望结果: 请求参数中 subjectselection 应该是 "综合"</div>
    </div>

    <div class="test-case">
        <div class="test-title">测试用例 2: 安徽 2022年 → 理科</div>
        <div class="test-step">1. 选择省份: <strong>安徽</strong></div>
        <div class="test-step">2. 选择年份: <strong>2022年</strong></div>
        <div class="test-step expected">3. 期望结果: 科目类型选项变为 "理科"、"文科"</div>
        <div class="test-step">4. 选择科目: <strong>理科</strong></div>
        <div class="test-step">5. 输入分数: <strong>500</strong></div>
        <div class="test-step">6. 点击查询位次</div>
        <div class="test-step expected">7. 期望结果: 请求参数中 subjectselection 应该是 "理科"</div>
    </div>

    <div class="test-case">
        <div class="test-title">测试用例 3: 安徽 2025年 → 物理类</div>
        <div class="test-step">1. 选择省份: <strong>安徽</strong></div>
        <div class="test-step">2. 选择年份: <strong>2025年</strong></div>
        <div class="test-step expected">3. 期望结果: 科目类型选项变为 "物理类"、"历史类"</div>
        <div class="test-step">4. 选择科目: <strong>物理类</strong></div>
        <div class="test-step">5. 输入分数: <strong>467</strong></div>
        <div class="test-step">6. 点击查询位次</div>
        <div class="test-step expected">7. 期望结果: 请求参数中 subjectselection 应该是 "物理类"</div>
    </div>

    <div class="test-case">
        <div class="test-title">测试用例 4: 山东 2024年 → 综合</div>
        <div class="test-step">1. 选择省份: <strong>山东</strong></div>
        <div class="test-step">2. 选择年份: <strong>2024年</strong></div>
        <div class="test-step expected">3. 期望结果: 科目类型自动变为 "综合"</div>
        <div class="test-step">4. 输入分数: <strong>520</strong></div>
        <div class="test-step">6. 点击查询位次</div>
        <div class="test-step expected">7. 期望结果: 请求参数中 subjectselection 应该是 "综合"</div>
    </div>

    <div class="test-case">
        <div class="test-title">测试用例 5: 河北 2020年 → 理科</div>
        <div class="test-step">1. 选择省份: <strong>河北</strong></div>
        <div class="test-step">2. 选择年份: <strong>2020年</strong></div>
        <div class="test-step expected">3. 期望结果: 科目类型选项包含 "理科"、"文科"、"体育文"、"体育理"</div>
        <div class="test-step">4. 选择科目: <strong>理科</strong></div>
        <div class="test-step">5. 输入分数: <strong>480</strong></div>
        <div class="test-step">6. 点击查询位次</div>
        <div class="test-step expected">7. 期望结果: 请求参数中 subjectselection 应该是 "理科"</div>
    </div>

    <h2>🔍 如何检查请求参数</h2>
    <div class="test-case">
        <div class="test-step">1. 打开浏览器开发者工具 (F12)</div>
        <div class="test-step">2. 切换到 "Network" (网络) 标签</div>
        <div class="test-step">3. 点击查询位次按钮</div>
        <div class="test-step">4. 查找包含 "score-segment" 的请求</div>
        <div class="test-step">5. 检查请求URL中的参数，特别是 subjectselection 参数</div>
        <div class="test-step expected">6. 确认 subjectselection 参数与当前选择的科目类型一致</div>
    </div>

    <h2>✅ 成功标准</h2>
    <div class="test-case">
        <div class="test-step expected">✓ 当省份或年份改变时，科目类型选项自动更新</div>
        <div class="test-step expected">✓ 如果当前选择的科目不在新的选项中，自动选择第一个可用选项</div>
        <div class="test-step expected">✓ 查询请求中的 subjectselection 参数与界面显示的科目类型一致</div>
        <div class="test-step expected">✓ 控制台中显示科目选择自动更新的日志信息</div>
    </div>

    <div class="test-case">
        <div class="test-title warning">⚠️ 如果测试失败</div>
        <div class="test-step">1. 检查控制台是否有错误信息</div>
        <div class="test-step">2. 确认 data/subjectSelections.json 文件存在且格式正确</div>
        <div class="test-step">3. 检查网络请求中的参数是否正确传递</div>
        <div class="test-step">4. 查看控制台中的科目选择更新日志</div>
    </div>
</body>
</html>
