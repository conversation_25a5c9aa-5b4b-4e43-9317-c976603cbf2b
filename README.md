# 🚀 Modern Web Stack Demo

一个集成了现代化 Web 开发技术栈的演示项目，包含 Vite + React + TypeScript + shadcn/ui + Magic UI。

## ✨ 技术栈

- **⚛️ React 19** - 最新的 React 版本
- **⚡ Vite** - 极速的构建工具
- **📘 TypeScript** - 类型安全的 JavaScript
- **🎨 Tailwind CSS v4** - 原子化 CSS 框架
- **🔮 shadcn/ui** - 高质量的 React 组件库
- **✨ Magic UI** - 美丽的动画组件库
- **🌊 Motion** - 强大的动画库
- **🎯 Lucide React** - 现代化图标库

## 🎯 功能特性

- ✅ 完整的 TypeScript 支持
- ✅ 热重载开发体验
- ✅ 现代化的组件系统
- ✅ 丰富的动画效果
- ✅ 响应式设计
- ✅ 暗色/亮色主题支持
- ✅ 可访问性优化

## 🛠️ 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📦 已集成的组件

### shadcn/ui 组件
- Button - 多种样式的按钮组件

### Magic UI 组件
- Globe - 交互式 3D 地球
- Marquee - 滚动展示组件
- ShimmerButton - 闪光效果按钮
- AnimatedShinyText - 动画文字效果
- DotPattern - 点阵背景
- NumberTicker - 数字动画

### Lucide React 图标
- 1000+ 精美图标
- 完美的 TypeScript 支持
- 树摇优化，按需加载
- 现代化设计风格

## 🎨 添加更多组件

### 添加 shadcn/ui 组件
```bash
npx shadcn@latest add [component-name]
```

### 添加 Magic UI 组件
```bash
npx shadcn@latest add https://magicui.design/r/[component-name]
```

### 使用 Lucide 图标
```tsx
import { Heart, Star, Settings } from "lucide-react"

<Heart className="w-6 h-6 text-red-500" />
```

## 📁 项目结构

```
src/
├── components/
│   ├── ui/              # shadcn/ui 组件
│   ├── magicui/         # Magic UI 组件
│   └── demo-showcase.tsx # 演示组件
├── lib/
│   └── utils.ts         # 工具函数
├── App.tsx              # 主应用组件
└── main.tsx             # 应用入口
```

## 🌟 演示功能

访问 `http://localhost:5173` 查看完整的演示，包括：

1. **动画文字标题** - 使用 AnimatedShinyText 和 Lucide 图标
2. **实时数据展示** - 使用 NumberTicker 和图标装饰
3. **交互式地球** - 使用 Globe 组件，支持切换模式
4. **按钮展示** - shadcn/ui 和 Magic UI 按钮，配合 Lucide 图标
5. **技术栈滚动** - 使用 Marquee 组件展示技术栈图标
6. **背景动画** - 使用 DotPattern 创建动态背景
7. **图标展示页面** - 点击右上角按钮查看 1000+ Lucide 图标

## 📚 相关文档

- [Vite 文档](https://vite.dev/)
- [React 文档](https://react.dev/)
- [shadcn/ui 文档](https://ui.shadcn.com/)
- [Magic UI 文档](https://magicui.design/)
- [Tailwind CSS 文档](https://tailwindcss.com/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
