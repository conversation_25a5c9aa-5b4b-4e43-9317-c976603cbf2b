# 科目选择年份与省份对应关系修复

## 问题描述

原项目中查位次页面的年份与选科的对应关系存在两个问题：

### 问题1: 科目选择显示不正确
- 2025年安徽的科目类型应该是"物理类"和"历史类"
- 2022年安徽的科目类型应该是"理科"和"文科"
- 但系统没有正确根据省份和年份动态显示对应的科目类型

### 问题2: 请求参数与界面不一致
- 用户选择"北京 2025年"时，界面显示"综合"
- 但实际请求参数中 subjectselection 仍然是初始值"物理类"
- 导致查询结果不准确

## 解决方案

### 1. 创建科目选择工具函数

**文件**: `src/utils/subjectSelectionUtils.ts`

- 从 `data/subjectSelections.json` 读取真实的科目选择数据
- 提供 `getSubjectSelectionsByProvinceAndYear()` 函数根据省份和年份获取准确的科目类型
- 提供后备方案，当数据不存在时根据年份推测科目类型

### 2. 更新API服务

**文件**: `src/services/oneScoreOneSectionApi.ts`

- 修改 `getAvailableSubjectSelections()` 函数，使用新的工具函数
- 确保根据省份和年份动态获取科目类型
- 更新 `getAvailableProvinces()` 函数，优先从数据文件获取省份列表

### 3. 更新其他相关服务

**文件**: `src/services/scoreLineApi.ts` 和 `src/services/scoreLineApiSimple.ts`

- 添加对动态科目选择函数的引用
- 保留原有常量作为后备方案，但推荐使用动态函数

### 4. 更新组件

**文件**: `src/components/ranking/OneScoreOneSectionPage.tsx`

- 修改初始状态，动态设置初始科目选择
- 添加 useEffect 监听省份和年份变化，自动更新科目选择
- 确保请求参数与界面显示一致

**文件**: `src/components/score/ProvinceScoreLine.tsx` 和 `src/components/score/CollegeScoreLine.tsx`

- 使用 `getAvailableSubjectSelections()` 函数动态获取科目类型
- 根据当前选择的省份和年份显示正确的科目选项

## 数据映射示例

根据 `data/subjectSelections.json` 中的数据：

### 安徽省
- 2016-2023年: 理科、文科
- 2024-2025年: 物理类、历史类

### 北京市
- 2016-2019年: 理科、文科
- 2020-2025年: 综合

### 河北省
- 2016-2020年: 理科、文科（2020年还有体育文、体育理）
- 2021-2025年: 物理类、历史类

### 山东省
- 2016-2019年: 理科、文科
- 2020-2025年: 综合

## 技术实现

### 核心函数

```typescript
export function getSubjectSelectionsByProvinceAndYear(
  provinceName: string, 
  year: string
): string[] {
  // 从 data/subjectSelections.json 获取准确数据
  // 如果数据不存在，使用基于年份的后备方案
}
```

### 后备方案

```typescript
function getSubjectSelectionsByYearFallback(year: string): string[] {
  const yearNum = parseInt(year)
  
  if (yearNum >= 2024) {
    return ['物理类', '历史类', '综合']
  } else if (yearNum >= 2021) {
    return ['物理类', '历史类', '理科', '文科', '综合']
  } else {
    return ['理科', '文科']
  }
}
```

## 修改的文件列表

1. **新增文件**:
   - `src/utils/subjectSelectionUtils.ts` - 科目选择工具函数

2. **修改文件**:
   - `src/components/ranking/OneScoreOneSectionPage.tsx` - 修复科目选择自动更新逻辑
   - `src/services/oneScoreOneSectionApi.ts` - 更新科目选择获取逻辑
   - `src/services/scoreLineApi.ts` - 添加动态函数引用
   - `src/services/scoreLineApiSimple.ts` - 添加动态函数引用
   - `src/components/score/ProvinceScoreLine.tsx` - 使用动态科目选择
   - `src/components/score/CollegeScoreLine.tsx` - 使用动态科目选择

3. **文档文件**:
   - `SUBJECT_SELECTION_FIX.md` - 详细的修改说明文档
   - `test-subject-selections.js` - 测试脚本
   - `test-subject-auto-update.html` - 界面测试指南

## 核心修复: 科目选择自动更新

### 问题根因
原代码中 `OneScoreOneSectionPage` 组件的初始状态硬编码为 `subjectselection: '物理类'`，当用户选择不同省份和年份时，虽然下拉框选项会更新，但如果用户没有重新选择科目，系统仍使用初始值。

### 修复方案
1. **动态初始化**: 根据初始省份和年份动态设置科目选择
2. **自动更新**: 添加 useEffect 监听省份和年份变化，自动更新科目选择
3. **智能选择**: 当当前科目不在新选项中时，自动选择第一个可用选项

### 关键代码修改

```typescript
// 动态初始化
const [filters, setFilters] = useState<OneScoreOneSectionFilters>(() => {
  const initialSubjects = getAvailableSubjectSelections('安徽', '2024')
  return {
    year: '2024',
    provincename: '安徽',
    subjectselection: initialSubjects[0] || '物理类',
    // ...其他字段
  }
})

// 自动更新监听
useEffect(() => {
  const currentSubjects = getAvailableSubjectSelections(filters.provincename, filters.year)

  if (currentSubjects.length > 0 && !currentSubjects.includes(filters.subjectselection)) {
    console.log(`🔄 省份/年份变化，自动更新科目选择: ${filters.subjectselection} → ${currentSubjects[0]}`)
    setFilters(prev => ({
      ...prev,
      subjectselection: currentSubjects[0]
    }))
  }
}, [filters.provincename, filters.year, filters.subjectselection])
```

## 测试验证

### 自动化测试
使用 `test-subject-auto-update.html` 进行完整的界面测试：

1. 打开 `http://localhost:5176` 进入查位次页面
2. 按照测试指南逐一验证各个测试用例
3. 检查网络请求中的 `subjectselection` 参数是否正确

### 关键测试用例

**测试用例 1: 北京 2025年 → 综合**
- 选择"北京" + "2025年"
- 期望: 科目自动变为"综合"
- 输入分数568，查询
- 验证: 请求参数 `subjectselection=综合`

**测试用例 2: 安徽 2022年 → 理科**
- 选择"安徽" + "2022年"
- 期望: 科目选项变为"理科"、"文科"
- 验证: 请求参数正确传递

**测试用例 3: 省份年份切换**
- 从"安徽 2022年"切换到"北京 2025年"
- 期望: 科目自动从"理科"变为"综合"
- 验证: 控制台显示自动更新日志

### 验证方法
1. **界面验证**: 观察科目类型下拉框选项是否正确
2. **网络验证**: 检查请求参数中的 `subjectselection` 值
3. **日志验证**: 查看控制台中的科目选择更新日志

## 兼容性

- 保持向后兼容，原有的硬编码常量仍然存在
- 新的动态函数优先使用真实数据，数据不存在时使用智能后备方案
- 不会影响现有功能的正常使用

## 未来扩展

- 可以轻松添加新的省份和年份数据
- 支持更复杂的科目类型（如艺术类、体育类等）
- 可以根据需要添加更多的数据验证和错误处理
