

## 分组查询招生计划数据


**接口地址**:`/admin-api/system/college-enrollment-plan/group-query`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|pageNo|页码|query|true|integer(int32)||
|pageSize|每页数据量，取值范围在 10 ~ 100 之间（含）|query|true|integer(int32)||
|groupType|分组类型：school-按学校分组，major-按专业分组|query|true|string||
|collegeMajorName|查询的高校专业名称，支持模糊查询|query|false|string||
|schoolName|查询的高校名称，支持模糊查询|query|false|string||
|provinceName|查询的招生省份|query|false|string||
|classOne|查询的专业大类|query|false|string||
|classTwo|查询的专业小类|query|false|string||
|batchName|录取批次参数|query|false|string||
|type|文理综合类别|query|false|string||
|schoolUuid|咕咕数据平台高校唯一 ID|query|false|string||
|year|查询的招生年份，如 2020、2021、2022、2023、2024。参数默认值为 0：即获取所有年份的招生计划数据|query|false|integer(int32)||
|tenant-id|租户编号|header|false|integer(int32)||
|Authorization|认证 Token|header|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|CommonResultCollegeEnrollmentPlanGroupRespVO|


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code||integer(int32)|integer(int32)|
|data||CollegeEnrollmentPlanGroupRespVO|CollegeEnrollmentPlanGroupRespVO|
|&emsp;&emsp;groupType|分组类型：school-按学校分组，major-按专业分组|string||
|&emsp;&emsp;schoolGroups||PageResultSchoolGroupInfo|PageResultSchoolGroupInfo|
|&emsp;&emsp;&emsp;&emsp;list|数据|array|SchoolGroupInfo|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolUuid|学校唯一ID|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolName|学校名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;totalEnrollmentNumbers|近三年招生计划总人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;majorCount|近三年招生专业数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2024|2024年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2023|2023年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2022|2022年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;majorCount2024|2024年招生专业数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;majorCount2023|2023年招生专业数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;majorCount2022|2022年招生专业数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;province|学校所在省份|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;city|学校所在城市|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;district|学校所在区县|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;collegeType|学校性质|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;is985|是否为985院校|boolean||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;is211|是否为211院校|boolean||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isDualClass|是否为双一流院校|boolean||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;collegeCategory|学校类别|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;collegeTags|学校标签|array|string|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;eduLevel|学校学制|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;collegeProperty|学校资质|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;collegeCode|学校编号|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;ranking|全国排名|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;rankingInCategory|学校所在类别下排名|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;website|学校官网|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;callNumber|学校招生电话|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;email|学校招生邮箱|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;address|学校地址|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;coverImage|学校校徽图片URL|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;intro|学校简介|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;expenses|学校收费情况|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;oldName|学校旧称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;shortName|学校简称|string||
|&emsp;&emsp;&emsp;&emsp;total|总量|integer(int64)||
|&emsp;&emsp;majorGroups||PageResultMajorGroupInfo|PageResultMajorGroupInfo|
|&emsp;&emsp;&emsp;&emsp;list|数据|array|MajorGroupInfo|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;majorName|专业名称（去除括号内容）|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;classOne|专业大类|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;classTwo|专业小类|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;totalEnrollmentNumbers|近三年招生计划总人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolCount|近三年招生院校数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2024|2024年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2023|2023年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enrollmentNumbers2022|2022年招生计划人数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolCount2024|2024年招生院校数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolCount2023|2023年招生院校数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;schoolCount2022|2022年招生院校数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;total|总量|integer(int64)||
|msg||string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"groupType": "school",
		"schoolGroups": {
			"list": [
				{
					"schoolUuid": "c24a67f87405b82bec08a5638c32f282",
					"schoolName": "北京大学",
					"totalEnrollmentNumbers": 1500,
					"majorCount": 85,
					"enrollmentNumbers2024": 500,
					"enrollmentNumbers2023": 500,
					"enrollmentNumbers2022": 500,
					"majorCount2024": 30,
					"majorCount2023": 28,
					"majorCount2022": 27,
					"province": "北京",
					"city": "北京市",
					"district": "海淀区",
					"collegeType": "公办",
					"is985": true,
					"is211": true,
					"isDualClass": true,
					"collegeCategory": "综合类",
					"collegeTags": "[\"985\",\"211\",\"双一流\"]",
					"eduLevel": "本科",
					"collegeProperty": "公办",
					"collegeCode": "10001",
					"ranking": 1,
					"rankingInCategory": "综合类第1名",
					"website": "https://www.pku.edu.cn",
					"callNumber": "010-62751407",
					"email": "<EMAIL>",
					"address": "北京市海淀区颐和园路5号",
					"coverImage": "",
					"intro": "",
					"expenses": "",
					"oldName": "京师大学堂",
					"shortName": "北大"
				}
			],
			"total": 0
		},
		"majorGroups": {
			"list": [
				{
					"majorName": "计算机科学与技术",
					"classOne": "工学",
					"classTwo": "计算机类",
					"totalEnrollmentNumbers": 2500,
					"schoolCount": 120,
					"enrollmentNumbers2024": 850,
					"enrollmentNumbers2023": 825,
					"enrollmentNumbers2022": 825,
					"schoolCount2024": 42,
					"schoolCount2023": 40,
					"schoolCount2022": 38
				}
			],
			"total": 0
		}
	},
	"msg": ""
}
```