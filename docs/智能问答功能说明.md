# 智能问答功能说明

## 功能概述

智能问答功能是基于百度AppBuilder AI API开发的高考志愿填报智能助手，可以为用户提供专业的高考相关问题解答。

## 主要特性

### 1. 专业的问答能力
- 高考志愿填报指导
- 大学专业介绍和分析
- 院校信息查询
- 录取分数线解读
- 就业前景分析

### 2. 智能对话体验
- **流式响应**：实时显示AI生成过程，无需等待
- **上下文理解**：支持多轮对话，记住对话历史
- **个性化回答**：根据用户问题提供针对性建议
- **友好界面**：现代化设计，响应式布局

### 3. 便捷的操作
- **快捷问题模板**：预设常见问题，一键发送
- **流式演示**：体验AI实时生成回答的过程
- **对话管理**：支持清空对话，重新开始
- **配置测试**：一键测试API配置状态
- **响应式设计**：适配各种设备屏幕

## 技术实现

### API集成
- **百度AppBuilder**：使用百度AppBuilder的对话接口
- **流式响应**：支持Server-Sent Events格式的实时数据流
- **自动对话管理**：自动创建和管理对话会话
- **错误处理**：完善的错误处理和用户友好提示
- **代理服务**：通过代理服务器解决CORS跨域问题

### 前端组件
- `AiChatPage`: 主要聊天界面组件
- `aiService`: API服务封装
- 响应式UI设计
- 实时消息更新

## 配置说明

### 环境变量配置
1. 复制 `.env.example` 为 `.env`
2. 填入您的百度AppBuilder API密钥和应用ID：
   ```
   VITE_BAIDU_API_KEY=your_actual_api_key_here
   VITE_BAIDU_APP_ID=your_actual_app_id_here
   ```

### API密钥和应用ID获取
1. 访问百度智能云AppBuilder控制台
2. 创建应用并获取应用ID
3. 获取API密钥
4. 将密钥和应用ID配置到环境变量中

## 使用方法

### 基本使用
1. 在导航页面点击"智能问答"
2. 在输入框中输入问题
3. 点击发送或按Enter键
4. 查看AI助手的回答

### 快捷问题
- 点击预设的快捷问题按钮
- 系统会自动填入问题到输入框
- 可以直接发送或进行修改

### 对话管理
- 支持多轮对话
- 自动保存对话历史
- 可以一键清空对话重新开始

## 常见问题

### Q: API密钥配置错误怎么办？
A: 检查.env文件中的VITE_BAIDU_API_KEY和VITE_BAIDU_APP_ID是否正确配置，确保密钥和应用ID有效。

### Q: 为什么AI回答很慢？
A: 可能是网络问题或API服务繁忙，请稍后重试。

### Q: 如何获得更准确的回答？
A: 尽量提供详细、具体的问题描述，避免过于宽泛的问题。

### Q: 支持哪些类型的问题？
A: 主要支持高考志愿填报、大学专业、院校信息、就业前景等相关问题。

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在代码中硬编码或公开分享
2. **使用限制**: 注意API的调用频率限制，避免过于频繁的请求
3. **网络要求**: 需要稳定的网络连接才能正常使用
4. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 更新日志

### v2.0.0 (2024-01-XX)
- 迁移到百度AppBuilder AI
- 支持流式响应
- 改进对话管理
- 优化用户体验

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础聊天功能
- 响应式UI设计
- 快捷问题功能

## 技术支持

如遇到问题，请：
1. 检查网络连接
2. 验证API密钥配置
3. 查看浏览器控制台错误信息
4. 联系技术支持团队
