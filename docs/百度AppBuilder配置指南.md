# 百度AppBuilder配置指南

## 概述

本指南将帮助您配置百度AppBuilder AI服务，以便在智能问答功能中使用。

## 前置条件

1. 拥有百度智能云账号
2. 开通AppBuilder服务
3. 创建应用并获取相关凭证

## 配置步骤

### 1. 注册百度智能云账号

1. 访问 [百度智能云官网](https://cloud.baidu.com/)
2. 点击"注册"按钮
3. 按照提示完成账号注册和实名认证

### 2. 开通AppBuilder服务

1. 登录百度智能云控制台
2. 搜索并进入"AppBuilder"服务
3. 点击"立即开通"
4. 根据需要选择合适的套餐

### 3. 创建应用

1. 在AppBuilder控制台中，点击"创建应用"
2. 填写应用信息：
   - 应用名称：高考志愿填报助手
   - 应用描述：专业的高考志愿填报智能问答助手
   - 应用类型：选择合适的类型
3. 配置应用能力和知识库（可选）
4. 完成创建并记录应用ID

### 4. 获取API密钥

1. 在应用详情页面，找到"API调用"或"密钥管理"
2. 生成或查看API密钥
3. 妥善保存API密钥

### 5. 配置环境变量

1. 在项目根目录复制 `.env.example` 为 `.env`
2. 编辑 `.env` 文件，填入获取的凭证：

```env
# 百度AppBuilder AI API 配置
VITE_BAIDU_API_KEY=your_actual_api_key_here
VITE_BAIDU_APP_ID=your_actual_app_id_here

# 其他配置
VITE_APP_NAME=高考志愿填报系统
VITE_APP_VERSION=2.0.0
```

## API接口说明

### 1. 新建对话接口

- **接口地址**: `POST /v2/app/conversation`
- **功能**: 创建新的对话会话
- **请求参数**:
  ```json
  {
    "app_id": "your_app_id"
  }
  ```
- **响应参数**:
  ```json
  {
    "request_id": "request_id",
    "conversation_id": "conversation_id"
  }
  ```

### 2. 对话接口

- **接口地址**: `POST /v2/app/conversation/runs`
- **功能**: 发送消息并获取AI回复
- **请求参数**:
  ```json
  {
    "app_id": "your_app_id",
    "query": "用户问题",
    "stream": true,
    "conversation_id": "conversation_id"
  }
  ```
- **响应**: 支持流式和非流式响应

### 3. 文件上传接口（可选）

- **接口地址**: `POST /v2/app/conversation/file/upload`
- **功能**: 上传文件用于对话
- **支持格式**: 文档、图片等

## 注意事项

### 安全性
1. **API密钥保护**: 不要在代码中硬编码API密钥
2. **环境变量**: 使用环境变量管理敏感信息
3. **访问控制**: 合理设置API访问权限

### 使用限制
1. **调用频率**: 注意API的调用频率限制
2. **并发限制**: 避免过多并发请求
3. **数据量**: 注意单次请求的数据量限制

### 成本控制
1. **按量计费**: 了解计费规则，合理使用
2. **缓存策略**: 实施合适的缓存策略
3. **监控使用**: 定期监控API使用情况

## 故障排除

### 常见错误

#### 1. 401 Unauthorized
- **原因**: API密钥无效或过期
- **解决**: 检查API密钥是否正确配置

#### 2. 403 Forbidden
- **原因**: 应用ID无效或权限不足
- **解决**: 检查应用ID和权限设置

#### 3. 429 Too Many Requests
- **原因**: 请求频率过高
- **解决**: 降低请求频率或升级套餐

#### 4. 500 Internal Server Error
- **原因**: 服务器内部错误
- **解决**: 稍后重试或联系技术支持

### 调试技巧

1. **日志记录**: 启用详细的日志记录
2. **网络检查**: 确保网络连接正常
3. **参数验证**: 检查请求参数格式
4. **响应分析**: 分析API响应内容

## 最佳实践

### 1. 错误处理
```typescript
try {
  const response = await aiService.askQuestion(question)
  // 处理成功响应
} catch (error) {
  // 处理错误情况
  console.error('API调用失败:', error)
}
```

### 2. 重试机制
```typescript
const maxRetries = 3
let retryCount = 0

while (retryCount < maxRetries) {
  try {
    const result = await apiCall()
    return result
  } catch (error) {
    retryCount++
    if (retryCount >= maxRetries) throw error
    await delay(1000 * retryCount) // 指数退避
  }
}
```

### 3. 缓存策略
- 缓存常见问题的答案
- 实施会话级缓存
- 合理设置缓存过期时间

## 技术支持

如遇到问题，可以通过以下方式获取帮助：

1. **官方文档**: [百度AppBuilder文档](https://cloud.baidu.com/doc/AppBuilder/)
2. **技术论坛**: 百度智能云开发者社区
3. **工单系统**: 通过控制台提交技术工单
4. **客服热线**: 联系百度智能云客服

## 更新日志

### 2024-01-XX
- 初始版本发布
- 基础配置指南
- API接口说明
- 故障排除指南
