// 位次查询模拟数据

// 本地类型定义（避免循环导入）
interface RankingDataPoint {
  score: number
  ranking: number
  sameScoreCount: number
}

interface ControlLine {
  type: '一本' | '二本' | '专科'
  score: number
  name: string
}

interface ProvinceRankingData {
  province: string
  years: {
    [year: number]: {
      文科: RankingDataPoint[]
      理科: RankingDataPoint[]
      综合?: RankingDataPoint[]
    }
  }
  controlLines: {
    [year: number]: {
      文科: ControlLine[]
      理科: ControlLine[]
      综合?: ControlLine[]
    }
  }
}

// 生成模拟的位次数据点
function generateRankingData(startScore: number, endScore: number, baseRanking: number): RankingDataPoint[] {
  const data: RankingDataPoint[] = []
  
  for (let score = startScore; score >= endScore; score--) {
    // 模拟位次计算：分数越高，位次越小
    const ranking = Math.floor(baseRanking + (startScore - score) * 100 + Math.random() * 50)
    const sameScoreCount = Math.floor(Math.random() * 50) + 1
    
    data.push({
      score,
      ranking,
      sameScoreCount
    })
  }
  
  return data
}

// 省份列表
export const provinces = [
  '安徽', '北京', '重庆', '福建', '甘肃', '广东', '广西', '贵州', 
  '海南', '河北', '河南', '黑龙江', '湖北', '湖南', '吉林', '江苏',
  '江西', '辽宁', '内蒙古', '宁夏', '青海', '山东', '山西', '陕西',
  '上海', '四川', '天津', '西藏', '新疆', '云南', '浙江'
]

// 年份列表
export const availableYears = [2025, 2024, 2023, 2022, 2021, 2020]

// 科类列表
export const categories = ['文科', '理科', '综合'] as const

// 模拟安徽省的位次数据
export const mockAnhuiRankingData: ProvinceRankingData = {
  province: '安徽',
  years: {
    2025: {
      文科: generateRankingData(650, 300, 1000),
      理科: generateRankingData(700, 350, 500),
      综合: generateRankingData(680, 320, 800)
    },
    2024: {
      文科: generateRankingData(645, 295, 1100),
      理科: generateRankingData(695, 345, 600),
      综合: generateRankingData(675, 315, 900)
    },
    2023: {
      文科: generateRankingData(640, 290, 1200),
      理科: generateRankingData(690, 340, 700),
      综合: generateRankingData(670, 310, 1000)
    }
  },
  controlLines: {
    2025: {
      文科: [
        { type: '一本', score: 495, name: '本科一批' },
        { type: '二本', score: 440, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      理科: [
        { type: '一本', score: 482, name: '本科一批' },
        { type: '二本', score: 427, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      综合: [
        { type: '一本', score: 488, name: '本科一批' },
        { type: '二本', score: 433, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ]
    },
    2024: {
      文科: [
        { type: '一本', score: 490, name: '本科一批' },
        { type: '二本', score: 435, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      理科: [
        { type: '一本', score: 477, name: '本科一批' },
        { type: '二本', score: 422, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      综合: [
        { type: '一本', score: 483, name: '本科一批' },
        { type: '二本', score: 428, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ]
    },
    2023: {
      文科: [
        { type: '一本', score: 485, name: '本科一批' },
        { type: '二本', score: 430, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      理科: [
        { type: '一本', score: 472, name: '本科一批' },
        { type: '二本', score: 417, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ],
      综合: [
        { type: '一本', score: 478, name: '本科一批' },
        { type: '二本', score: 423, name: '本科二批' },
        { type: '专科', score: 200, name: '高职专科' }
      ]
    }
  }
}

// 所有省份的模拟数据（简化版）
export const mockRankingData: { [province: string]: ProvinceRankingData } = {
  '安徽': mockAnhuiRankingData,
  // 其他省份可以类似生成，这里为了简化只提供安徽的详细数据
}

// 根据分数查询位次的函数
export function findRankingByScore(
  province: string,
  year: number,
  category: '文科' | '理科' | '综合',
  score: number
): RankingDataPoint | null {
  const provinceData = mockRankingData[province]
  if (!provinceData || !provinceData.years[year] || !provinceData.years[year][category]) {
    return null
  }

  const data = provinceData.years[year][category]
  const exactMatch = data.find(item => item.score === score)
  
  if (exactMatch) {
    return exactMatch
  }

  // 如果没有精确匹配，找到最接近的分数
  const sortedData = data.sort((a, b) => Math.abs(a.score - score) - Math.abs(b.score - score))
  return sortedData[0] || null
}

// 获取省控线信息
export function getControlLines(province: string, year: number, category: '文科' | '理科' | '综合'): ControlLine[] {
  const provinceData = mockRankingData[province]
  if (!provinceData || !provinceData.controlLines[year] || !provinceData.controlLines[year][category]) {
    return []
  }

  return provinceData.controlLines[year][category]
}

// 判断分数对应的批次
export function getScoreBatch(score: number, controlLines: ControlLine[]): '一本' | '二本' | '专科' {
  const sortedLines = controlLines.sort((a, b) => b.score - a.score)
  
  for (const line of sortedLines) {
    if (score >= line.score) {
      return line.type
    }
  }
  
  return '专科'
}
