// 专业数据

// 临时类型定义
type DisciplineCategory =
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'

// 教育层次类型
type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

export interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: DisciplineCategory
  subCategory?: string
  degree: string
  duration: number
  educationLevel: EducationLevel  // 新增教育层次字段
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  courses?: {
    core?: string[]
    elective?: string[]
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
}

// 模拟专业数据
export const mockMajors: Major[] = [
  // 临床医学
  {
    id: '1',
    code: '100201K',
    name: '临床医学',
    englishName: 'Clinical Medicine',
    category: '医学',
    subCategory: '临床医学类',
    degree: '学士',
    duration: 5,
    educationLevel: '本科(普通)',
    description: '临床医学专业是一门实践性很强的应用科学专业，致力于培养具备基础医学、临床医学的基本理论和医疗预防的基本技能。',
    employment: {
      rate: 95.2,
      avgSalary: 130000,
      salaryRange: { min: 80000, max: 200000 },
      topIndustries: ['医疗卫生', '制药', '医疗器械'],
      topPositions: ['医师', '医学研究员', '医疗顾问']
    },
    genderRatio: { male: 44.56, female: 55.44 },
    courses: {
      core: ['人体解剖学', '生理学', '病理学', '药理学', '诊断学', '内科学', '外科学'],
      elective: ['医学影像学', '急救医学', '康复医学']
    },
    features: ['实践性强', '就业前景好', '社会地位高'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'high'
    }
  },
  
  // 法学
  {
    id: '2',
    code: '030101K',
    name: '法学',
    englishName: 'Law',
    category: '法学',
    subCategory: '法学类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '法学专业培养系统掌握法学知识，熟悉我国法律和党的相关政策，能在国家机关、企事业单位和社会团体从事法律工作的高级专门人才。',
    employment: {
      rate: 87.63,
      avgSalary: 120000,
      salaryRange: { min: 60000, max: 300000 },
      topIndustries: ['法律服务', '政府机关', '金融'],
      topPositions: ['律师', '法官', '检察官', '法务专员']
    },
    genderRatio: { male: 37.63, female: 62.37 },
    courses: {
      core: ['法理学', '宪法学', '民法学', '刑法学', '行政法学', '经济法学', '国际法学'],
      elective: ['知识产权法', '环境法', '劳动法']
    },
    features: ['理论性强', '应用面广', '职业发展多样'],
    prospects: {
      trend: 'stable',
      demandLevel: 'medium',
      competitionLevel: 'high'
    }
  },

  // 口腔医学
  {
    id: '3',
    code: '100301K',
    name: '口腔医学',
    englishName: 'Stomatology',
    category: '医学',
    subCategory: '口腔医学类',
    degree: '学士',
    duration: 5,
    educationLevel: '本科(普通)',
    description: '口腔医学专业学生主要学习口腔医学的基本理论和基本知识，受到口腔及颌面部疾病的诊断、治疗、预防方面的训练。',
    employment: {
      rate: 94.66,
      avgSalary: 130000,
      salaryRange: { min: 90000, max: 250000 },
      topIndustries: ['医疗卫生', '口腔诊所', '医疗美容'],
      topPositions: ['口腔医师', '口腔科医生', '口腔修复师']
    },
    genderRatio: { male: 34.66, female: 65.34 },
    courses: {
      core: ['口腔解剖生理学', '口腔组织病理学', '口腔内科学', '口腔外科学', '口腔修复学', '口腔正畸学'],
      elective: ['口腔预防医学', '口腔材料学', '口腔影像学']
    },
    features: ['专业性强', '收入稳定', '工作环境好'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 计算机科学与技术
  {
    id: '4',
    code: '080901',
    name: '计算机科学与技术',
    englishName: 'Computer Science and Technology',
    category: '工学',
    subCategory: '计算机类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '计算机科学与技术专业培养具有良好的科学素养，系统地、较好地掌握计算机科学与技术包括计算机硬件、软件与应用的基本理论、基本知识和基本技能与方法。',
    employment: {
      rate: 96.18,
      avgSalary: 142000,
      salaryRange: { min: 80000, max: 400000 },
      topIndustries: ['互联网', '软件开发', '人工智能', '金融科技'],
      topPositions: ['软件工程师', '算法工程师', '系统架构师', '产品经理']
    },
    genderRatio: { male: 66.18, female: 33.82 },
    courses: {
      core: ['程序设计基础', '数据结构', '计算机组成原理', '操作系统', '数据库系统', '计算机网络', '软件工程'],
      elective: ['人工智能', '机器学习', '网络安全', '移动开发']
    },
    features: ['就业面广', '薪资水平高', '技术更新快'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 电气工程及其自动化
  {
    id: '5',
    code: '080601',
    name: '电气工程及其自动化',
    englishName: 'Electrical Engineering and Automation',
    category: '工学',
    subCategory: '电气类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '电气工程及其自动化专业培养能够从事与电气工程有关的系统运行、自动控制、电力电子技术、信息处理、试验分析、研制开发等领域工作的宽口径"复合型"高级工程技术人才。',
    employment: {
      rate: 92.18,
      avgSalary: 113000,
      salaryRange: { min: 70000, max: 200000 },
      topIndustries: ['电力', '制造业', '新能源', '自动化'],
      topPositions: ['电气工程师', '自动化工程师', '电力系统工程师']
    },
    genderRatio: { male: 82.18, female: 17.82 },
    courses: {
      core: ['电路理论', '电子技术', '电机学', '电力系统分析', '自动控制理论', '电力电子技术'],
      elective: ['新能源技术', '智能电网', '电气传动']
    },
    features: ['就业稳定', '行业需求大', '技术含量高'],
    prospects: {
      trend: 'stable',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 心理学
  {
    id: '6',
    code: '071101',
    name: '心理学',
    englishName: 'Psychology',
    category: '理学',
    subCategory: '心理学类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '心理学专业培养具备心理学的基本理论、基本知识、基本技能，能在科研部门、高等和中等学校、企事业单位等从事心理学科学研究、教学工作和管理工作的高级专门人才。',
    employment: {
      rate: 89.78,
      avgSalary: 118000,
      salaryRange: { min: 50000, max: 200000 },
      topIndustries: ['教育', '医疗卫生', '人力资源', '咨询服务'],
      topPositions: ['心理咨询师', '人力资源专员', '教师', '心理治疗师']
    },
    genderRatio: { male: 22.78, female: 77.22 },
    courses: {
      core: ['普通心理学', '发展心理学', '社会心理学', '认知心理学', '心理统计学', '心理测量学'],
      elective: ['临床心理学', '教育心理学', '管理心理学']
    },
    features: ['应用面广', '助人性质', '理论与实践并重'],
    prospects: {
      trend: 'rising',
      demandLevel: 'medium',
      competitionLevel: 'medium'
    }
  },

  // 人工智能
  {
    id: '7',
    code: '080717T',
    name: '人工智能',
    englishName: 'Artificial Intelligence',
    category: '工学',
    subCategory: '计算机类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '人工智能专业是中国高校人才培养计划设立的专业，旨在培养中国人工智能产业的应用型人才，推动人工智能一级学科建设。',
    employment: {
      rate: 97.30,
      avgSalary: 160000,
      salaryRange: { min: 100000, max: 500000 },
      topIndustries: ['人工智能', '互联网', '金融科技', '自动驾驶'],
      topPositions: ['AI工程师', '机器学习工程师', '数据科学家', '算法研究员']
    },
    genderRatio: { male: 70.30, female: 29.70 },
    courses: {
      core: ['人工智能导论', '机器学习', '深度学习', '计算机视觉', '自然语言处理', '数据挖掘'],
      elective: ['强化学习', '知识图谱', '语音识别']
    },
    features: ['前沿技术', '高薪就业', '发展前景好'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'high'
    }
  },

  // 汉语言文学
  {
    id: '8',
    code: '050101',
    name: '汉语言文学',
    englishName: 'Chinese Language and Literature',
    category: '文学',
    subCategory: '中国语言文学类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '汉语言文学专业培养具备文艺理论素养和系统的汉语言文学知识，能在新闻文艺出版部门、高校、科研机构和机关企事业单位从事文学评论、汉语言文学教学与研究工作。',
    employment: {
      rate: 84.84,
      avgSalary: 109000,
      salaryRange: { min: 40000, max: 180000 },
      topIndustries: ['教育', '新闻出版', '文化传媒', '政府机关'],
      topPositions: ['教师', '编辑', '记者', '文案策划']
    },
    genderRatio: { male: 16.84, female: 83.16 },
    courses: {
      core: ['现代汉语', '古代汉语', '中国古代文学', '中国现代文学', '外国文学', '文学理论'],
      elective: ['比较文学', '民间文学', '影视文学']
    },
    features: ['文化底蕴深厚', '培养人文素养', '就业面较广'],
    prospects: {
      trend: 'stable',
      demandLevel: 'medium',
      competitionLevel: 'medium'
    }
  },

  // 自动化
  {
    id: '9',
    code: '080801',
    name: '自动化',
    englishName: 'Automation',
    category: '工学',
    subCategory: '自动化类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '自动化专业培养的学生要具备电工技术、电子技术、控制理论、自动检测与仪表、信息处理、系统工程、计算机技术与应用和网络技术等较宽广领域的工程技术基础和一定的专业知识。',
    employment: {
      rate: 91.19,
      avgSalary: 121000,
      salaryRange: { min: 70000, max: 220000 },
      topIndustries: ['制造业', '自动化设备', '机器人', '智能制造'],
      topPositions: ['自动化工程师', '控制系统工程师', '机器人工程师']
    },
    genderRatio: { male: 81.19, female: 18.81 },
    courses: {
      core: ['自动控制理论', '现代控制理论', '电机与拖动', '电力电子技术', '计算机控制技术'],
      elective: ['机器人技术', '智能控制', '工业网络']
    },
    features: ['技术含量高', '应用领域广', '就业前景好'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 经济学
  {
    id: '10',
    code: '020101',
    name: '经济学',
    englishName: 'Economics',
    category: '经济学',
    subCategory: '经济学类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '经济学专业培养具备比较扎实的经济学理论基础，熟悉现代经济学理论，比较熟练地掌握现代经济分析方法，知识面较宽，具有向经济学相关领域扩展渗透的能力。',
    employment: {
      rate: 88.45,
      avgSalary: 115000,
      salaryRange: { min: 60000, max: 250000 },
      topIndustries: ['金融', '政府机关', '咨询', '教育'],
      topPositions: ['经济分析师', '金融分析师', '投资顾问', '政策研究员']
    },
    genderRatio: { male: 42.45, female: 57.55 },
    courses: {
      core: ['微观经济学', '宏观经济学', '计量经济学', '政治经济学', '统计学', '会计学'],
      elective: ['国际经济学', '发展经济学', '产业经济学']
    },
    features: ['理论基础扎实', '分析能力强', '就业面广'],
    prospects: {
      trend: 'stable',
      demandLevel: 'medium',
      competitionLevel: 'high'
    }
  },

  // 工商管理
  {
    id: '11',
    code: '120201K',
    name: '工商管理',
    englishName: 'Business Administration',
    category: '管理学',
    subCategory: '工商管理类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '工商管理专业培养具备管理、经济、法律及企业管理方面的知识和能力，能在企、事业单位及政府部门从事管理以及教学、科研方面工作的工商管理学科高级专门人才。',
    employment: {
      rate: 89.32,
      avgSalary: 108000,
      salaryRange: { min: 50000, max: 300000 },
      topIndustries: ['企业管理', '咨询', '金融', '互联网'],
      topPositions: ['管理培训生', '项目经理', '运营经理', '咨询顾问']
    },
    genderRatio: { male: 38.32, female: 61.68 },
    courses: {
      core: ['管理学', '微观经济学', '宏观经济学', '管理信息系统', '统计学', '会计学', '财务管理'],
      elective: ['人力资源管理', '市场营销', '战略管理']
    },
    features: ['知识面广', '实用性强', '发展空间大'],
    prospects: {
      trend: 'stable',
      demandLevel: 'high',
      competitionLevel: 'high'
    }
  },

  // 英语
  {
    id: '12',
    code: '050201',
    name: '英语',
    englishName: 'English',
    category: '文学',
    subCategory: '外国语言文学类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(普通)',
    description: '英语专业培养具有扎实的英语语言基础和比较广泛的科学文化知识，能在外事、经贸、文化、新闻出版、教育、科研、旅游等部门从事翻译、研究、教学、管理工作的英语高级专门人才。',
    employment: {
      rate: 86.78,
      avgSalary: 95000,
      salaryRange: { min: 40000, max: 180000 },
      topIndustries: ['教育', '翻译', '外贸', '旅游'],
      topPositions: ['英语教师', '翻译', '外贸专员', '导游']
    },
    genderRatio: { male: 15.78, female: 84.22 },
    courses: {
      core: ['综合英语', '英语阅读', '英语写作', '英语听力', '英语口语', '英语语法', '英美文学'],
      elective: ['商务英语', '翻译理论与实践', '英语国家概况']
    },
    features: ['语言技能强', '国际化视野', '就业选择多'],
    prospects: {
      trend: 'stable',
      demandLevel: 'medium',
      competitionLevel: 'medium'
    }
  },

  // 软件工程技术 (本科职业)
  {
    id: '13',
    code: '080902T',
    name: '软件工程技术',
    englishName: 'Software Engineering Technology',
    category: '工学',
    subCategory: '计算机类',
    degree: '学士',
    duration: 4,
    educationLevel: '本科(职业)',
    description: '软件工程技术专业面向软件产业发展需要，培养具有扎实的软件工程技术基础理论和专业技能的应用型人才。',
    employment: {
      rate: 94.50,
      avgSalary: 125000,
      salaryRange: { min: 70000, max: 300000 },
      topIndustries: ['软件开发', '互联网', '信息技术', '游戏开发'],
      topPositions: ['软件开发工程师', '测试工程师', '运维工程师', '技术支持']
    },
    genderRatio: { male: 68.50, female: 31.50 },
    features: ['实践性强', '就业面广', '技术更新快'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 计算机应用技术 (专科高职)
  {
    id: '14',
    code: '510201',
    name: '计算机应用技术',
    englishName: 'Computer Application Technology',
    category: '工学',
    subCategory: '计算机类',
    degree: '专科',
    duration: 3,
    educationLevel: '专科(高职)',
    description: '计算机应用技术专业培养具有计算机应用技术的基础理论知识，具备计算机及相关设备的维护与维修、行业应用软件、平面图像处理、广告设计制作、动画制作、计算机网络及网站建设与管理、数据库管理与维护等应用能力和操作能力的高等技术应用性人才。',
    employment: {
      rate: 91.20,
      avgSalary: 85000,
      salaryRange: { min: 45000, max: 150000 },
      topIndustries: ['信息技术', '制造业', '服务业', '教育'],
      topPositions: ['程序员', '网络管理员', '系统维护员', 'IT技术支持']
    },
    genderRatio: { male: 72.20, female: 27.80 },
    features: ['实用性强', '就业快', '技能导向'],
    prospects: {
      trend: 'stable',
      demandLevel: 'high',
      competitionLevel: 'medium'
    }
  },

  // 护理 (专科高职)
  {
    id: '15',
    code: '520201',
    name: '护理',
    englishName: 'Nursing',
    category: '医学',
    subCategory: '护理学类',
    degree: '专科',
    duration: 3,
    educationLevel: '专科(高职)',
    description: '护理专业培养具备人文社会科学、医学、预防保健的基本知识及护理学的基本理论知识和技能，能在护理领域内从事临床护理、预防保健、护理管理、护理教学和护理科研的高级专门人才。',
    employment: {
      rate: 96.80,
      avgSalary: 75000,
      salaryRange: { min: 40000, max: 120000 },
      topIndustries: ['医疗卫生', '养老服务', '社区卫生', '康复医疗'],
      topPositions: ['护士', '护理员', '健康管理师', '康复师']
    },
    genderRatio: { male: 8.80, female: 91.20 },
    features: ['就业率高', '社会需求大', '职业稳定'],
    prospects: {
      trend: 'rising',
      demandLevel: 'high',
      competitionLevel: 'low'
    }
  }
]

// 学科门类列表
export const disciplineCategories: DisciplineCategory[] = [
  '工学',
  '医学', 
  '文学',
  '管理学',
  '理学',
  '经济学',
  '法学',
  '艺术学',
  '教育学',
  '农学',
  '历史学',
  '哲学'
]

// 教育层次列表
export const educationLevels: EducationLevel[] = [
  '本科(普通)',
  '本科(职业)',
  '专科(高职)'
]

// 学科子分类数据
export const subCategories: Record<DisciplineCategory, string[]> = {
  '工学': [
    '计算机类',
    '电子信息类',
    '机械类',
    '电气类',
    '自动化类',
    '材料类',
    '土木类',
    '化工与制药类',
    '交通运输类',
    '环境科学与工程类'
  ],
  '医学': [
    '基础医学类',
    '临床医学类',
    '口腔医学类',
    '公共卫生与预防医学类',
    '中医学类',
    '中西医结合类',
    '药学类',
    '中药学类',
    '法医学类',
    '医学技术类',
    '护理学类'
  ],
  '文学': [
    '中国语言文学类',
    '外国语言文学类',
    '新闻传播学类'
  ],
  '管理学': [
    '管理科学与工程类',
    '工商管理类',
    '农业经济管理类',
    '公共管理类',
    '图书情报与档案管理类',
    '物流管理与工程类',
    '工业工程类',
    '电子商务类',
    '旅游管理类'
  ],
  '理学': [
    '数学类',
    '物理学类',
    '化学类',
    '天文学类',
    '地理科学类',
    '大气科学类',
    '海洋科学类',
    '地球物理学类',
    '地质学类',
    '生物科学类',
    '心理学类',
    '统计学类'
  ],
  '经济学': [
    '经济学类',
    '财政学类',
    '金融学类',
    '经济与贸易类'
  ],
  '法学': [
    '法学类',
    '政治学类',
    '社会学类',
    '民族学类',
    '马克思主义理论类',
    '公安学类'
  ],
  '艺术学': [
    '艺术学理论类',
    '音乐与舞蹈学类',
    '戏剧与影视学类',
    '美术学类',
    '设计学类'
  ],
  '教育学': [
    '教育学类',
    '体育学类'
  ],
  '农学': [
    '植物生产类',
    '自然保护与环境生态类',
    '动物生产类',
    '动物医学类',
    '林学类',
    '水产类',
    '草学类'
  ],
  '历史学': [
    '历史学类'
  ],
  '哲学': [
    '哲学类'
  ]
}

// 学科门类统计数据
export const categoryStats = disciplineCategories.map(category => {
  const majorsInCategory = mockMajors.filter(major => major.category === category)
  const avgSalary = majorsInCategory.length > 0
    ? majorsInCategory.reduce((sum, major) => sum + (major.employment?.avgSalary || 0), 0) / majorsInCategory.length
    : 0
  const avgEmploymentRate = majorsInCategory.length > 0
    ? majorsInCategory.reduce((sum, major) => sum + (major.employment?.rate || 0), 0) / majorsInCategory.length
    : 0

  return {
    category,
    count: majorsInCategory.length,
    avgSalary: Math.round(avgSalary),
    avgEmploymentRate: Math.round(avgEmploymentRate * 100) / 100
  }
})
