import type { VolunteerUniversity } from '../types/volunteer'

export const volunteerUniversities: VolunteerUniversity[] = [
  {
    id: 'tsinghua',
    name: '清华大学',
    shortName: '清华',
    code: '10003',
    location: {
      province: '北京',
      city: '北京'
    },
    type: '985',
    category: '理工',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 1,
      category: 1
    },
    scores: [
      {
        year: 2023,
        province: '北京',
        subject: '物理',
        minScore: 688,
        avgScore: 692,
        maxScore: 698,
        ranking: 150,
        admissionRate: 0.8
      },
      {
        year: 2023,
        province: '河南',
        subject: '理科',
        minScore: 685,
        avgScore: 689,
        maxScore: 695,
        ranking: 280,
        admissionRate: 0.6
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['计算机科学与技术', '电子信息工程', '自动化', '建筑学', '土木工程'],
    availableMajors: [
      {
        id: 'tsinghua-cs',
        code: '080901',
        name: '计算机科学与技术',
        category: '工学',
        subCategory: '计算机类',
        university: { id: 'tsinghua', name: '清华大学' },
        admissionScore: [{
          year: 2023,
          minScore: 685,
          avgScore: 690,
          ranking: 500
        }],
        features: ['热门专业', '就业前景好', '薪资待遇高']
      },
      {
        id: 'tsinghua-ee',
        code: '080701',
        name: '电子信息工程',
        category: '工学',
        subCategory: '电子信息类',
        university: { id: 'tsinghua', name: '清华大学' },
        admissionScore: [{
          year: 2023,
          minScore: 683,
          avgScore: 687,
          ranking: 600
        }],
        features: ['传统强势专业', '就业面广']
      },
      {
        id: 'tsinghua-arch',
        code: '082801',
        name: '建筑学',
        category: '工学',
        subCategory: '建筑类',
        university: { id: 'tsinghua', name: '清华大学' },
        admissionScore: [{
          year: 2023,
          minScore: 680,
          avgScore: 684,
          ranking: 700
        }],
        features: ['五年制', '设计能力强', '就业前景好']
      }
    ],
    tuition: {
      min: 5000,
      max: 5000,
      unit: '年'
    },
    contact: {
      phone: '010-62770334',
      website: 'https://www.tsinghua.edu.cn',
      address: '北京市海淀区清华园1号'
    },
    admissionPlan: {
      total: 3400,
      province: '全国',
      year: 2023
    }
  },
  {
    id: 'peking',
    name: '北京大学',
    shortName: '北大',
    code: '10001',
    location: {
      province: '北京',
      city: '北京'
    },
    type: '985',
    category: '综合',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 2,
      category: 1
    },
    scores: [
      {
        year: 2023,
        province: '北京',
        subject: '物理',
        minScore: 687,
        avgScore: 691,
        maxScore: 697,
        ranking: 160,
        admissionRate: 0.8
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['哲学', '中国语言文学', '历史学', '数学', '物理学', '化学', '生物学'],
    availableMajors: [
      {
        id: 'peking-medicine',
        code: '100201K',
        name: '临床医学',
        category: '医学',
        subCategory: '临床医学类',
        university: { id: 'peking', name: '北京大学' },
        admissionScore: [{
          year: 2023,
          minScore: 680,
          avgScore: 685,
          ranking: 600
        }],
        features: ['八年制', '社会地位高', '就业稳定']
      },
      {
        id: 'peking-math',
        code: '070101',
        name: '数学与应用数学',
        category: '理学',
        subCategory: '数学类',
        university: { id: 'peking', name: '北京大学' },
        admissionScore: [{
          year: 2023,
          minScore: 678,
          avgScore: 682,
          ranking: 700
        }],
        features: ['基础学科', '学术氛围浓厚', '深造率高']
      },
      {
        id: 'peking-chinese',
        code: '050101',
        name: '汉语言文学',
        category: '文学',
        subCategory: '中国语言文学类',
        university: { id: 'peking', name: '北京大学' },
        admissionScore: [{
          year: 2023,
          minScore: 675,
          avgScore: 679,
          ranking: 800
        }],
        features: ['传统优势专业', '文化底蕴深厚']
      }
    ],
    tuition: {
      min: 5000,
      max: 5000,
      unit: '年'
    },
    contact: {
      phone: '010-62751407',
      website: 'https://www.pku.edu.cn',
      address: '北京市海淀区颐和园路5号'
    },
    admissionPlan: {
      total: 2972,
      province: '全国',
      year: 2023
    }
  },
  {
    id: 'zju',
    name: '浙江大学',
    shortName: '浙大',
    code: '10335',
    location: {
      province: '浙江',
      city: '杭州'
    },
    type: '985',
    category: '综合',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 3,
      category: 2
    },
    scores: [
      {
        year: 2023,
        province: '浙江',
        subject: '物理',
        minScore: 663,
        avgScore: 668,
        maxScore: 675,
        ranking: 1200,
        admissionRate: 2.1
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['计算机科学与技术', '软件工程', '控制科学与工程', '光学工程'],
    tuition: {
      min: 4800,
      max: 6000,
      unit: '年'
    },
    contact: {
      phone: '0571-87951006',
      website: 'https://www.zju.edu.cn',
      address: '浙江省杭州市西湖区余杭塘路866号'
    },
    admissionPlan: {
      total: 6400,
      province: '全国',
      year: 2023
    }
  },
  {
    id: 'sjtu',
    name: '上海交通大学',
    shortName: '上交',
    code: '10248',
    location: {
      province: '上海',
      city: '上海'
    },
    type: '985',
    category: '理工',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 4,
      category: 2
    },
    scores: [
      {
        year: 2023,
        province: '上海',
        subject: '物理',
        minScore: 580,
        avgScore: 585,
        maxScore: 590,
        ranking: 1500,
        admissionRate: 3.2
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['机械工程', '电气工程', '船舶与海洋工程', '临床医学'],
    tuition: {
      min: 5000,
      max: 5000,
      unit: '年'
    },
    contact: {
      phone: '021-34200000',
      website: 'https://www.sjtu.edu.cn',
      address: '上海市闵行区东川路800号'
    },
    admissionPlan: {
      total: 4200,
      province: '全国',
      year: 2023
    }
  },
  {
    id: 'nju',
    name: '南京大学',
    shortName: '南大',
    code: '10284',
    location: {
      province: '江苏',
      city: '南京'
    },
    type: '985',
    category: '综合',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 5,
      category: 3
    },
    scores: [
      {
        year: 2023,
        province: '江苏',
        subject: '物理',
        minScore: 658,
        avgScore: 662,
        maxScore: 668,
        ranking: 1800,
        admissionRate: 2.8
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['物理学', '化学', '天文学', '地质学', '生物学'],
    tuition: {
      min: 4600,
      max: 5200,
      unit: '年'
    },
    contact: {
      phone: '025-83593186',
      website: 'https://www.nju.edu.cn',
      address: '江苏省南京市栖霞区仙林大道163号'
    },
    admissionPlan: {
      total: 3350,
      province: '全国',
      year: 2023
    }
  },
  {
    id: 'fudan',
    name: '复旦大学',
    shortName: '复旦',
    code: '10246',
    location: {
      province: '上海',
      city: '上海'
    },
    type: '985',
    category: '综合',
    level: '本科',
    isPublic: true,
    ranking: {
      national: 6,
      category: 4
    },
    scores: [
      {
        year: 2023,
        province: '上海',
        subject: '物理',
        minScore: 578,
        avgScore: 583,
        maxScore: 588,
        ranking: 1600,
        admissionRate: 3.0
      }
    ],
    features: ['985工程', '211工程', '双一流', 'C9联盟'],
    keyMajors: ['新闻传播学', '哲学', '理论经济学', '中国语言文学', '数学'],
    tuition: {
      min: 5000,
      max: 5000,
      unit: '年'
    },
    contact: {
      phone: '021-55666668',
      website: 'https://www.fudan.edu.cn',
      address: '上海市杨浦区邯郸路220号'
    },
    admissionPlan: {
      total: 2950,
      province: '全国',
      year: 2023
    }
  }
]

// 筛选选项数据
export const filterOptions = {
  provinces: [
    '北京', '上海', '天津', '重庆',
    '河北', '山西', '内蒙古',
    '辽宁', '吉林', '黑龙江',
    '江苏', '浙江', '安徽', '福建', '江西', '山东',
    '河南', '湖北', '湖南', '广东', '广西', '海南',
    '四川', '贵州', '云南', '西藏',
    '陕西', '甘肃', '青海', '宁夏', '新疆'
  ],
  types: ['985', '211', '双一流', '普通本科', '民办', '独立学院'],
  categories: ['综合', '理工', '师范', '农林', '医药', '财经', '政法', '艺术', '体育', '军事', '民族', '语言'],
  levels: ['本科', '专科'],
  features: [
    '985工程', '211工程', '双一流', 'C9联盟',
    '教育部直属', '中央部委直属', '省部共建',
    '卓越工程师计划', '卓越医生计划', '卓越法律人才计划',
    '国家示范性高职', '国家骨干高职'
  ]
}
