// 招生计划服务
export interface SchoolGroupInfo {
  schoolUuid: string
  schoolName: string
  totalEnrollmentNumbers: number
  majorCount: number
  enrollmentNumbers2025: number
  enrollmentNumbers2024: number
  enrollmentNumbers2023: number
  enrollmentNumbers2022: number
  majorCount2025: number
  majorCount2024: number
  majorCount2023: number
  majorCount2022: number
  province: string
  city: string
  district: string
  collegeType: string
  is985: boolean
  is211: boolean
  isDualClass: boolean
  collegeCategory: string
  collegeTags: string[]
  eduLevel: string
  collegeProperty: string
  collegeCode: string
  ranking: number
  rankingInCategory: string
  website: string
  callNumber: string
  email: string
  address: string
  coverImage: string
  intro: string
  expenses: string
  oldName: string
  shortName: string
}

export interface MajorGroupInfo {
  majorName: string
  classOne: string
  classTwo: string
  totalEnrollmentNumbers: number
  schoolCount: number
  enrollmentNumbers2025: number
  enrollmentNumbers2024: number
  enrollmentNumbers2023: number
  enrollmentNumbers2022: number
  schoolCount2025: number
  schoolCount2024: number
  schoolCount2023: number
  schoolCount2022: number
}

export interface EnrollmentPlanGroupResponse {
  groupType: 'school' | 'major'
  schoolGroups?: {
    list: SchoolGroupInfo[]
    total: number
  }
  majorGroups?: {
    list: MajorGroupInfo[]
    total: number
  }
}

export interface EnrollmentPlanQueryParams {
  pageNo: number
  pageSize: number
  groupType: 'school' | 'major'
  collegeMajorName?: string
  schoolName?: string
  provinceName?: string
  classOne?: string
  classTwo?: string
  batchName?: string
  type?: string
  schoolUuid?: string
  year?: number
}

class EnrollmentPlanService {
  private baseUrl = 'https://card.kefeichangduo.top/admin-api/system/college-enrollment-plan'

  async getEnrollmentPlanData(params: EnrollmentPlanQueryParams): Promise<EnrollmentPlanGroupResponse> {
    try {
      console.log('调用真实API接口，参数:', params)
      const queryParams = new URLSearchParams()
      
      // 添加必需参数
      queryParams.append('pageNo', params.pageNo.toString())
      queryParams.append('pageSize', params.pageSize.toString())
      queryParams.append('groupType', params.groupType)
      
      // 添加可选参数
      if (params.collegeMajorName) queryParams.append('collegeMajorName', params.collegeMajorName)
      if (params.schoolName) queryParams.append('schoolName', params.schoolName)
      if (params.provinceName) queryParams.append('provinceName', params.provinceName)
      if (params.classOne) queryParams.append('classOne', params.classOne)
      if (params.classTwo) queryParams.append('classTwo', params.classTwo)
      if (params.batchName) queryParams.append('batchName', params.batchName)
      if (params.type) queryParams.append('type', params.type)
      if (params.schoolUuid) queryParams.append('schoolUuid', params.schoolUuid)
      if (params.year) queryParams.append('year', params.year.toString())

      const url = `${this.baseUrl}/group-query?${queryParams.toString()}`
      console.log('请求URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // 这里可能需要添加认证头
          // 'Authorization': 'Bearer your-token',
          // 'tenant-id': 'your-tenant-id'
        }
      })

      console.log('API响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API请求失败:', response.status, errorText)
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
      }

      const result = await response.json()
      console.log('API响应数据:', result)
      
      if (result.code !== 0) {
        throw new Error(result.msg || '获取招生计划数据失败')
      }

      return result.data
    } catch (error) {
      console.error('获取招生计划数据失败:', error)
      throw error
    }
  }

  // 获取模拟数据用于开发测试
  async getMockData(params: EnrollmentPlanQueryParams): Promise<EnrollmentPlanGroupResponse> {
    console.log('getMockData called with params:', params)
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    if (params.groupType === 'school') {
      return {
        groupType: 'school',
        schoolGroups: {
          list: [
            {
              schoolUuid: "c24a67f87405b82bec08a5638c32f282",
              schoolName: "北京大学",
              totalEnrollmentNumbers: 1800,
              majorCount: 95,
              enrollmentNumbers2025: 600,
              enrollmentNumbers2024: 500,
              enrollmentNumbers2023: 500,
              enrollmentNumbers2022: 500,
              majorCount2025: 32,
              majorCount2024: 30,
              majorCount2023: 28,
              majorCount2022: 27,
              province: "北京",
              city: "北京市",
              district: "海淀区",
              collegeType: "公办",
              is985: true,
              is211: true,
              isDualClass: true,
              collegeCategory: "综合类",
              collegeTags: ["985", "211", "双一流"],
              eduLevel: "本科",
              collegeProperty: "公办",
              collegeCode: "10001",
              ranking: 1,
              rankingInCategory: "综合类第1名",
              website: "https://www.pku.edu.cn",
              callNumber: "010-62751407",
              email: "<EMAIL>",
              address: "北京市海淀区颐和园路5号",
              coverImage: "https://static.gugudata.com/pku-logo.jpg",
              intro: "北京大学创办于1898年，初名京师大学堂，是中国第一所国立综合性大学，也是近代中国最早以\"大学\"之名创办的学校，其成立标志着中国近代高等教育的开端。北大是中国近代以来唯一以国家最高学府身份创立的学校，最初也是国家最高教育行政机关，行使教育部职能，统管全国教育。",
              expenses: "学费：5000-6000元/年",
              oldName: "京师大学堂",
              shortName: "北大"
            },
            {
              schoolUuid: "d35b78g98516c93cfd19b6749d43g393",
              schoolName: "清华大学",
              totalEnrollmentNumbers: 1680,
              majorCount: 88,
              enrollmentNumbers2025: 580,
              enrollmentNumbers2024: 480,
              enrollmentNumbers2023: 460,
              enrollmentNumbers2022: 460,
              majorCount2025: 30,
              majorCount2024: 28,
              majorCount2023: 26,
              majorCount2022: 24,
              province: "北京",
              city: "北京市",
              district: "海淀区",
              collegeType: "公办",
              is985: true,
              is211: true,
              isDualClass: true,
              collegeCategory: "理工类",
              collegeTags: ["985", "211", "双一流"],
              eduLevel: "本科",
              collegeProperty: "公办",
              collegeCode: "10003",
              ranking: 2,
              rankingInCategory: "理工类第1名",
              website: "https://www.tsinghua.edu.cn",
              callNumber: "010-62770334",
              email: "<EMAIL>",
              address: "北京市海淀区清华园1号",
              coverImage: "https://static.gugudata.com/tsinghua-logo.jpg",
              intro: "清华大学始建于1911年，坐落于北京西北郊风景秀丽的清华园，是中国著名高等学府，是中国高层次人才培养和科学技术研究的重要基地。清华大学的前身清华学堂始建于1911年，1912年更名为清华学校。1928年更名为国立清华大学。",
              expenses: "学费：5000-6000元/年",
              oldName: "清华学堂",
              shortName: "清华"
            },
            {
              schoolUuid: "f44e2fff2fdf49230cccf5d7205a5769",
              schoolName: "滁州学院",
              totalEnrollmentNumbers: 4029,
              majorCount: 44,
              enrollmentNumbers2025: 4200,
              enrollmentNumbers2024: 4029,
              enrollmentNumbers2023: 0,
              enrollmentNumbers2022: 0,
              majorCount2025: 46,
              majorCount2024: 44,
              majorCount2023: 0,
              majorCount2022: 0,
              province: "安徽",
              city: "滁州市",
              district: "琅琊区",
              collegeType: "本科",
              is985: false,
              is211: false,
              isDualClass: false,
              collegeCategory: "综合类",
              collegeTags: [],
              eduLevel: "本科",
              collegeProperty: "公办",
              collegeCode: "10377",
              ranking: 363,
              rankingInCategory: "综合类",
              website: "https://www.chzu.edu.cn/",
              callNumber: "0550-3518833,0550-3510048",
              email: "<EMAIL>",
              address: "琅琊校区：安徽省滁州市琅琊西路2号,会峰校区：安徽省滁州市会峰西路1号",
              coverImage: "https://static.gugudata.com/854cee26ad5c11ebb8be001a7dda7111.jpg",
              intro: "滁州学院坐落于皖东江淮之间，学校所在地滁州市是南京都市圈和合肥经济圈中心城市，也是长三角一体化发展核心区城市之一，素有\"金陵锁钥、江淮保障\"之美誉。学校肇始于1950年创建的皖北滁州师范学校，历经滁县师范学校、安徽师范大学滁县教学点（分校）等发展阶段，1980年更名为滁州师范专科学校。2004年经教育部批准升格为本科院校并定名为滁州学院。",
              expenses: "视觉传达设计专业、环境设计专业、产品设计专业、数字媒体艺术专业为7000元/生·学年；音乐学专业、美术学专业为5000元/生·学年；体育教育专业、英语专业、商务英语专业（本科）、新闻学专业为3850元/生·学年；计算机科学与技术专业、电子信息工程专业、土木工程专业、工业设计专业、通信工程专业为4290元/生·学年。",
              oldName: "滁州师范专科学校",
              shortName: "CHZU"
            }
          ],
          total: 2856
        }
      }
    } else {
      return {
        groupType: 'major',
        majorGroups: {
          list: [
            {
              majorName: "计算机科学与技术",
              classOne: "工学",
              classTwo: "计算机类",
              totalEnrollmentNumbers: 3000,
              schoolCount: 135,
              enrollmentNumbers2025: 950,
              enrollmentNumbers2024: 850,
              enrollmentNumbers2023: 825,
              enrollmentNumbers2022: 825,
              schoolCount2025: 45,
              schoolCount2024: 42,
              schoolCount2023: 40,
              schoolCount2022: 38
            },
            {
              majorName: "软件工程",
              classOne: "工学",
              classTwo: "计算机类",
              totalEnrollmentNumbers: 2650,
              schoolCount: 110,
              enrollmentNumbers2025: 850,
              enrollmentNumbers2024: 750,
              enrollmentNumbers2023: 725,
              enrollmentNumbers2022: 725,
              schoolCount2025: 38,
              schoolCount2024: 35,
              schoolCount2023: 33,
              schoolCount2022: 30
            }
          ],
          total: 703
        }
      }
    }
  }
}

export const enrollmentPlanService = new EnrollmentPlanService()
