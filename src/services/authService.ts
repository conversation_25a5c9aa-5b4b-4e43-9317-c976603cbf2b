// 本地类型定义（避免导入问题）
interface User {
  id: string
  username?: string
  email?: string
  phone?: string
  avatar?: string
  nickname?: string
  province?: string
  subjects?: string[]
  score?: number
  ranking?: number
  createdAt: string
  updatedAt: string
}

type LoginMethod = 'phone' | 'email' | 'wechat'
type RegisterMethod = 'phone' | 'email' | 'username'

interface LoginRequest {
  method: LoginMethod
  phone?: string
  email?: string
  password?: string
  verificationCode?: string
  wechatCode?: string
}

interface RegisterRequest {
  method: RegisterMethod
  username?: string
  email?: string
  phone?: string
  password: string
  confirmPassword: string
  verificationCode?: string
  nickname?: string
}

interface VerificationCodeRequest {
  type: 'login' | 'register'
  phone?: string
  email?: string
}

interface LoginResponse {
  user: User
  token: string
  refreshToken?: string
}

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
}

// 模拟API基础URL
const API_BASE_URL = 'https://api.fh-ai.com'

// 本地存储键名
const TOKEN_KEY = 'fh_auth_token'
const REFRESH_TOKEN_KEY = 'fh_refresh_token'
const USER_KEY = 'fh_user_info'

class AuthService {
  // 获取存储的token
  getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY)
  }

  // 获取存储的用户信息
  getStoredUser(): User | null {
    const userStr = localStorage.getItem(USER_KEY)
    return userStr ? JSON.parse(userStr) : null
  }

  // 存储认证信息
  private storeAuthData(token: string, user: User, refreshToken?: string) {
    localStorage.setItem(TOKEN_KEY, token)
    localStorage.setItem(USER_KEY, JSON.stringify(user))
    if (refreshToken) {
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
    }
  }

  // 清除认证信息
  clearAuthData() {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(USER_KEY)
  }

  // 发送验证码
  async sendVerificationCode(data: VerificationCodeRequest): Promise<void> {
    try {
      // 模拟API调用
      console.log('发送验证码:', data)
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟成功响应
      const response: ApiResponse = {
        success: true,
        message: '验证码已发送'
      }
      
      if (!response.success) {
        throw new Error(response.message || '发送验证码失败')
      }
    } catch (error) {
      console.error('发送验证码失败:', error)
      throw error
    }
  }

  // 用户登录
  async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      console.log('用户登录:', data)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 模拟登录验证
      if (data.method === 'phone' && data.phone === '13800138000' && data.verificationCode === '123456') {
        // 手机验证码登录成功
      } else if (data.method === 'email' && data.email === '<EMAIL>' && data.password === '123456') {
        // 邮箱密码登录成功
      } else if (data.method === 'wechat' && data.wechatCode) {
        // 微信登录成功
      } else {
        throw new Error('登录信息不正确')
      }
      
      // 模拟用户数据
      const user: User = {
        id: '1',
        username: data.method === 'email' ? 'testuser' : undefined,
        email: data.email,
        phone: data.phone,
        nickname: data.method === 'phone' ? '手机用户' : data.method === 'email' ? '邮箱用户' : '微信用户',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
        province: '安徽',
        subjects: ['物理', '生物', '地理'],
        score: 467,
        ranking: 29819,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      const token = 'mock_jwt_token_' + Date.now()
      const refreshToken = 'mock_refresh_token_' + Date.now()
      
      const loginResponse: LoginResponse = {
        user,
        token,
        refreshToken
      }
      
      // 存储认证信息
      this.storeAuthData(token, user, refreshToken)
      
      return loginResponse
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 用户注册
  async register(data: RegisterRequest): Promise<LoginResponse> {
    try {
      console.log('用户注册:', data)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟注册验证
      if (data.password !== data.confirmPassword) {
        throw new Error('两次输入的密码不一致')
      }
      
      if (data.method === 'phone' && !data.verificationCode) {
        throw new Error('请输入手机验证码')
      }
      
      if (data.method === 'email' && !data.verificationCode) {
        throw new Error('请输入邮箱验证码')
      }
      
      // 模拟用户数据
      const user: User = {
        id: 'new_' + Date.now(),
        username: data.username,
        email: data.email,
        phone: data.phone,
        nickname: data.nickname || '新用户',
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`,
        province: '安徽',
        subjects: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      const token = 'mock_jwt_token_' + Date.now()
      const refreshToken = 'mock_refresh_token_' + Date.now()
      
      const registerResponse: LoginResponse = {
        user,
        token,
        refreshToken
      }
      
      // 存储认证信息
      this.storeAuthData(token, user, refreshToken)
      
      return registerResponse
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  // 用户登出
  logout() {
    this.clearAuthData()
  }

  // 更新用户信息
  async updateUser(data: Partial<User>): Promise<User> {
    try {
      console.log('更新用户信息:', data)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const currentUser = this.getStoredUser()
      if (!currentUser) {
        throw new Error('用户未登录')
      }
      
      const updatedUser: User = {
        ...currentUser,
        ...data,
        updatedAt: new Date().toISOString()
      }
      
      // 更新本地存储
      localStorage.setItem(USER_KEY, JSON.stringify(updatedUser))
      
      return updatedUser
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  // 检查token是否有效
  async validateToken(): Promise<boolean> {
    const token = this.getToken()
    if (!token) return false
    
    try {
      // 模拟token验证
      await new Promise(resolve => setTimeout(resolve, 500))
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      this.clearAuthData()
      return false
    }
  }

  // 微信登录URL生成
  generateWechatLoginUrl(): string {
    const appId = 'wx1234567890abcdef' // 模拟微信应用ID
    const redirectUri = encodeURIComponent(window.location.origin + '/auth/wechat/callback')
    const state = Date.now().toString()
    
    return `https://open.weixin.qq.com/connect/qrconnect?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_login&state=${state}#wechat_redirect`
  }
}

export const authService = new AuthService()
export default authService
