/**
 * 录取分数线API服务
 * 包含省录取分数线和高校录取分数线查询功能
 */

const API_BASE_URL = 'http://localhost:3001/api/gugudata'
const API_KEY = 'GLGDHX8N6N29U7L75M6FBC4LPVQYM398'

// 省录取分数线查询参数
export interface ProvinceScoreLineFilters {
  keyword?: string // 录取省份搜索关键字，如江苏。为空返回所有数据
  year?: number // 录取数据的年份，2014 - 2025
  category?: string // 录取学科分类筛选，如：理科、文科、综合等
}

// 高校录取分数线查询参数
export interface CollegeScoreLineFilters {
  searchtype: 'PROVINCENAME' | 'COLLEGENAME' // 查询条件类型
  keyword: string // 录取省份或高校名称
  pageindex?: number // 分页参数，第几页
  pagesize?: number // 分页参数，每页总条数，取值范围在 1 ~ 20 之间
  year?: number // 查询的录取年份
  min?: number // 录取最低分查询条件
  type?: string // 文科、理科、综合等
  keywordstrict?: boolean // 控制keyword参数在查询高校名称时是否进行模糊查询
  enrollprovince?: string // 招生省份条件
  batchname?: string // 录取批次参数
  collegeprovincename?: string // 学院所属省份筛选
  enrollmenttype?: string // 招生类型筛选
  schooluuid?: string // 咕咕数据平台高校唯一ID
  sort?: string // 排序控制
  minrange?: string // 录取最低分区间查询条件
}

// 省录取分数线数据结构
export interface ProvinceScoreLineData {
  Province: string // 录取省份
  Category: string // 录取学科分类
  ScoreBatch: string // 录取批次
  Year: number // 招生年份
  Score: number // 录取分数线
  SpecialName: string | null // 专业说明
}

// 高校录取分数线数据结构
export interface CollegeScoreLineData {
  Province: string // 招生的省份
  SchoolUUID: string // 咕咕数据平台高校唯一ID
  CollegeName: string // 高校名称
  Year: number // 招生年份
  HighestScore: string // 录取最高分
  AverageScore: string // 录取平均分
  LowestScore: string // 录取最低分
  LowestRank: string // 录取最低位次
  ProvincialControlLine: string // 录取省控线
  EnrollmentType: string // 招生类型
  SelectionLevel: string // 选测等级
  AdmissionBatch: string // 录取批次
  TypeName: string // 文科、理科或综合
  CourseSelection: string // 选科要求
  CourseSelectionName: string // 专业组
  SchoolType: string // 学校类型
  SchoolInCity: string // 高校所在的城市
  Is985: boolean // 是否为985院校
  Is211: boolean // 是否为211院校
  IsDualClass: boolean // 是否为双一流院校
  CoverImage: string // 高校logo
}

// API响应结构
export interface ApiResponse<T> {
  DataStatus: {
    StatusCode: number
    StatusDescription: string
    ResponseDateTime: string
    DataTotalCount: number
    RequestParameter?: string
  }
  Data: T[]
}

/**
 * 构建省录取分数线查询参数
 */
function buildProvinceScoreLineParams(filters: ProvinceScoreLineFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', API_KEY)
  
  if (filters.keyword !== undefined) {
    params.append('keyword', filters.keyword)
  } else {
    params.append('keyword', '') // 为空返回所有数据
  }
  
  if (filters.year) {
    params.append('year', filters.year.toString())
  } else {
    params.append('year', '') // 默认为空
  }
  
  if (filters.category) {
    params.append('category', filters.category)
  } else {
    params.append('category', '') // 默认为空
  }
  
  return params
}

/**
 * 构建高校录取分数线查询参数
 */
function buildCollegeScoreLineParams(filters: CollegeScoreLineFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', API_KEY)
  params.append('searchtype', filters.searchtype)
  params.append('keyword', filters.keyword)
  params.append('pageindex', (filters.pageindex || 1).toString())
  params.append('pagesize', (filters.pagesize || 10).toString())
  
  if (filters.year) {
    params.append('year', filters.year.toString())
  } else {
    params.append('year', '') // 默认为空
  }
  
  if (filters.min) {
    params.append('min', filters.min.toString())
  } else {
    params.append('min', '') // 默认为空
  }
  
  if (filters.type) {
    params.append('type', filters.type)
  } else {
    params.append('type', '') // 默认为空
  }
  
  params.append('keywordstrict', (filters.keywordstrict || false).toString())
  
  if (filters.enrollprovince) {
    params.append('enrollprovince', filters.enrollprovince)
  } else {
    params.append('enrollprovince', '') // 默认为空
  }
  
  if (filters.batchname) {
    params.append('batchname', filters.batchname)
  } else {
    params.append('batchname', '') // 默认为空
  }
  
  if (filters.collegeprovincename) {
    params.append('collegeprovincename', filters.collegeprovincename)
  } else {
    params.append('collegeprovincename', '') // 默认为空
  }
  
  if (filters.enrollmenttype) {
    params.append('enrollmenttype', filters.enrollmenttype)
  } else {
    params.append('enrollmenttype', '') // 默认为空
  }
  
  if (filters.schooluuid) {
    params.append('schooluuid', filters.schooluuid)
  } else {
    params.append('schooluuid', '') // 默认为空
  }
  
  if (filters.sort) {
    params.append('sort', filters.sort)
  } else {
    params.append('sort', '') // 默认为空
  }
  
  params.append('minrange', filters.minrange || '0')
  
  return params
}

/**
 * 获取省录取分数线数据
 */
export async function getProvinceScoreLine(filters: ProvinceScoreLineFilters = {}): Promise<ApiResponse<ProvinceScoreLineData>> {
  try {
    const params = buildProvinceScoreLineParams(filters)
    const url = `${API_BASE_URL}/metadata/ceeprovince?${params.toString()}`
    
    console.log('🔍 请求省录取分数线:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 省录取分数线获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 省录取分数线获取失败:', error)
    throw error
  }
}

/**
 * 获取高校录取分数线数据
 */
export async function getCollegeScoreLine(filters: CollegeScoreLineFilters): Promise<ApiResponse<CollegeScoreLineData>> {
  try {
    const params = buildCollegeScoreLineParams(filters)
    const url = `${API_BASE_URL}/metadata/ceecollegeline?${params.toString()}`
    
    console.log('🔍 请求高校录取分数线:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 高校录取分数线获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 高校录取分数线获取失败:', error)
    throw error
  }
}

// 常用的省份列表
export const PROVINCES = [
  '上海', '云南', '内蒙古', '北京', '台湾', '吉林', '四川', '天津', 
  '宁夏', '安徽', '山东', '山西', '广东', '广西', '新疆', '江苏', 
  '江西', '河北', '河南', '浙江', '海南', '湖北', '湖南', '澳门', 
  '甘肃', '福建', '西藏', '贵州', '辽宁', '重庆', '陕西', '青海', 
  '香港', '黑龙江'
]

// 常用的科目类型（注意：建议使用 getAvailableSubjectSelections 函数获取动态数据）
export const SUBJECT_TYPES = [
  '理科', '文科', '综合', '艺术类', '物理类', '历史类', '体育类',
  '艺术文', '艺术理', '体育理', '体育文'
]

// 推荐使用的动态获取科目类型的函数
export { getAvailableSubjectSelections } from './oneScoreOneSectionApi'

// 常用的录取批次
export const ADMISSION_BATCHES = [
  '本科一批', '本科二批', '本科批', '专科批', '本科三批',
  '艺术类（本科）', '艺术类（高职专科）', '体育类（本科）',
  '体育类（高职专科）', '特殊类型招生控制线'
]
