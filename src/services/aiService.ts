// 百度AppBuilder AI API 服务

interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface CreateConversationRequest {
  app_id: string
}

interface CreateConversationResponse {
  request_id: string
  conversation_id: string
}

interface ChatRequest {
  app_id: string
  query: string
  stream: boolean
  conversation_id: string
  end_user_id?: string
}

interface ChatResponse {
  request_id: string
  date: string
  answer: string
  conversation_id: string
  message_id: string
  is_completion: boolean
  content: Array<{
    event_code: number
    event_message: string
    event_type: string
    event_id: string
    event_status: string
    content_type: string
    outputs: {
      text: string | any
    }
    usage?: {
      prompt_tokens: number
      completion_tokens: number
      total_tokens: number
      name: string
    }
  }>
}

class AiService {
  private baseUrl: string
  private apiKey: string
  private appId: string

  constructor() {
    // 在开发环境使用代理，生产环境需要后端API
    this.baseUrl = import.meta.env.DEV
      ? 'http://localhost:3001/api/baidu/v2'
      : 'https://qianfan.baidubce.com/v2'
    this.apiKey = import.meta.env.VITE_BAIDU_API_KEY || 'bce-v3/ALTAK-CJ2wsTJjGxkv86YSKzmPm/36e80d188ea58c3ff4ce10f80b1a16ab8a013629'
    this.appId = import.meta.env.VITE_BAIDU_APP_ID || 'ab83ec47-12c1-4755-ba0f-c9318f2c37aa'
  }

  /**
   * 创建新对话
   */
  async createConversation(): Promise<string> {
    try {
      console.log('开始创建新对话...')
      console.log('请求URL:', `${this.baseUrl}/app/conversation`)
      console.log('请求参数:', { app_id: this.appId })
      
      const response = await fetch(`${this.baseUrl}/app/conversation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          app_id: this.appId
        } as CreateConversationRequest)
      })

      console.log('API响应状态:', response.status, response.statusText)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('创建对话失败，API响应:', errorText)
        throw new Error(`创建对话失败: ${response.status} ${response.statusText} - ${errorText}`)
      }

      const data = await response.json() as CreateConversationResponse
      console.log('创建对话成功，对话ID:', data.conversation_id)
      return data.conversation_id
    } catch (error) {
      console.error('创建对话失败:', error)
      throw error
    }
  }

  /**
   * 发送聊天消息
   */
  async sendMessage(query: string, conversationId: string, stream = true): Promise<Response> {
    try {
      const response = await fetch(`${this.baseUrl}/app/conversation/runs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          app_id: this.appId,
          query,
          stream,
          conversation_id: conversationId
        } as ChatRequest)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`发送消息失败: ${response.status} ${response.statusText} - ${errorText}`)
      }

      return response
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  /**
   * 处理流式响应
   */
  async processStreamResponse(response: Response, onProgress?: (text: string, type: 'thinking' | 'answer' | 'followup', data?: any) => void): Promise<string> {
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let finalResult = ''
    let buffer = ''
    let thoughtText = ''
    let followUpQueries: string[] = []

    if (!reader) {
      throw new Error('无法读取响应流')
    }

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true })

        // 按行分割数据
        const lines = buffer.split('\n')

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || ''

        for (const line of lines) {
          const trimmedLine = line.trim()
          if (trimmedLine === '' || trimmedLine === 'data: [DONE]') continue

          // 处理 Server-Sent Events 格式
          let jsonStr = trimmedLine
          if (trimmedLine.startsWith('data: ')) {
            jsonStr = trimmedLine.substring(6)
          }

          try {
            const data = JSON.parse(jsonStr) as ChatResponse

            // 处理不同类型的事件
            if (data.content && data.content.length > 0) {
              for (const content of data.content) {
                // 处理思考过程（thought类型）
                if (content.event_type === 'thought' && content.outputs?.text) {
                  const newText = content.outputs.text
                  if (typeof newText === 'string') {
                    thoughtText += newText
                    // 实时更新思考过程
                    if (onProgress) {
                      onProgress(thoughtText, 'thinking')
                    }
                  }
                }

                // 处理最终回答（ChatAgent类型）
                if (content.event_type === 'ChatAgent' && content.outputs?.text) {
                  if (typeof content.outputs.text === 'string') {
                    finalResult += content.outputs.text
                    if (onProgress) {
                      onProgress(finalResult, 'answer')
                    }
                  }
                }

                // 处理追问建议（FollowUpQuery类型）
                if (content.event_type === 'FollowUpQuery' && content.outputs?.json) {
                  const followUpData = content.outputs.json
                  if (followUpData.follow_up_querys && Array.isArray(followUpData.follow_up_querys)) {
                    followUpQueries = [...followUpQueries, ...followUpData.follow_up_querys]
                    if (onProgress) {
                      onProgress('', 'followup', { follow_up_querys: followUpQueries })
                    }
                  }
                }

                // 处理其他类型的输出
                if (content.event_type === 'AgentThought' && content.outputs?.text) {
                  // 可以在这里处理其他思考过程
                  continue
                }
              }
            }

            // 如果对话完成，使用最终答案
            if (data.is_completion) {
              if (data.answer) {
                finalResult = data.answer
                if (onProgress) {
                  onProgress(finalResult, 'answer')
                }
              }
              break
            }
          } catch (parseError) {
            // 忽略解析错误，继续处理下一行
            console.warn('解析响应行失败:', trimmedLine, parseError)
            continue
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    // 返回最终结果，如果没有最终答案则返回思考过程
    return finalResult || thoughtText || '抱歉，我暂时无法回答这个问题。'
  }

  /**
   * 处理高考相关问题的智能回答
   */
  async askQuestion(userQuestion: string, conversationId?: string, onProgress?: (text: string, type: 'thinking' | 'answer' | 'followup', data?: any) => void): Promise<string> {
    try {
      // 如果没有对话ID，先创建一个新对话
      let currentConversationId = conversationId
      if (!currentConversationId) {
        currentConversationId = await this.createConversation()
      }

      // 发送消息并处理流式响应
      const response = await this.sendMessage(userQuestion, currentConversationId, true)
      const result = await this.processStreamResponse(response, onProgress)

      return result
    } catch (error) {
      console.error('处理问题时出错:', error)

      // 返回友好的错误信息
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('403')) {
          return '抱歉，API密钥验证失败。请联系管理员检查配置。'
        } else if (error.message.includes('429')) {
          return '抱歉，请求过于频繁，请稍后再试。'
        } else if (error.message.includes('500')) {
          return '抱歉，服务器暂时出现问题，请稍后再试。'
        }
      }

      return '抱歉，我暂时无法回答您的问题。请稍后再试或联系技术支持。'
    }
  }

  /**
   * 获取预设的常见问题
   */
  getCommonQuestions(): string[] {
    return [
      '如何选择适合自己的专业？',
      '985和211大学有什么区别？',
      '什么是平行志愿？如何填报？',
      '如何查看往年录取分数线？',
      '计算机专业的就业前景如何？',
      '医学专业需要什么条件？',
      '艺术类专业如何报考？',
      '如何判断专业的冷热程度？',
      '双一流大学是什么意思？',
      '如何避免志愿填报的常见误区？'
    ]
  }

  /**
   * 验证API密钥和应用ID是否有效，并返回创建的对话ID
   */
  async validateApiKey(): Promise<{ isValid: boolean; conversationId?: string }> {
    try {
      console.log('验证API密钥和应用ID...')
      console.log('当前API密钥:', this.apiKey ? '已设置' : '未设置')
      console.log('当前应用ID:', this.appId ? '已设置' : '未设置')

      if (!this.apiKey || !this.appId) {
        console.warn('API密钥或应用ID未设置')
        return { isValid: false }
      }

      // 尝试创建一个对话来验证API密钥和应用ID
      const conversationId = await this.createConversation()
      console.log('验证成功，创建的对话ID:', conversationId)
      return { isValid: true, conversationId }
    } catch (error) {
      console.error('API密钥验证失败:', error)
      return { isValid: false }
    }
  }

  /**
   * 验证API密钥和应用ID是否有效（兼容旧版本）
   * @deprecated 请使用 validateApiKey() 方法
   */
  async validateApiKeyLegacy(): Promise<boolean> {
    const result = await this.validateApiKey()
    return result.isValid
  }

  /**
   * 获取应用ID
   */
  getAppId(): string {
    return this.appId
  }

  /**
   * 设置应用ID
   */
  setAppId(appId: string): void {
    this.appId = appId
  }
}

// 创建单例实例
export const aiService = new AiService()

// 导出类型
export type { ChatMessage, ChatResponse, CreateConversationResponse }
