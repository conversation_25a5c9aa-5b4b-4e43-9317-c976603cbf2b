// 专业树形结构API服务
export interface MajorTreeNode {
  id: string
  name: string
  code?: string
  categoryId?: string
  educationLevel: string
  children?: MajorTreeNode[]
  // 扩展字段
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
  // 新增实际API字段
  graduateScale?: string
  maleFemaleRatio?: string
  recommendSchools?: string[]
  courses?: any[]
  disciplinaryCategory?: string
  disciplinarySubCategory?: string
  careerDirection?: string
  isRecommended?: boolean
  majorIntroduction?: string
}

export interface MajorTreeResponse {
  code: number
  data: MajorTreeNode[] | Record<string, any>
  msg: string
}

// 教育层次类型 - 前端显示用
export type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

// API实际需要的教育层次参数（使用英文括号）
export type ApiEducationLevel = '本科(普通教育)' | '本科(职业教育)' | '高职(专科)'

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 教育层次映射 - 前端显示 -> API参数（使用英文括号）
 */
function mapEducationLevelToApi(level: EducationLevel): ApiEducationLevel {
  const mapping: Record<EducationLevel, ApiEducationLevel> = {
    '本科(普通)': '本科(普通教育)',
    '本科(职业)': '本科(职业教育)',
    '专科(高职)': '高职(专科)'
  }
  return mapping[level]
}

/**
 * 获取专业树形结构数据
 */
export async function getMajorTree(
  name: string = '',
  categoryId: string = '',
  educationLevel: EducationLevel = '本科(普通)'
): Promise<MajorTreeResponse> {
  try {
    // 构建API URL
    let url = `${API_BASE_URL}/admin-api/system/metadata/ceemajor/tree`

    // 添加查询参数
    const params = new URLSearchParams()
    if (name) params.append('majorName', name)
    if (categoryId) params.append('categoryId', categoryId)
    if (educationLevel) {
      // 转换为API需要的格式
      const apiLevel = mapEducationLevelToApi(educationLevel)
      params.append('educationLevel', apiLevel)
    }

    // 如果有参数，添加到URL
    if (params.toString()) {
      url += '?' + params.toString()
    }

    console.log('🔍 请求专业树形结构:', url)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: MajorTreeResponse = await response.json()
    console.log('✅ 专业树形结构响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('❌ 获取专业树形结构失败:', error)
    throw error
  }
}

/**
 * 搜索专业
 */
export async function searchMajors(
  keyword: string,
  educationLevel: EducationLevel = '本科(普通)',
  categoryId: string = ''
): Promise<MajorTreeResponse> {
  return getMajorTree(keyword, categoryId, educationLevel)
}

/**
 * 按分类获取专业
 */
export async function getMajorsByCategory(
  categoryId: string,
  educationLevel: EducationLevel = '本科(普通)'
): Promise<MajorTreeResponse> {
  return getMajorTree('', categoryId, educationLevel)
}

/**
 * 获取所有专业（按教育层次）
 */
export async function getAllMajors(
  educationLevel: EducationLevel = '本科(普通)'
): Promise<MajorTreeResponse> {
  return getMajorTree('', '', educationLevel)
}

/**
 * 将API返回的数据结构扁平化为专业列表
 */
export function flattenMajorTree(apiData: any): MajorTreeNode[] {
  const result: MajorTreeNode[] = []

  // 处理API返回的数据结构
  if (apiData && apiData.tree && apiData.tree.categories) {
    const categories = apiData.tree.categories

    for (const category of categories) {
      // 只处理已加载的分类（loaded: true）
      if (category.loaded && category.children) {
        for (const subCategory of category.children) {
          if (subCategory.children) {
            for (const major of subCategory.children) {
              // 转换为标准格式
              result.push({
                id: major.id?.toString() || major.code || Math.random().toString(36).substr(2, 9),
                name: major.name,
                code: major.code,
                educationLevel: major.educationLevel,
                categoryId: category.id,
                description: major.majorIntroduction,
                // 添加实际API字段
                graduateScale: major.graduateScale,
                maleFemaleRatio: major.maleFemaleRatio,
                recommendSchools: major.recommendSchools || [],
                courses: major.courses || [],
                disciplinaryCategory: major.disciplinaryCategory,
                disciplinarySubCategory: major.disciplinarySubCategory,
                careerDirection: major.careerDirection,
                isRecommended: major.isRecommended,
                employment: {
                  rate: parseGraduateScale(major.graduateScale),
                  avgSalary: estimateSalaryByCategory(major.disciplinaryCategory),
                  salaryRange: getSalaryRangeByCategory(major.disciplinaryCategory),
                  topIndustries: getIndustriesByCategory(major.disciplinaryCategory),
                  topPositions: getPositionsByCategory(major.disciplinaryCategory)
                },
                genderRatio: parseGenderRatio(major.maleFemaleRatio),
                features: generateFeatures(major),
                prospects: generateProspects(major)
              })
            }
          }
        }
      }
    }
  }

  return result
}

/**
 * 从API数据中提取所有学科门类信息（包括未加载的）
 */
export function extractCategoriesFromApiData(apiData: any): {
  categories: string[]
  categoryStats: Array<{category: string, count: number}>
} {
  const categories: string[] = []
  const categoryStats: Array<{category: string, count: number}> = []

  if (apiData && apiData.tree && apiData.tree.categories) {
    for (const category of apiData.tree.categories) {
      categories.push(category.name)
      categoryStats.push({
        category: category.name,
        count: category.majorCount || 0
      })
    }
  }

  // 如果API数据中有categories数组，也使用它
  if (apiData && apiData.categories) {
    for (const categoryName of apiData.categories) {
      if (!categories.includes(categoryName)) {
        categories.push(categoryName)
        // 从tree中查找对应的统计信息
        const categoryInfo = apiData.tree?.categories?.find((c: any) => c.name === categoryName)
        categoryStats.push({
          category: categoryName,
          count: categoryInfo?.majorCount || 0
        })
      }
    }
  }

  return { categories, categoryStats }
}

// 辅助函数：解析毕业生规模
function parseGraduateScale(graduateScale: string): number {
  if (!graduateScale) return 85

  // 提取数字范围，如 "8000-9000人" -> 平均值
  const match = graduateScale.match(/(\d+)-(\d+)/)
  if (match) {
    const min = parseInt(match[1])
    const max = parseInt(match[2])
    // 根据毕业生规模估算就业率
    const avg = (min + max) / 2
    if (avg > 20000) return 92
    if (avg > 10000) return 88
    if (avg > 5000) return 85
    return 82
  }

  // 单个数字，如 "50-100人"
  const singleMatch = graduateScale.match(/(\d+)/)
  if (singleMatch) {
    const num = parseInt(singleMatch[1])
    if (num > 20000) return 92
    if (num > 10000) return 88
    if (num > 5000) return 85
    return 82
  }

  return 85 // 默认就业率
}

// 辅助函数：解析性别比例
function parseGenderRatio(maleFemaleRatio: string): { male: number, female: number } {
  if (!maleFemaleRatio || maleFemaleRatio === '0:0') {
    return { male: 50, female: 50 }
  }

  const match = maleFemaleRatio.match(/(\d+):(\d+)/)
  if (match) {
    const male = parseInt(match[1])
    const female = parseInt(match[2])
    const total = male + female
    if (total > 0) {
      return {
        male: Math.round((male / total) * 100),
        female: Math.round((female / total) * 100)
      }
    }
  }

  return { male: 50, female: 50 }
}

// 辅助函数：根据学科门类估算薪资
function estimateSalaryByCategory(category: string): number {
  const salaryMap: Record<string, number> = {
    '工学': 85000,
    '医学': 95000,
    '经济学': 90000,
    '管理学': 80000,
    '理学': 75000,
    '法学': 85000,
    '文学': 65000,
    '教育学': 60000,
    '艺术学': 70000,
    '农学': 55000,
    '历史学': 60000,
    '哲学': 65000
  }
  return salaryMap[category] || 70000
}

// 辅助函数：根据学科门类获取薪资范围
function getSalaryRangeByCategory(category: string): { min: number, max: number } {
  const rangeMap: Record<string, { min: number, max: number }> = {
    '工学': { min: 50000, max: 150000 },
    '医学': { min: 60000, max: 200000 },
    '经济学': { min: 55000, max: 180000 },
    '管理学': { min: 45000, max: 160000 },
    '理学': { min: 40000, max: 140000 },
    '法学': { min: 50000, max: 150000 },
    '文学': { min: 35000, max: 120000 },
    '教育学': { min: 35000, max: 100000 },
    '艺术学': { min: 30000, max: 150000 },
    '农学': { min: 30000, max: 100000 },
    '历史学': { min: 35000, max: 100000 },
    '哲学': { min: 35000, max: 120000 }
  }
  return rangeMap[category] || { min: 40000, max: 120000 }
}

// 辅助函数：根据学科门类获取主要就业行业
function getIndustriesByCategory(category: string): string[] {
  const industryMap: Record<string, string[]> = {
    '工学': ['制造业', '互联网', '建筑业', '电子信息'],
    '医学': ['医疗卫生', '制药', '医疗器械', '生物技术'],
    '经济学': ['金融', '银行', '证券', '保险'],
    '管理学': ['企业管理', '咨询', '人力资源', '市场营销'],
    '理学': ['科研院所', '教育', '技术服务', '环保'],
    '法学': ['律师事务所', '司法机关', '企业法务', '政府机关'],
    '文学': ['媒体', '出版', '教育', '文化传媒'],
    '教育学': ['教育', '培训', '公务员', '社会工作'],
    '艺术学': ['文化创意', '设计', '媒体', '娱乐'],
    '农学': ['农业', '林业', '畜牧业', '食品加工'],
    '历史学': ['教育', '文博', '出版', '政府机关'],
    '哲学': ['教育', '研究', '媒体', '政府机关']
  }
  return industryMap[category] || ['服务业', '制造业', '教育', '政府机关']
}

// 辅助函数：根据学科门类获取主要就业岗位
function getPositionsByCategory(category: string): string[] {
  const positionMap: Record<string, string[]> = {
    '工学': ['工程师', '技术员', '研发人员', '项目经理'],
    '医学': ['医师', '护士', '医学研究员', '医疗顾问'],
    '经济学': ['经济分析师', '金融顾问', '投资顾问', '银行职员'],
    '管理学': ['管理人员', '咨询师', '人事专员', '市场专员'],
    '理学': ['研究员', '实验员', '数据分析师', '教师'],
    '法学': ['律师', '法官', '检察官', '法务专员'],
    '文学': ['编辑', '记者', '翻译', '教师'],
    '教育学': ['教师', '教育管理者', '培训师', '心理咨询师'],
    '艺术学': ['设计师', '艺术家', '策展人', '创意总监'],
    '农学': ['农艺师', '兽医', '农业技术员', '农业管理员'],
    '历史学': ['教师', '研究员', '文物保护员', '档案管理员'],
    '哲学': ['教师', '研究员', '编辑', '思想工作者']
  }
  return positionMap[category] || ['专业技术人员', '管理人员', '教师', '公务员']
}

// 辅助函数：生成专业特色
function generateFeatures(major: any): string[] {
  const features: string[] = []

  if (major.isRecommended) {
    features.push('推荐专业')
  }

  // 根据毕业生规模判断
  if (major.graduateScale) {
    const scale = major.graduateScale
    if (scale.includes('10000') || scale.includes('12000')) {
      features.push('热门专业')
    } else if (scale.includes('50人以下') || scale.includes('100')) {
      features.push('小众专业')
    }
  }

  // 根据学科门类添加特色
  const categoryFeatures: Record<string, string[]> = {
    '工学': ['实践性强', '就业面广'],
    '医学': ['社会地位高', '专业性强'],
    '经济学': ['发展前景好', '薪资待遇高'],
    '管理学': ['应用性强', '发展空间大'],
    '理学': ['基础扎实', '深造机会多'],
    '法学': ['逻辑性强', '社会需求大'],
    '文学': ['人文素养高', '创造性强'],
    '教育学': ['社会贡献大', '工作稳定'],
    '艺术学': ['创意性强', '个性发展'],
    '农学': ['国家重视', '发展潜力大'],
    '历史学': ['文化底蕴深', '思维严谨'],
    '哲学': ['思辨能力强', '视野开阔']
  }

  const categorySpecific = categoryFeatures[major.disciplinaryCategory] || ['专业性强', '发展前景好']
  features.push(...categorySpecific)

  return features.slice(0, 4) // 最多返回4个特色
}

// 辅助函数：生成专业前景
function generateProspects(major: any): { trend: 'rising' | 'stable' | 'declining', demandLevel: 'high' | 'medium' | 'low', competitionLevel: 'high' | 'medium' | 'low' } {
  // 根据学科门类判断趋势
  const trendMap: Record<string, 'rising' | 'stable' | 'declining'> = {
    '工学': 'rising',
    '医学': 'rising',
    '经济学': 'stable',
    '管理学': 'stable',
    '理学': 'rising',
    '法学': 'stable',
    '文学': 'stable',
    '教育学': 'stable',
    '艺术学': 'rising',
    '农学': 'rising',
    '历史学': 'stable',
    '哲学': 'stable'
  }

  // 根据毕业生规模判断需求和竞争程度
  let demandLevel: 'high' | 'medium' | 'low' = 'medium'
  let competitionLevel: 'high' | 'medium' | 'low' = 'medium'

  if (major.graduateScale) {
    const scale = major.graduateScale
    if (scale.includes('10000') || scale.includes('12000')) {
      demandLevel = 'high'
      competitionLevel = 'high'
    } else if (scale.includes('50人以下') || scale.includes('100')) {
      demandLevel = 'low'
      competitionLevel = 'low'
    }
  }

  return {
    trend: trendMap[major.disciplinaryCategory] || 'stable',
    demandLevel,
    competitionLevel
  }
}

/**
 * 从API数据转换为组件所需的专业数据格式
 */
export function transformApiDataToMajor(apiNode: any): any {
  // 处理课程数据
  const courses = {
    core: [],
    elective: []
  }

  if (apiNode.courses && Array.isArray(apiNode.courses)) {
    // 按难度分类课程，难度4-5为核心课程，1-3为选修课程
    apiNode.courses.forEach((course: any) => {
      const difficulty = parseInt(course.courseDifficulty) || 3
      if (difficulty >= 4) {
        courses.core.push(course.courseName)
      } else {
        courses.elective.push(course.courseName)
      }
    })
  }

  return {
    id: apiNode.id?.toString() || apiNode.code || Math.random().toString(36).substr(2, 9),
    code: apiNode.code || '',
    name: apiNode.name,
    englishName: '', // API暂无此字段
    category: apiNode.disciplinaryCategory || extractCategoryFromName(apiNode.name),
    subCategory: apiNode.disciplinarySubCategory || extractSubCategoryFromName(apiNode.name),
    degree: getDegreeByCategoryAndLevel(apiNode.educationLevel),
    duration: getDurationByEducationLevel(apiNode.educationLevel),
    educationLevel: apiNode.educationLevel,
    description: apiNode.description || apiNode.majorIntroduction || `${apiNode.name}专业`,
    employment: apiNode.employment,
    genderRatio: apiNode.genderRatio,
    courses: courses,
    features: apiNode.features || ['就业前景好', '发展空间大'],
    prospects: apiNode.prospects,
    // 添加实际API字段
    graduateScale: apiNode.graduateScale,
    recommendSchools: apiNode.recommendSchools || [],
    careerDirection: apiNode.careerDirection,
    isRecommended: apiNode.isRecommended
  }
}

// 辅助函数：从专业名称提取学科门类
function extractCategoryFromName(name: string): string {
  const categoryMap: Record<string, string> = {
    '计算机': '工学',
    '软件': '工学',
    '电子': '工学',
    '机械': '工学',
    '土木': '工学',
    '化学': '理学',
    '数学': '理学',
    '物理': '理学',
    '生物': '理学',
    '医学': '医学',
    '临床': '医学',
    '护理': '医学',
    '经济': '经济学',
    '金融': '经济学',
    '管理': '管理学',
    '工商': '管理学',
    '会计': '管理学',
    '法学': '法学',
    '中文': '文学',
    '英语': '文学',
    '新闻': '文学',
    '教育': '教育学',
    '体育': '教育学',
    '艺术': '艺术学',
    '音乐': '艺术学',
    '美术': '艺术学',
    '设计': '艺术学'
  }
  
  for (const [keyword, category] of Object.entries(categoryMap)) {
    if (name.includes(keyword)) {
      return category
    }
  }
  
  return '工学' // 默认分类
}

// 辅助函数：从专业名称提取专业类别
function extractSubCategoryFromName(name: string): string {
  const subCategoryMap: Record<string, string> = {
    '计算机': '计算机类',
    '软件': '计算机类',
    '电子': '电子信息类',
    '通信': '电子信息类',
    '机械': '机械类',
    '土木': '土木类',
    '化学': '化学类',
    '数学': '数学类',
    '物理': '物理学类',
    '生物': '生物科学类',
    '医学': '临床医学类',
    '护理': '护理学类',
    '经济': '经济学类',
    '金融': '金融学类',
    '管理': '工商管理类',
    '会计': '工商管理类',
    '法学': '法学类',
    '中文': '中国语言文学类',
    '英语': '外国语言文学类',
    '新闻': '新闻传播学类',
    '教育': '教育学类',
    '体育': '体育学类',
    '艺术': '艺术学理论类',
    '音乐': '音乐与舞蹈学类',
    '美术': '美术学类',
    '设计': '设计学类'
  }
  
  for (const [keyword, subCategory] of Object.entries(subCategoryMap)) {
    if (name.includes(keyword)) {
      return subCategory
    }
  }
  
  return '其他类'
}

// 辅助函数：根据教育层次获取学位类型
function getDegreeByCategoryAndLevel(educationLevel: string): string {
  switch (educationLevel) {
    case '本科(普通)':
    case '本科(职业)':
      return '学士'
    case '专科(高职)':
      return '专科'
    default:
      return '学士'
  }
}

// 辅助函数：根据教育层次获取学制
function getDurationByEducationLevel(educationLevel: string): number {
  switch (educationLevel) {
    case '本科(普通)':
    case '本科(职业)':
      return 4
    case '专科(高职)':
      return 3
    default:
      return 4
  }
}

console.log('🔧 专业树形结构API服务已加载')
