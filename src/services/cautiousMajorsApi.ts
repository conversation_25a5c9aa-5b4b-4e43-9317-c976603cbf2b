// 慎选专业API服务
export interface CautiousMajorItem {
  id: number
  content: string | null
  type: string
  parentType: string
  question: string
  version: string | null
  createTime: number | null
  star: string | null
  ds: string
}

export interface CautiousMajorDetailItem {
  id: number
  questionId: number
  content: string
  type: string
  parentType: string
  question: string | null
  version: string | null
  createTime: number
  star: string | null
  ds: string
  dsCus: string
}

export interface CautiousMajorsListResponse {
  code: number
  data: {
    list: CautiousMajorItem[]
    total: number
  }
  msg: string
}

export interface CautiousMajorDetailResponse {
  code: number
  data: {
    list: CautiousMajorDetailItem[]
    total: number
  }
  msg: string
}

// API配置
const API_BASE_URL = 'https://m.kefeichangduo.top'

/**
 * 获取慎选专业列表
 */
export async function getCautiousMajorsList(
  pageNo: number = 1,
  pageSize: number = 1000
): Promise<CautiousMajorsListResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question/page?parentType=4&pageNo=${pageNo}&pageSize=${pageSize}`
    
    console.log('🔍 请求慎选专业列表:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: CautiousMajorsListResponse = await response.json()
    console.log('✅ 慎选专业列表响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取慎选专业列表失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 获取慎选专业详情
 */
export async function getCautiousMajorDetail(
  questionId: number,
  version: string = '',
  dsCus: string = ''
): Promise<CautiousMajorDetailResponse> {
  try {
    const url = `${API_BASE_URL}/admin-api/system/ai/normal-question-content/page?questionId=${questionId}&version=${version}&dsCus=${encodeURIComponent(dsCus)}`
    
    console.log('🔍 请求慎选专业详情:', url)
    console.log('📋 请求参数:', { questionId, version, dsCus })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      mode: 'cors'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: CautiousMajorDetailResponse = await response.json()
    console.log('✅ 慎选专业详情响应:', data)

    if (data.code !== 0) {
      throw new Error(`API Error: ${data.msg || '未知错误'}`)
    }

    return data
  } catch (error) {
    console.error('💥 获取慎选专业详情失败:', error)
    
    // 如果是CORS错误，提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
      throw new Error('网络请求失败，可能是CORS问题。请检查API服务器是否支持跨域请求。')
    }
    
    throw error
  }
}

/**
 * 格式化时间戳为可读日期
 */
export function formatTimestamp(timestamp: number | null): string {
  if (!timestamp) return '未知时间'
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 根据专业类型获取分类信息
 */
export function getCategoryInfo(type: string): { name: string; color: string; bgColor: string; icon: string } {
  const categoryMap: Record<string, { name: string; color: string; bgColor: string; icon: string }> = {
    '就业难类': { name: '就业难类', color: 'text-red-700', bgColor: 'bg-red-100', icon: '⚠️' },
    '基础科学类': { name: '基础科学类', color: 'text-orange-700', bgColor: 'bg-orange-100', icon: '🔬' },
    '工程类': { name: '工程类', color: 'text-yellow-700', bgColor: 'bg-yellow-100', icon: '🏗️' },
    '市场类': { name: '市场类', color: 'text-purple-700', bgColor: 'bg-purple-100', icon: '📈' },
    '设计类': { name: '设计类', color: 'text-pink-700', bgColor: 'bg-pink-100', icon: '🎨' },
    '管理类': { name: '管理类', color: 'text-indigo-700', bgColor: 'bg-indigo-100', icon: '📊' },
    '信息技术类': { name: '信息技术类', color: 'text-blue-700', bgColor: 'bg-blue-100', icon: '💻' }
  }
  
  return categoryMap[type] || { name: type, color: 'text-gray-700', bgColor: 'bg-gray-100', icon: '📚' }
}

/**
 * 获取所有分类统计
 */
export function getCategoryStats(majorItems: CautiousMajorItem[]): Array<{ id: string; name: string; count: number; icon: string }> {
  const categoryCount: Record<string, number> = {}
  
  majorItems.forEach(item => {
    categoryCount[item.type] = (categoryCount[item.type] || 0) + 1
  })
  
  const categories = [
    { id: 'all', name: '全部专业', count: majorItems.length, icon: '📋' }
  ]
  
  Object.entries(categoryCount).forEach(([type, count]) => {
    const categoryInfo = getCategoryInfo(type)
    categories.push({
      id: type,
      name: type,
      count,
      icon: categoryInfo.icon
    })
  })
  
  return categories
}

/**
 * 获取专业风险等级
 */
export function getRiskLevel(ds: string): { level: string; color: string; bgColor: string } {
  const riskFactors = [
    '就业困难', '招聘岗位少', '薪资低', '就业面窄', '转型难度大',
    'AI技术代替', '职业发展空间小', '行业周期处于衰退期'
  ]
  
  const riskCount = riskFactors.filter(factor => ds.includes(factor)).length
  
  if (riskCount >= 6) {
    return { level: '高风险', color: 'text-red-700', bgColor: 'bg-red-100' }
  } else if (riskCount >= 4) {
    return { level: '中风险', color: 'text-orange-700', bgColor: 'bg-orange-100' }
  } else {
    return { level: '低风险', color: 'text-yellow-700', bgColor: 'bg-yellow-100' }
  }
}

/**
 * 提取主要风险因素
 */
export function extractRiskFactors(ds: string): string[] {
  const allFactors = [
    '就业困难', '招聘岗位少', '薪资低', '就业面窄', '转型难度大',
    '学习能力要求高', '学习周期长考研考博', '学习内容理论性强',
    'AI技术代替就业岗位', '人脉资源要求高', '经验要求高',
    '名校背景要求高', '职业发展空间小', '行业周期处于衰退期'
  ]
  
  return allFactors.filter(factor => ds.includes(factor)).slice(0, 5) // 只显示前5个主要因素
}
