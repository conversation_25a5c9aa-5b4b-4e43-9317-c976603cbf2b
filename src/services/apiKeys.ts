// 咕咕数据API密钥管理器
// 每个接口都有不同的API密钥

export enum ApiType {
  SCHOOL = 'school',                    // 高校基础信息
  SCORE = 'score',                      // 一分一段
  MAJOR = 'major',                      // 历年高考专业录取数据
  MAJOR_DETAIL = 'major_detail',        // 大学高校专业数据
  CEELINE = 'ceeline',                  // 历年高校招生计划
  PROVINCE = 'province',                // 历年高考省录取分数线
  SCHOOL_SCORE = 'school_score',        // 历年高考高校录取分数线
  QS_RANKING = 'qs_ranking',            // 全球QS世界大学排名
  ONE_SCORE_ONE_SECTION = 'one_score_one_section'  // 历年高考一分一段数据
}

// API密钥映射
const API_KEYS: Record<ApiType, string> = {
  [ApiType.SCHOOL]: import.meta.env?.VITE_GUGUDATA_SCHOOL_APIKEY || '257KUWTX6ZUES8994QLLN5Z5PPLDTHV5',
  [ApiType.SCORE]: import.meta.env?.VITE_GUGUDATA_SCORE_APIKEY || 'VB8WXGDWJ47VAGW54MAVGYMGTBYZNJJ3',
  [ApiType.MAJOR]: import.meta.env?.VITE_GUGUDATA_MAJOR_APIKEY || 'JWZQJAFNR5LB7HP89R3R9CTUSX3PECKF',
  [ApiType.MAJOR_DETAIL]: import.meta.env?.VITE_GUGUDATA_MAJOR_DETAIL_APIKEY || 'F2G9U9BBNY3YNWSWC2CEXXBK62QJ6VC3',
  [ApiType.CEELINE]: import.meta.env?.VITE_GUGUDATA_CEELINE_APIKEY || '8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM',
  [ApiType.PROVINCE]: import.meta.env?.VITE_GUGUDATA_PROVINCE_APIKEY || 'GLGDHX8N6N29U7L75M6FBC4LPVQYM398',
  [ApiType.SCHOOL_SCORE]: import.meta.env?.VITE_GUGUDATA_SCHOOL_SCORE_APIKEY || 'E6X2G4QC2VC2LA3WN3K46KLCVAZKEXLM',
  [ApiType.QS_RANKING]: import.meta.env?.VITE_GUGUDATA_QS_RANKING_APIKEY || '3KPPMJEQK3EHL6PZDPXTGDEPMKQPZP4U',
  [ApiType.ONE_SCORE_ONE_SECTION]: import.meta.env?.VITE_GUGUDATA_ONE_SCORE_ONE_SECTION_APIKEY || 'VB8WXGDWJ47VAGW54MAVGYMGTBYZNJJ3'
}

// API接口路径映射
export const API_PATHS: Record<ApiType, string> = {
  [ApiType.SCHOOL]: '/location/college',                    // 高校基础信息
  [ApiType.SCORE]: '/score/segment',                        // 一分一段
  [ApiType.MAJOR]: '/admission/major',                      // 历年高考专业录取数据
  [ApiType.MAJOR_DETAIL]: '/college/major',                 // 大学高校专业数据
  [ApiType.CEELINE]: '/admission/plan',                     // 历年高校招生计划
  [ApiType.PROVINCE]: '/score/province',                    // 历年高考省录取分数线
  [ApiType.SCHOOL_SCORE]: '/score/college',                 // 历年高考高校录取分数线
  [ApiType.QS_RANKING]: '/ranking/qs',                      // 全球QS世界大学排名
  [ApiType.ONE_SCORE_ONE_SECTION]: '/metadata/ceeline/one-score-one-section'  // 历年高考一分一段数据
}

/**
 * 获取指定API类型的密钥
 * @param apiType API类型
 * @returns API密钥
 */
export function getApiKey(apiType: ApiType): string {
  const apiKey = API_KEYS[apiType]
  if (!apiKey) {
    throw new Error(`未找到 ${apiType} 接口的API密钥`)
  }
  return apiKey
}

/**
 * 获取指定API类型的接口路径
 * @param apiType API类型
 * @returns API路径
 */
export function getApiPath(apiType: ApiType): string {
  const apiPath = API_PATHS[apiType]
  if (!apiPath) {
    throw new Error(`未找到 ${apiType} 接口的API路径`)
  }
  return apiPath
}

/**
 * 检查所有API密钥是否已配置
 * @returns 配置状态报告
 */
export function checkApiKeysConfiguration(): {
  configured: ApiType[]
  missing: ApiType[]
  total: number
} {
  const configured: ApiType[] = []
  const missing: ApiType[] = []

  Object.values(ApiType).forEach(apiType => {
    const envKey = `VITE_GUGUDATA_${apiType.toUpperCase()}_APIKEY`
    if (import.meta.env?.[envKey]) {
      configured.push(apiType)
    } else {
      missing.push(apiType)
    }
  })

  return {
    configured,
    missing,
    total: Object.values(ApiType).length
  }
}

/**
 * 获取API密钥的显示信息（隐藏敏感部分）
 * @param apiType API类型
 * @returns 脱敏的API密钥信息
 */
export function getApiKeyInfo(apiType: ApiType): {
  type: ApiType
  hasKey: boolean
  preview: string
  source: 'env' | 'default'
} {
  const envKey = `VITE_GUGUDATA_${apiType.toUpperCase()}_APIKEY`
  const envValue = import.meta.env?.[envKey]
  const apiKey = getApiKey(apiType)
  
  return {
    type: apiType,
    hasKey: !!apiKey,
    preview: apiKey ? `${apiKey.substring(0, 8)}...` : '未配置',
    source: envValue ? 'env' : 'default'
  }
}

// 调试信息
console.log('🔑 API密钥配置状态:')
const configStatus = checkApiKeysConfiguration()
console.log(`✅ 已配置: ${configStatus.configured.length}/${configStatus.total}`)
console.log(`❌ 未配置: ${configStatus.missing.length}/${configStatus.total}`)

if (configStatus.missing.length > 0) {
  console.warn('⚠️ 以下API密钥使用默认值:', configStatus.missing)
}

// 输出所有API密钥信息
Object.values(ApiType).forEach(apiType => {
  const info = getApiKeyInfo(apiType)
  console.log(`🔑 ${apiType}: ${info.preview} (${info.source})`)
})
