// 专业相关API服务
import { ApiType, getApiKey, getApiPath } from './apiKeys'

// API配置
const isDevelopment = import.meta.env?.MODE === 'development' || typeof window !== 'undefined'
const API_BASE_URL = isDevelopment ? 'http://localhost:3001/api/gugudata' : 'https://api.gugudata.com'

// 专业录取数据接口
export interface MajorAdmissionFilters {
  keywords?: string      // 搜索关键字
  pagesize?: number      // 每页数据量
  pageindex?: number     // 页码
  province?: string      // 省份
  year?: string          // 年份
  schoolname?: string    // 学校名称
  majorname?: string     // 专业名称
}

// 高校专业数据接口
export interface CollegeMajorFilters {
  keywords?: string      // 搜索关键字
  pagesize?: number      // 每页数据量
  pageindex?: number     // 页码
  schoolname?: string    // 学校名称
  majortype?: string     // 专业类型
  degree?: string        // 学位类型
}

/**
 * 构建查询参数 - 专业录取数据
 */
function buildMajorAdmissionParams(filters: MajorAdmissionFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', getApiKey(ApiType.MAJOR))
  
  if (filters.keywords !== undefined) params.append('keywords', filters.keywords)
  if (filters.pagesize !== undefined) params.append('pagesize', filters.pagesize.toString())
  if (filters.pageindex !== undefined) params.append('pageindex', filters.pageindex.toString())
  if (filters.province) params.append('province', filters.province)
  if (filters.year) params.append('year', filters.year)
  if (filters.schoolname) params.append('schoolname', filters.schoolname)
  if (filters.majorname) params.append('majorname', filters.majorname)
  
  return params
}

/**
 * 构建查询参数 - 高校专业数据
 */
function buildCollegeMajorParams(filters: CollegeMajorFilters): URLSearchParams {
  const params = new URLSearchParams()
  params.append('appkey', getApiKey(ApiType.MAJOR_DETAIL))
  
  if (filters.keywords !== undefined) params.append('keywords', filters.keywords)
  if (filters.pagesize !== undefined) params.append('pagesize', filters.pagesize.toString())
  if (filters.pageindex !== undefined) params.append('pageindex', filters.pageindex.toString())
  if (filters.schoolname) params.append('schoolname', filters.schoolname)
  if (filters.majortype) params.append('majortype', filters.majortype)
  if (filters.degree) params.append('degree', filters.degree)
  
  return params
}

/**
 * 获取专业录取数据
 */
export async function getMajorAdmission(filters: MajorAdmissionFilters = {}): Promise<any> {
  try {
    const params = buildMajorAdmissionParams(filters)
    const apiPath = getApiPath(ApiType.MAJOR)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求专业录取数据:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 专业录取数据获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 专业录取数据获取失败:', error)
    throw error
  }
}

/**
 * 获取高校专业数据
 */
export async function getCollegeMajor(filters: CollegeMajorFilters = {}): Promise<any> {
  try {
    const params = buildCollegeMajorParams(filters)
    const apiPath = getApiPath(ApiType.MAJOR_DETAIL)
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求高校专业数据:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ 高校专业数据获取成功:', data)
    return data
  } catch (error) {
    console.error('❌ 高校专业数据获取失败:', error)
    throw error
  }
}

/**
 * 根据学校名称获取专业列表
 */
export async function getMajorsBySchool(schoolName: string, page: number = 1, pageSize: number = 20): Promise<any> {
  return getCollegeMajor({
    schoolname: schoolName,
    pageindex: page,
    pagesize: pageSize
  })
}

/**
 * 根据专业名称搜索相关学校
 */
export async function getSchoolsByMajor(majorName: string, page: number = 1, pageSize: number = 20): Promise<any> {
  return getCollegeMajor({
    keywords: majorName,
    pageindex: page,
    pagesize: pageSize
  })
}

/**
 * 获取专业录取分数线
 */
export async function getMajorScores(
  schoolName?: string, 
  majorName?: string, 
  province?: string, 
  year?: string,
  page: number = 1, 
  pageSize: number = 20
): Promise<any> {
  return getMajorAdmission({
    schoolname: schoolName,
    majorname: majorName,
    province: province,
    year: year,
    pageindex: page,
    pagesize: pageSize
  })
}

console.log('🔧 专业API服务已加载')
console.log('- 专业录取API密钥:', getApiKey(ApiType.MAJOR).substring(0, 8) + '...')
console.log('- 高校专业API密钥:', getApiKey(ApiType.MAJOR_DETAIL).substring(0, 8) + '...')
