import type {
  QSRankingResponse,
  QSRankingQuery,
  QSUniversity,
  ProcessedQSUniversity,
  CountryRegion
} from '../types/qs-ranking'
import { TranslationService } from './translationService'

class QSRankingService {
  private baseUrl = 'https://api.gugudata.com/metadata/global-university-ranking'
  private appKey = '3KPPMJEQK3EHL6PZDPXTGDEPMKQPZP4U' // This should be replaced with actual API key

  /**
   * Fetch QS ranking data from GuGuData API
   */
  async fetchQSRanking(query: QSRankingQuery = {}): Promise<QSRankingResponse> {
    const params = new URLSearchParams({
      appkey: this.appKey,
      name: query.name || '',
      pageIndex: (query.pageIndex || 1).toString(),
      pageSize: (query.pageSize || 20).toString()
    })

    try {
      const response = await fetch(`${this.baseUrl}?${params}`)
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`)
      }

      const data: QSRankingResponse = await response.json()
      
      if (data.DataStatus.StatusCode !== 100) {
        throw new Error(`API error: ${data.DataStatus.StatusDescription}`)
      }

      return data
    } catch (error) {
      console.error('Failed to fetch QS ranking data:', error)
      // Return mock data for development/demo purposes
      return this.getMockData(query)
    }
  }

  /**
   * Process raw API data into display-friendly format
   */
  processUniversityData(rawData: QSUniversity[]): ProcessedQSUniversity[] {
    return rawData.map(university => ({
      id: university.Id,
      name: university.UniversityName,
      nameZh: TranslationService.translateUniversityName(university.UniversityName),
      country: university.Country,
      countryZh: TranslationService.translateCountryName(university.Country),
      region: university.Region,
      regionZh: TranslationService.translateRegionName(university.Region),
      city: university.City,
      cityZh: TranslationService.translateCityName(university.City),
      logoUrl: university.LogoUrl,
      rank: this.parseRank(university.Rank),
      rankDisplay: university.Rank,
      scores: {
        academicReputation: {
          rank: this.parseRank(university.AcademicReputationRank),
          score: this.parseScore(university.AcademicReputationScore)
        },
        citationsPerFaculty: {
          rank: this.parseRank(university.CitationsPerFacultyRank),
          score: this.parseScore(university.CitationsPerFacultyScore)
        },
        facultyStudentRatio: {
          rank: this.parseRank(university.FacultyStudentRatioRank),
          score: this.parseScore(university.FacultyStudentRatioScore)
        },
        employerReputation: {
          rank: this.parseRank(university.EmployerReputationRank),
          score: this.parseScore(university.EmployerReputationScore)
        },
        employmentOutcomes: {
          rank: this.parseRank(university.EmploymentOutcomesRank),
          score: this.parseScore(university.EmploymentOutcomesScore)
        },
        internationalStudentRatio: {
          rank: this.parseRank(university.InternationalStudentRatioRank),
          score: this.parseScore(university.InternationalStudentRatioScore)
        },
        internationalResearchNetwork: {
          rank: this.parseRank(university.InternationalResearchNetworkRank),
          score: this.parseScore(university.InternationalResearchNetworkScore)
        },
        internationalFacultyRatio: {
          rank: this.parseRank(university.InternationalFacultyRatioRank),
          score: this.parseScore(university.InternationalFacultyRatioScore)
        },
        sustainability: {
          rank: this.parseRank(university.SustainabilityRank),
          score: this.parseScore(university.SustainabilityScore)
        }
      }
    }))
  }

  /**
   * Parse rank string to number (handle ranges like "1-10")
   */
  private parseRank(rankStr: string): number {
    if (!rankStr || rankStr === '-' || rankStr === 'N/A') return 0
    
    // Handle ranges like "1-10", take the first number
    const match = rankStr.match(/^(\d+)/)
    return match ? parseInt(match[1], 10) : 0
  }

  /**
   * Parse score string to number
   */
  private parseScore(scoreStr: string): number {
    if (!scoreStr || scoreStr === '-' || scoreStr === 'N/A') return 0
    
    const score = parseFloat(scoreStr)
    return isNaN(score) ? 0 : score
  }

  /**
   * Get available countries and regions for filtering
   */
  getAvailableCountriesAndRegions(): CountryRegion[] {
    // This would ideally come from the API, but for now we'll use common ones
    return [
      {
        country: 'United States',
        regions: ['North America']
      },
      {
        country: 'United Kingdom',
        regions: ['Europe']
      },
      {
        country: 'China',
        regions: ['Asia']
      },
      {
        country: 'Australia',
        regions: ['Oceania']
      },
      {
        country: 'Canada',
        regions: ['North America']
      },
      {
        country: 'Germany',
        regions: ['Europe']
      },
      {
        country: 'Japan',
        regions: ['Asia']
      },
      {
        country: 'France',
        regions: ['Europe']
      },
      {
        country: 'Singapore',
        regions: ['Asia']
      },
      {
        country: 'Switzerland',
        regions: ['Europe']
      }
    ]
  }

  /**
   * Mock data for development/demo purposes
   */
  private getMockData(query: QSRankingQuery): QSRankingResponse {
    const mockUniversities: QSUniversity[] = [
      {
        Id: '1',
        UniversityName: 'Massachusetts Institute of Technology (MIT)',
        Region: 'North America',
        Country: 'United States',
        City: 'Cambridge',
        LogoUrl: 'https://via.placeholder.com/100x100?text=MIT',
        Rank: '1',
        AcademicReputationRank: '1',
        AcademicReputationScore: '100.0',
        CitationsPerFacultyRank: '1',
        CitationsPerFacultyScore: '100.0',
        FacultyStudentRatioRank: '5',
        FacultyStudentRatioScore: '100.0',
        EmployerReputationRank: '1',
        EmployerReputationScore: '100.0',
        EmploymentOutcomesRank: '1',
        EmploymentOutcomesScore: '100.0',
        InternationalStudentRatioRank: '15',
        InternationalStudentRatioScore: '90.5',
        InternationalResearchNetworkRank: '2',
        InternationalResearchNetworkScore: '98.2',
        InternationalFacultyRatioRank: '20',
        InternationalFacultyRatioScore: '85.3',
        SustainabilityRank: '10',
        SustainabilityScore: '92.1'
      },
      {
        Id: '2',
        UniversityName: 'University of Cambridge',
        Region: 'Europe',
        Country: 'United Kingdom',
        City: 'Cambridge',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Cambridge',
        Rank: '2',
        AcademicReputationRank: '2',
        AcademicReputationScore: '99.2',
        CitationsPerFacultyRank: '8',
        CitationsPerFacultyScore: '95.8',
        FacultyStudentRatioRank: '3',
        FacultyStudentRatioScore: '99.5',
        EmployerReputationRank: '3',
        EmployerReputationScore: '98.9',
        EmploymentOutcomesRank: '2',
        EmploymentOutcomesScore: '99.1',
        InternationalStudentRatioRank: '25',
        InternationalStudentRatioScore: '88.7',
        InternationalResearchNetworkRank: '1',
        InternationalResearchNetworkScore: '100.0',
        InternationalFacultyRatioRank: '18',
        InternationalFacultyRatioScore: '87.2',
        SustainabilityRank: '5',
        SustainabilityScore: '95.8'
      },
      {
        Id: '3',
        UniversityName: 'Stanford University',
        Region: 'North America',
        Country: 'United States',
        City: 'Stanford',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Stanford',
        Rank: '3',
        AcademicReputationRank: '3',
        AcademicReputationScore: '98.5',
        CitationsPerFacultyRank: '2',
        CitationsPerFacultyScore: '99.1',
        FacultyStudentRatioRank: '8',
        FacultyStudentRatioScore: '96.2',
        EmployerReputationRank: '2',
        EmployerReputationScore: '99.5',
        EmploymentOutcomesRank: '3',
        EmploymentOutcomesScore: '98.7',
        InternationalStudentRatioRank: '22',
        InternationalStudentRatioScore: '89.3',
        InternationalResearchNetworkRank: '5',
        InternationalResearchNetworkScore: '95.8',
        InternationalFacultyRatioRank: '25',
        InternationalFacultyRatioScore: '83.7',
        SustainabilityRank: '8',
        SustainabilityScore: '93.5'
      },
      {
        Id: '4',
        UniversityName: 'University of Oxford',
        Region: 'Europe',
        Country: 'United Kingdom',
        City: 'Oxford',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Oxford',
        Rank: '4',
        AcademicReputationRank: '4',
        AcademicReputationScore: '98.1',
        CitationsPerFacultyRank: '12',
        CitationsPerFacultyScore: '93.2',
        FacultyStudentRatioRank: '2',
        FacultyStudentRatioScore: '99.8',
        EmployerReputationRank: '4',
        EmployerReputationScore: '98.2',
        EmploymentOutcomesRank: '4',
        EmploymentOutcomesScore: '98.3',
        InternationalStudentRatioRank: '18',
        InternationalStudentRatioScore: '91.2',
        InternationalResearchNetworkRank: '3',
        InternationalResearchNetworkScore: '97.5',
        InternationalFacultyRatioRank: '15',
        InternationalFacultyRatioScore: '88.9',
        SustainabilityRank: '3',
        SustainabilityScore: '96.7'
      },
      {
        Id: '5',
        UniversityName: 'Harvard University',
        Region: 'North America',
        Country: 'United States',
        City: 'Cambridge',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Harvard',
        Rank: '5',
        AcademicReputationRank: '5',
        AcademicReputationScore: '97.8',
        CitationsPerFacultyRank: '6',
        CitationsPerFacultyScore: '96.5',
        FacultyStudentRatioRank: '12',
        FacultyStudentRatioScore: '94.1',
        EmployerReputationRank: '5',
        EmployerReputationScore: '97.9',
        EmploymentOutcomesRank: '5',
        EmploymentOutcomesScore: '98.0',
        InternationalStudentRatioRank: '28',
        InternationalStudentRatioScore: '87.5',
        InternationalResearchNetworkRank: '4',
        InternationalResearchNetworkScore: '96.3',
        InternationalFacultyRatioRank: '30',
        InternationalFacultyRatioScore: '81.2',
        SustainabilityRank: '12',
        SustainabilityScore: '91.8'
      },
      {
        Id: '6',
        UniversityName: 'California Institute of Technology (Caltech)',
        Region: 'North America',
        Country: 'United States',
        City: 'Pasadena',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Caltech',
        Rank: '6',
        AcademicReputationRank: '6',
        AcademicReputationScore: '97.2',
        CitationsPerFacultyRank: '3',
        CitationsPerFacultyScore: '98.7',
        FacultyStudentRatioRank: '1',
        FacultyStudentRatioScore: '100.0',
        EmployerReputationRank: '8',
        EmployerReputationScore: '95.8',
        EmploymentOutcomesRank: '6',
        EmploymentOutcomesScore: '97.5',
        InternationalStudentRatioRank: '35',
        InternationalStudentRatioScore: '84.2',
        InternationalResearchNetworkRank: '8',
        InternationalResearchNetworkScore: '93.7',
        InternationalFacultyRatioRank: '40',
        InternationalFacultyRatioScore: '78.5',
        SustainabilityRank: '15',
        SustainabilityScore: '90.3'
      },
      // 添加更多模拟数据以测试分页
      {
        Id: '7',
        UniversityName: 'University College London (UCL)',
        Region: 'Europe',
        Country: 'United Kingdom',
        City: 'London',
        LogoUrl: 'https://via.placeholder.com/100x100?text=UCL',
        Rank: '7',
        AcademicReputationRank: '7',
        AcademicReputationScore: '96.8',
        CitationsPerFacultyRank: '15',
        CitationsPerFacultyScore: '91.2',
        FacultyStudentRatioRank: '18',
        FacultyStudentRatioScore: '89.5',
        EmployerReputationRank: '6',
        EmployerReputationScore: '97.1',
        EmploymentOutcomesRank: '7',
        EmploymentOutcomesScore: '96.8',
        InternationalStudentRatioRank: '8',
        InternationalStudentRatioScore: '95.2',
        InternationalResearchNetworkRank: '6',
        InternationalResearchNetworkScore: '94.7',
        InternationalFacultyRatioRank: '12',
        InternationalFacultyRatioScore: '90.8',
        SustainabilityRank: '18',
        SustainabilityScore: '89.2'
      },
      {
        Id: '8',
        UniversityName: 'Imperial College London',
        Region: 'Europe',
        Country: 'United Kingdom',
        City: 'London',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Imperial',
        Rank: '8',
        AcademicReputationRank: '8',
        AcademicReputationScore: '96.2',
        CitationsPerFacultyRank: '4',
        CitationsPerFacultyScore: '97.8',
        FacultyStudentRatioRank: '15',
        FacultyStudentRatioScore: '91.3',
        EmployerReputationRank: '7',
        EmployerReputationScore: '96.5',
        EmploymentOutcomesRank: '8',
        EmploymentOutcomesScore: '96.2',
        InternationalStudentRatioRank: '5',
        InternationalStudentRatioScore: '97.8',
        InternationalResearchNetworkRank: '7',
        InternationalResearchNetworkScore: '94.2',
        InternationalFacultyRatioRank: '8',
        InternationalFacultyRatioScore: '93.5',
        SustainabilityRank: '22',
        SustainabilityScore: '87.8'
      },
      {
        Id: '9',
        UniversityName: 'ETH Zurich',
        Region: 'Europe',
        Country: 'Switzerland',
        City: 'Zurich',
        LogoUrl: 'https://via.placeholder.com/100x100?text=ETH',
        Rank: '9',
        AcademicReputationRank: '9',
        AcademicReputationScore: '95.8',
        CitationsPerFacultyRank: '7',
        CitationsPerFacultyScore: '96.2',
        FacultyStudentRatioRank: '6',
        FacultyStudentRatioScore: '97.8',
        EmployerReputationRank: '9',
        EmployerReputationScore: '95.2',
        EmploymentOutcomesRank: '9',
        EmploymentOutcomesScore: '95.8',
        InternationalStudentRatioRank: '12',
        InternationalStudentRatioScore: '92.8',
        InternationalResearchNetworkRank: '9',
        InternationalResearchNetworkScore: '93.5',
        InternationalFacultyRatioRank: '6',
        InternationalFacultyRatioScore: '95.2',
        SustainabilityRank: '6',
        SustainabilityScore: '95.2'
      },
      {
        Id: '10',
        UniversityName: 'University of Chicago',
        Region: 'North America',
        Country: 'United States',
        City: 'Chicago',
        LogoUrl: 'https://via.placeholder.com/100x100?text=UChicago',
        Rank: '10',
        AcademicReputationRank: '10',
        AcademicReputationScore: '95.2',
        CitationsPerFacultyRank: '18',
        CitationsPerFacultyScore: '89.8',
        FacultyStudentRatioRank: '4',
        FacultyStudentRatioScore: '98.5',
        EmployerReputationRank: '10',
        EmployerReputationScore: '94.8',
        EmploymentOutcomesRank: '10',
        EmploymentOutcomesScore: '95.2',
        InternationalStudentRatioRank: '32',
        InternationalStudentRatioScore: '85.8',
        InternationalResearchNetworkRank: '12',
        InternationalResearchNetworkScore: '91.8',
        InternationalFacultyRatioRank: '35',
        InternationalFacultyRatioScore: '79.2',
        SustainabilityRank: '25',
        SustainabilityScore: '86.5'
      },
      {
        Id: '11',
        UniversityName: 'National University of Singapore (NUS)',
        Region: 'Asia',
        Country: 'Singapore',
        City: 'Singapore',
        LogoUrl: 'https://via.placeholder.com/100x100?text=NUS',
        Rank: '11',
        AcademicReputationRank: '11',
        AcademicReputationScore: '94.8',
        CitationsPerFacultyRank: '22',
        CitationsPerFacultyScore: '87.5',
        FacultyStudentRatioRank: '25',
        FacultyStudentRatioScore: '85.2',
        EmployerReputationRank: '11',
        EmployerReputationScore: '94.2',
        EmploymentOutcomesRank: '11',
        EmploymentOutcomesScore: '94.8',
        InternationalStudentRatioRank: '3',
        InternationalStudentRatioScore: '98.5',
        InternationalResearchNetworkRank: '10',
        InternationalResearchNetworkScore: '93.2',
        InternationalFacultyRatioRank: '4',
        InternationalFacultyRatioScore: '96.8',
        SustainabilityRank: '8',
        SustainabilityScore: '94.2'
      },
      {
        Id: '12',
        UniversityName: 'Peking University',
        Region: 'Asia',
        Country: 'China',
        City: 'Beijing',
        LogoUrl: 'https://via.placeholder.com/100x100?text=PKU',
        Rank: '12',
        AcademicReputationRank: '12',
        AcademicReputationScore: '94.2',
        CitationsPerFacultyRank: '45',
        CitationsPerFacultyScore: '72.8',
        FacultyStudentRatioRank: '35',
        FacultyStudentRatioScore: '78.5',
        EmployerReputationRank: '12',
        EmployerReputationScore: '93.8',
        EmploymentOutcomesRank: '12',
        EmploymentOutcomesScore: '94.2',
        InternationalStudentRatioRank: '85',
        InternationalStudentRatioScore: '45.2',
        InternationalResearchNetworkRank: '25',
        InternationalResearchNetworkScore: '85.8',
        InternationalFacultyRatioRank: '95',
        InternationalFacultyRatioScore: '35.8',
        SustainabilityRank: '35',
        SustainabilityScore: '78.5'
      },
      // 添加更多数据确保分页功能可见
      {
        Id: '13',
        UniversityName: 'University of Pennsylvania',
        Region: 'North America',
        Country: 'United States',
        City: 'Philadelphia',
        LogoUrl: 'https://via.placeholder.com/100x100?text=UPenn',
        Rank: '13',
        AcademicReputationRank: '13',
        AcademicReputationScore: '93.8',
        CitationsPerFacultyRank: '25',
        CitationsPerFacultyScore: '86.2',
        FacultyStudentRatioRank: '20',
        FacultyStudentRatioScore: '88.5',
        EmployerReputationRank: '13',
        EmployerReputationScore: '93.2',
        EmploymentOutcomesRank: '13',
        EmploymentOutcomesScore: '93.8',
        InternationalStudentRatioRank: '45',
        InternationalStudentRatioScore: '78.2',
        InternationalResearchNetworkRank: '15',
        InternationalResearchNetworkScore: '90.5',
        InternationalFacultyRatioRank: '42',
        InternationalFacultyRatioScore: '76.8',
        SustainabilityRank: '28',
        SustainabilityScore: '84.2'
      },
      {
        Id: '14',
        UniversityName: 'Tsinghua University',
        Region: 'Asia',
        Country: 'China',
        City: 'Beijing',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Tsinghua',
        Rank: '14',
        AcademicReputationRank: '14',
        AcademicReputationScore: '93.2',
        CitationsPerFacultyRank: '48',
        CitationsPerFacultyScore: '71.5',
        FacultyStudentRatioRank: '38',
        FacultyStudentRatioScore: '76.8',
        EmployerReputationRank: '14',
        EmployerReputationScore: '92.8',
        EmploymentOutcomesRank: '14',
        EmploymentOutcomesScore: '93.2',
        InternationalStudentRatioRank: '88',
        InternationalStudentRatioScore: '42.8',
        InternationalResearchNetworkRank: '28',
        InternationalResearchNetworkScore: '84.2',
        InternationalFacultyRatioRank: '98',
        InternationalFacultyRatioScore: '32.5',
        SustainabilityRank: '38',
        SustainabilityScore: '76.8'
      },
      {
        Id: '15',
        UniversityName: 'University of Edinburgh',
        Region: 'Europe',
        Country: 'United Kingdom',
        City: 'Edinburgh',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Edinburgh',
        Rank: '15',
        AcademicReputationRank: '15',
        AcademicReputationScore: '92.8',
        CitationsPerFacultyRank: '32',
        CitationsPerFacultyScore: '82.5',
        FacultyStudentRatioRank: '28',
        FacultyStudentRatioScore: '83.2',
        EmployerReputationRank: '15',
        EmployerReputationScore: '92.2',
        EmploymentOutcomesRank: '15',
        EmploymentOutcomesScore: '92.8',
        InternationalStudentRatioRank: '18',
        InternationalStudentRatioScore: '90.5',
        InternationalResearchNetworkRank: '18',
        InternationalResearchNetworkScore: '89.2',
        InternationalFacultyRatioRank: '22',
        InternationalFacultyRatioScore: '85.8',
        SustainabilityRank: '12',
        SustainabilityScore: '91.5'
      },
      {
        Id: '16',
        UniversityName: 'École Polytechnique Fédérale de Lausanne (EPFL)',
        Region: 'Europe',
        Country: 'Switzerland',
        City: 'Lausanne',
        LogoUrl: 'https://via.placeholder.com/100x100?text=EPFL',
        Rank: '16',
        AcademicReputationRank: '16',
        AcademicReputationScore: '92.2',
        CitationsPerFacultyRank: '12',
        CitationsPerFacultyScore: '92.8',
        FacultyStudentRatioRank: '8',
        FacultyStudentRatioScore: '96.5',
        EmployerReputationRank: '16',
        EmployerReputationScore: '91.8',
        EmploymentOutcomesRank: '16',
        EmploymentOutcomesScore: '92.2',
        InternationalStudentRatioRank: '6',
        InternationalStudentRatioScore: '96.8',
        InternationalResearchNetworkRank: '11',
        InternationalResearchNetworkScore: '92.5',
        InternationalFacultyRatioRank: '5',
        InternationalFacultyRatioScore: '95.8',
        SustainabilityRank: '9',
        SustainabilityScore: '93.8'
      },
      {
        Id: '17',
        UniversityName: 'Princeton University',
        Region: 'North America',
        Country: 'United States',
        City: 'Princeton',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Princeton',
        Rank: '17',
        AcademicReputationRank: '17',
        AcademicReputationScore: '91.8',
        CitationsPerFacultyRank: '28',
        CitationsPerFacultyScore: '84.8',
        FacultyStudentRatioRank: '2',
        FacultyStudentRatioScore: '99.2',
        EmployerReputationRank: '17',
        EmployerReputationScore: '91.2',
        EmploymentOutcomesRank: '17',
        EmploymentOutcomesScore: '91.8',
        InternationalStudentRatioRank: '52',
        InternationalStudentRatioScore: '72.8',
        InternationalResearchNetworkRank: '22',
        InternationalResearchNetworkScore: '87.5',
        InternationalFacultyRatioRank: '48',
        InternationalFacultyRatioScore: '73.2',
        SustainabilityRank: '32',
        SustainabilityScore: '81.5'
      },
      {
        Id: '18',
        UniversityName: 'Yale University',
        Region: 'North America',
        Country: 'United States',
        City: 'New Haven',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Yale',
        Rank: '18',
        AcademicReputationRank: '18',
        AcademicReputationScore: '91.2',
        CitationsPerFacultyRank: '35',
        CitationsPerFacultyScore: '81.2',
        FacultyStudentRatioRank: '7',
        FacultyStudentRatioScore: '97.2',
        EmployerReputationRank: '18',
        EmployerReputationScore: '90.8',
        EmploymentOutcomesRank: '18',
        EmploymentOutcomesScore: '91.2',
        InternationalStudentRatioRank: '48',
        InternationalStudentRatioScore: '75.2',
        InternationalResearchNetworkRank: '25',
        InternationalResearchNetworkScore: '86.2',
        InternationalFacultyRatioRank: '45',
        InternationalFacultyRatioScore: '74.8',
        SustainabilityRank: '28',
        SustainabilityScore: '84.2'
      },
      {
        Id: '19',
        UniversityName: 'Nanyang Technological University (NTU)',
        Region: 'Asia',
        Country: 'Singapore',
        City: 'Singapore',
        LogoUrl: 'https://via.placeholder.com/100x100?text=NTU',
        Rank: '19',
        AcademicReputationRank: '19',
        AcademicReputationScore: '90.8',
        CitationsPerFacultyRank: '18',
        CitationsPerFacultyScore: '89.2',
        FacultyStudentRatioRank: '32',
        FacultyStudentRatioScore: '81.8',
        EmployerReputationRank: '19',
        EmployerReputationScore: '90.2',
        EmploymentOutcomesRank: '19',
        EmploymentOutcomesScore: '90.8',
        InternationalStudentRatioRank: '4',
        InternationalStudentRatioScore: '98.2',
        InternationalResearchNetworkRank: '8',
        InternationalResearchNetworkScore: '93.8',
        InternationalFacultyRatioRank: '3',
        InternationalFacultyRatioScore: '97.2',
        SustainabilityRank: '15',
        SustainabilityScore: '90.5'
      },
      {
        Id: '20',
        UniversityName: 'Cornell University',
        Region: 'North America',
        Country: 'United States',
        City: 'Ithaca',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Cornell',
        Rank: '20',
        AcademicReputationRank: '20',
        AcademicReputationScore: '90.2',
        CitationsPerFacultyRank: '42',
        CitationsPerFacultyScore: '78.5',
        FacultyStudentRatioRank: '22',
        FacultyStudentRatioScore: '87.2',
        EmployerReputationRank: '20',
        EmployerReputationScore: '89.8',
        EmploymentOutcomesRank: '20',
        EmploymentOutcomesScore: '90.2',
        InternationalStudentRatioRank: '38',
        InternationalStudentRatioScore: '82.5',
        InternationalResearchNetworkRank: '28',
        InternationalResearchNetworkScore: '84.8',
        InternationalFacultyRatioRank: '38',
        InternationalFacultyRatioScore: '78.2',
        SustainabilityRank: '22',
        SustainabilityScore: '87.8'
      },
      // 添加更多数据以测试分页功能（21-40）
      {
        Id: '21',
        UniversityName: 'University of Hong Kong (HKU)',
        Region: 'Asia',
        Country: 'Hong Kong',
        City: 'Hong Kong',
        LogoUrl: 'https://via.placeholder.com/100x100?text=HKU',
        Rank: '21',
        AcademicReputationRank: '21',
        AcademicReputationScore: '89.8',
        CitationsPerFacultyRank: '38',
        CitationsPerFacultyScore: '79.2',
        FacultyStudentRatioRank: '35',
        FacultyStudentRatioScore: '79.8',
        EmployerReputationRank: '21',
        EmployerReputationScore: '89.2',
        EmploymentOutcomesRank: '21',
        EmploymentOutcomesScore: '89.8',
        InternationalStudentRatioRank: '2',
        InternationalStudentRatioScore: '99.2',
        InternationalResearchNetworkRank: '20',
        InternationalResearchNetworkScore: '88.5',
        InternationalFacultyRatioRank: '2',
        InternationalFacultyRatioScore: '98.5',
        SustainabilityRank: '18',
        SustainabilityScore: '89.5'
      },
      {
        Id: '22',
        UniversityName: 'Columbia University',
        Region: 'North America',
        Country: 'United States',
        City: 'New York',
        LogoUrl: 'https://via.placeholder.com/100x100?text=Columbia',
        Rank: '22',
        AcademicReputationRank: '22',
        AcademicReputationScore: '89.2',
        CitationsPerFacultyRank: '45',
        CitationsPerFacultyScore: '76.8',
        FacultyStudentRatioRank: '18',
        FacultyStudentRatioScore: '89.2',
        EmployerReputationRank: '22',
        EmployerReputationScore: '88.8',
        EmploymentOutcomesRank: '22',
        EmploymentOutcomesScore: '89.2',
        InternationalStudentRatioRank: '42',
        InternationalStudentRatioScore: '79.8',
        InternationalResearchNetworkRank: '32',
        InternationalResearchNetworkScore: '82.5',
        InternationalFacultyRatioRank: '52',
        InternationalFacultyRatioScore: '71.2',
        SustainabilityRank: '35',
        SustainabilityScore: '79.8'
      },
      {
        Id: '23',
        UniversityName: 'University of Tokyo',
        Region: 'Asia',
        Country: 'Japan',
        City: 'Tokyo',
        LogoUrl: 'https://via.placeholder.com/100x100?text=UTokyo',
        Rank: '23',
        AcademicReputationRank: '23',
        AcademicReputationScore: '88.8',
        CitationsPerFacultyRank: '52',
        CitationsPerFacultyScore: '69.8',
        FacultyStudentRatioRank: '42',
        FacultyStudentRatioScore: '75.2',
        EmployerReputationRank: '23',
        EmployerReputationScore: '88.2',
        EmploymentOutcomesRank: '23',
        EmploymentOutcomesScore: '88.8',
        InternationalStudentRatioRank: '95',
        InternationalStudentRatioScore: '32.8',
        InternationalResearchNetworkRank: '45',
        InternationalResearchNetworkScore: '76.2',
        InternationalFacultyRatioRank: '98',
        InternationalFacultyRatioScore: '28.5',
        SustainabilityRank: '42',
        SustainabilityScore: '75.8'
      },
      {
        Id: '24',
        UniversityName: 'Johns Hopkins University',
        Region: 'North America',
        Country: 'United States',
        City: 'Baltimore',
        LogoUrl: 'https://via.placeholder.com/100x100?text=JHU',
        Rank: '24',
        AcademicReputationRank: '24',
        AcademicReputationScore: '88.2',
        CitationsPerFacultyRank: '22',
        CitationsPerFacultyScore: '87.2',
        FacultyStudentRatioRank: '25',
        FacultyStudentRatioScore: '85.8',
        EmployerReputationRank: '24',
        EmployerReputationScore: '87.8',
        EmploymentOutcomesRank: '24',
        EmploymentOutcomesScore: '88.2',
        InternationalStudentRatioRank: '35',
        InternationalStudentRatioScore: '83.2',
        InternationalResearchNetworkRank: '18',
        InternationalResearchNetworkScore: '89.8',
        InternationalFacultyRatioRank: '32',
        InternationalFacultyRatioScore: '82.5',
        SustainabilityRank: '28',
        SustainabilityScore: '84.8'
      },
      {
        Id: '25',
        UniversityName: 'University of Michigan',
        Region: 'North America',
        Country: 'United States',
        City: 'Ann Arbor',
        LogoUrl: 'https://via.placeholder.com/100x100?text=UMich',
        Rank: '25',
        AcademicReputationRank: '25',
        AcademicReputationScore: '87.8',
        CitationsPerFacultyRank: '48',
        CitationsPerFacultyScore: '72.5',
        FacultyStudentRatioRank: '38',
        FacultyStudentRatioScore: '77.8',
        EmployerReputationRank: '25',
        EmployerReputationScore: '87.2',
        EmploymentOutcomesRank: '25',
        EmploymentOutcomesScore: '87.8',
        InternationalStudentRatioRank: '58',
        InternationalStudentRatioScore: '68.5',
        InternationalResearchNetworkRank: '35',
        InternationalResearchNetworkScore: '81.2',
        InternationalFacultyRatioRank: '65',
        InternationalFacultyRatioScore: '65.8',
        SustainabilityRank: '32',
        SustainabilityScore: '82.2'
      }
    ]

    // Filter by name if provided
    let filteredData = mockUniversities
    if (query.name) {
      filteredData = mockUniversities.filter(uni =>
        uni.UniversityName.toLowerCase().includes(query.name!.toLowerCase())
      )
    }

    // 实现服务端分页
    const totalCount = filteredData.length
    const pageIndex = query.pageIndex || 1
    const pageSize = query.pageSize || 20
    const startIndex = (pageIndex - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      DataStatus: {
        RequestParameter: JSON.stringify(query),
        StatusCode: 100,
        StatusDescription: 'Success',
        ResponseDateTime: new Date().toISOString(),
        DataTotalCount: totalCount  // 返回总记录数，不是当前页的记录数
      },
      Data: paginatedData  // 返回当前页的数据
    }
  }

  /**
   * Set API key (for configuration)
   */
  setApiKey(apiKey: string): void {
    this.appKey = apiKey
  }

  /**
   * Get current API key
   */
  getApiKey(): string {
    return this.appKey
  }
}

// Create singleton instance
export const qsRankingService = new QSRankingService()
export default qsRankingService
