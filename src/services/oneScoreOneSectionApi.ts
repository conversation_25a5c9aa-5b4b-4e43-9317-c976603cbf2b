// 一分一段数据API服务
import type {
  OneScoreOneSectionFilters,
  OneScoreOneSectionItem,
  OneScoreOneSectionResponse
} from '../types/oneScoreOneSection'
import {
  getSubjectSelectionsByProvinceAndYear,
  getAvailableProvinces as getProvincesFromData
} from '../utils/subjectSelectionUtils'

// API配置 - 根据文档使用新的API地址
const isDevelopment = import.meta.env?.MODE === 'development' || typeof window !== 'undefined'
const API_BASE_URL = isDevelopment ? 'http://localhost:3001' : 'https://card.kefeichangduo.top'



/**
 * 构建查询参数（根据文档API格式）
 */
function buildOneScoreOneSectionParams(filters: OneScoreOneSectionFilters): URLSearchParams {
  const params = new URLSearchParams()

  // 必需参数（根据文档格式）
  params.append('year', filters.year)
  params.append('provincename', filters.provincename)
  params.append('subjectselection', filters.subjectselection)

  // 可选参数
  if (filters.minscore) {
    params.append('minscore', filters.minscore)
    console.log('📊 设置最低分数:', filters.minscore)
  }
  if (filters.maxscore) {
    params.append('maxscore', filters.maxscore)
    console.log('📊 设置最高分数:', filters.maxscore)
  }
  if (filters.batchname) {
    params.append('batchname', filters.batchname)
  }

  console.log('🔧 构建的查询参数:', Object.fromEntries(params.entries()))
  return params
}

/**
 * 获取一分一段数据（根据文档API）
 */
export async function getOneScoreOneSectionData(filters: OneScoreOneSectionFilters): Promise<OneScoreOneSectionResponse> {
  try {
    const params = buildOneScoreOneSectionParams(filters)
    const apiPath = '/admin-api/system/score-segment/by-condition'
    const url = `${API_BASE_URL}${apiPath}?${params.toString()}`
    
    console.log('🔍 请求一分一段数据:', url)
    console.log('📋 查询参数:', {
      year: filters.year,
      provincename: filters.provincename,
      subjectselection: filters.subjectselection,
      minscore: filters.minscore,
      maxscore: filters.maxscore,
      batchname: filters.batchname
    })

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      mode: 'cors',
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: OneScoreOneSectionResponse = await response.json()
    console.log('✅ 一分一段数据获取成功:', data)

    // 验证返回数据状态（根据文档格式）
    if (data.code !== 0) {
      throw new Error(`API错误: ${data.msg} (状态码: ${data.code})`)
    }

    return data
  } catch (error) {
    console.error('❌ 一分一段数据获取失败:', error)
    throw error
  }
}

/**
 * 根据分数查找位次信息
 */
export async function findRankingByScore(
  year: string,
  provinceName: string,
  subjectSelection: string,
  targetScore: number
): Promise<{
  exactMatch?: OneScoreOneSectionItem
  nearbyScores: OneScoreOneSectionItem[]
  ranking: string
  totalCandidates: number
} | null> {
  try {
    console.log('🎯 精确查询目标分数:', {
      year,
      provinceName,
      subjectSelection,
      targetScore,
      minscore: targetScore.toString(),
      maxscore: targetScore.toString()
    })

    // 首先尝试精确查询目标分数
    const exactResponse = await getOneScoreOneSectionData({
      year,
      provincename: provinceName,
      subjectselection: subjectSelection,
      minscore: targetScore.toString(),
      maxscore: targetScore.toString()
    })

    // 如果精确查询有结果，直接返回
    if (exactResponse.data && exactResponse.data.length > 0) {
      const exactMatch = exactResponse.data[0]
      return {
        exactMatch,
        nearbyScores: exactResponse.data,
        ranking: exactMatch.ranking || exactMatch.rankingRange,
        totalCandidates: exactMatch.totalCandidates
      }
    }

    // 如果精确查询没有结果，则查询附近分数范围
    const response = await getOneScoreOneSectionData({
      year,
      provincename: provinceName,
      subjectselection: subjectSelection,
      minscore: (targetScore - 5).toString(),
      maxscore: (targetScore + 5).toString()
    })

    if (!response.data || response.data.length === 0) {
      return null
    }

    const allData = response.data

    // 查找精确匹配
    const exactMatch = allData.find(item => {
      const scoreStr = item.examinationScore
      // 处理分数范围（如"699-750"）和单一分数（如"698"）
      if (scoreStr.includes('-')) {
        const [minStr, maxStr] = scoreStr.split('-')
        const min = parseInt(minStr)
        const max = parseInt(maxStr)
        return targetScore >= min && targetScore <= max
      } else {
        const score = parseInt(scoreStr)
        return score === targetScore
      }
    })

    // 获取附近分数的数据（用于参考）
    const nearbyScores = allData.filter(item => {
      const scoreStr = item.examinationScore
      if (scoreStr.includes('-')) {
        const [minStr, maxStr] = scoreStr.split('-')
        const min = parseInt(minStr)
        const max = parseInt(maxStr)
        return Math.abs((min + max) / 2 - targetScore) <= 5
      } else {
        const score = parseInt(scoreStr)
        return Math.abs(score - targetScore) <= 5
      }
    }).sort((a, b) => {
      const getScore = (scoreStr: string) => {
        if (scoreStr.includes('-')) {
          const [minStr, maxStr] = scoreStr.split('-')
          return (parseInt(minStr) + parseInt(maxStr)) / 2
        }
        return parseInt(scoreStr)
      }
      return getScore(b.examinationScore) - getScore(a.examinationScore)
    })

    // 确定位次和累计考生数
    let ranking = '未知'
    let totalCandidates = 0

    if (exactMatch) {
      ranking = exactMatch.ranking || exactMatch.rankingRange
      totalCandidates = exactMatch.totalCandidates
    } else if (nearbyScores.length > 0) {
      // 如果没有精确匹配，使用最接近的分数进行估算
      const closest = nearbyScores[0]
      ranking = `约${closest.ranking || closest.rankingRange}`
      totalCandidates = closest.totalCandidates
    }

    return {
      exactMatch,
      nearbyScores: nearbyScores.slice(0, 5), // 只返回前5个最接近的
      ranking,
      totalCandidates
    }
  } catch (error) {
    console.error('❌ 根据分数查找位次失败:', error)
    throw error
  }
}

/**
 * 获取可用的年份列表
 */
export function getAvailableYears(): string[] {
  return ['2025', '2024', '2023', '2022', '2021', '2020']
}

/**
 * 获取可用的省份列表
 */
export function getAvailableProvinces(): string[] {
  // 优先从数据文件获取省份列表，确保与实际数据一致
  const provincesFromData = getProvincesFromData()
  if (provincesFromData.length > 0) {
    return provincesFromData
  }

  // 后备方案：返回常见省份列表
  return [
    '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
    '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
    '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
    '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆'
  ]
}

/**
 * 获取可用的科目选择类型（根据省份和年份动态获取）
 */
export function getAvailableSubjectSelections(provinceName?: string, year?: string): string[] {
  // 如果没有提供省份或年份，返回默认的科目类型
  if (!provinceName || !year) {
    return ['物理类', '历史类', '理科', '文科', '综合']
  }

  // 使用工具函数获取准确的科目选择数据
  return getSubjectSelectionsByProvinceAndYear(provinceName, year)
}

/**
 * 获取可用的批次名称
 */
export function getAvailableBatchNames(): string[] {
  return ['本科批', '专科批', '本科一批', '本科二批', '高职专科批']
}

console.log('🔧 一分一段API服务已加载（基于文档API）')
console.log('- 开发环境API地址:', 'http://localhost:3001/admin-api/system/score-segment/by-condition')
console.log('- 生产环境API地址:', 'https://card.kefeichangduo.top/admin-api/system/score-segment/by-condition')
