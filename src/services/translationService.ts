// 翻译服务 - 将英文大学信息翻译为中文
export class TranslationService {
  // 大学名称翻译字典
  private static universityNames: Record<string, string> = {
    'Massachusetts Institute of Technology (MIT)': '麻省理工学院',
    'University of Cambridge': '剑桥大学',
    'Stanford University': '斯坦福大学',
    'University of Oxford': '牛津大学',
    'Harvard University': '哈佛大学',
    'California Institute of Technology (Caltech)': '加州理工学院',
    'Imperial College London': '伦敦帝国学院',
    'University College London (UCL)': '伦敦大学学院',
    'ETH Zurich': '苏黎世联邦理工学院',
    'University of Chicago': '芝加哥大学',
    'National University of Singapore (NUS)': '新加坡国立大学',
    'Peking University': '北京大学',
    'University of Pennsylvania': '宾夕法尼亚大学',
    'Tsinghua University': '清华大学',
    'University of Edinburgh': '爱丁堡大学',
    'École Polytechnique Fédérale de Lausanne (EPFL)': '洛桑联邦理工学院',
    'Princeton University': '普林斯顿大学',
    'Yale University': '耶鲁大学',
    'Nanyang Technological University (NTU)': '南洋理工大学',
    'Cornell University': '康奈尔大学',
    'University of Hong Kong (HKU)': '香港大学',
    'Columbia University': '哥伦比亚大学',
    'University of Tokyo': '东京大学',
    'Johns Hopkins University': '约翰霍普金斯大学',
    'University of Michigan': '密歇根大学',
    'Université PSL': '巴黎文理研究大学',
    'University of California, Berkeley': '加州大学伯克利分校',
    'University of Manchester': '曼彻斯特大学',
    'Australian National University (ANU)': '澳大利亚国立大学',
    'University of Toronto': '多伦多大学',
    'London School of Economics and Political Science (LSE)': '伦敦政治经济学院',
    'Northwestern University': '西北大学',
    'University of Bristol': '布里斯托大学',
    'Sorbonne University': '索邦大学',
    'University of Melbourne': '墨尔本大学',
    'University of California, Los Angeles (UCLA)': '加州大学洛杉矶分校',
    'University of Sydney': '悉尼大学',
    'New York University (NYU)': '纽约大学',
    'King\'s College London': '伦敦国王学院',
    'University of Glasgow': '格拉斯哥大学',
    'Seoul National University': '首尔国立大学',
    'University of Birmingham': '伯明翰大学',
    'University of St Andrews': '圣安德鲁斯大学',
    'University of Leeds': '利兹大学',
    'University of Sheffield': '谢菲尔德大学',
    'University of Nottingham': '诺丁汉大学',
    'University of Southampton': '南安普顿大学',
    'Boston University': '波士顿大学',
    'University of Warwick': '华威大学',
    'Technical University of Munich': '慕尼黑工业大学',
    'University of Queensland': '昆士兰大学',
    'University of Amsterdam': '阿姆斯特丹大学',
    'Delft University of Technology': '代尔夫特理工大学',
    'University of Copenhagen': '哥本哈根大学',
    'Lund University': '隆德大学',
    'KTH Royal Institute of Technology': '皇家理工学院',
    'University of Zurich': '苏黎世大学',
    'University of Geneva': '日内瓦大学',
    'McGill University': '麦吉尔大学',
    'University of British Columbia': '不列颠哥伦比亚大学',
    'Kyoto University': '京都大学',
    'Osaka University': '大阪大学',
    'Tokyo Institute of Technology': '东京工业大学',
    'Tohoku University': '东北大学',
    'Nagoya University': '名古屋大学',
    'Hokkaido University': '北海道大学',
    'Waseda University': '早稻田大学',
    'Keio University': '庆应义塾大学',
    // 添加更多常见大学
    'University of California, San Diego': '加州大学圣地亚哥分校',
    'University of California, San Francisco': '加州大学旧金山分校',
    'University of California, Davis': '加州大学戴维斯分校',
    'University of California, Irvine': '加州大学尔湾分校',
    'University of California, Santa Barbara': '加州大学圣巴巴拉分校',
    'Carnegie Mellon University': '卡内基梅隆大学',
    'Duke University': '杜克大学',
    'Dartmouth College': '达特茅斯学院',
    'Brown University': '布朗大学',
    'Rice University': '莱斯大学',
    'Vanderbilt University': '范德堡大学',
    'Washington University in St. Louis': '圣路易斯华盛顿大学',
    'Emory University': '埃默里大学',
    'Georgetown University': '乔治城大学',
    'University of Notre Dame': '圣母大学',
    'University of Virginia': '弗吉尼亚大学',
    'University of North Carolina at Chapel Hill': '北卡罗来纳大学教堂山分校',
    'Georgia Institute of Technology': '佐治亚理工学院',
    'University of Wisconsin-Madison': '威斯康星大学麦迪逊分校',
    'University of Illinois at Urbana-Champaign': '伊利诺伊大学厄巴纳-香槟分校',
    'University of Washington': '华盛顿大学',
    'University of Texas at Austin': '德克萨斯大学奥斯汀分校',
    'University of Florida': '佛罗里达大学',
    'Ohio State University': '俄亥俄州立大学',
    'Pennsylvania State University': '宾夕法尼亚州立大学',
    'Purdue University': '普渡大学',
    'University of Minnesota': '明尼苏达大学',
    'University of Maryland': '马里兰大学',
    'University of Pittsburgh': '匹兹堡大学',
    'Rutgers University': '罗格斯大学',
    'Arizona State University': '亚利桑那州立大学',
    'University of Colorado Boulder': '科罗拉多大学博尔德分校',
    'Virginia Tech': '弗吉尼亚理工学院',
    'North Carolina State University': '北卡罗来纳州立大学',
    'Texas A&M University': '德州农工大学',
    'University of Arizona': '亚利桑那大学',
    'University of Utah': '犹他大学',
    'University of Oregon': '俄勒冈大学',
    'University of Iowa': '爱荷华大学',
    'University of Kansas': '堪萨斯大学',
    'University of Nebraska': '内布拉斯加大学',
    'University of Oklahoma': '俄克拉荷马大学',
    'University of Tennessee': '田纳西大学',
    'University of Kentucky': '肯塔基大学',
    'University of Alabama': '阿拉巴马大学',
    'University of South Carolina': '南卡罗来纳大学',
    'University of Georgia': '佐治亚大学',
    'University of Arkansas': '阿肯色大学',
    'University of Mississippi': '密西西比大学',
    'University of Connecticut': '康涅狄格大学',
    'University of Delaware': '特拉华大学',
    'University of Hawaii': '夏威夷大学',
    'University of Vermont': '佛蒙特大学',
    'University of New Hampshire': '新罕布什尔大学',
    'University of Maine': '缅因大学',
    'University of Rhode Island': '罗德岛大学',
    'University of Alaska': '阿拉斯加大学',
    // 欧洲大学
    'University of Vienna': '维也纳大学',
    'University of Bologna': '博洛尼亚大学',
    'University of Milan': '米兰大学',
    'University of Rome': '罗马大学',
    'University of Florence': '佛罗伦萨大学',
    'University of Barcelona': '巴塞罗那大学',
    'University of Madrid': '马德里大学',
    'University of Lisbon': '里斯本大学',
    'University of Porto': '波尔图大学',
    'Trinity College Dublin': '都柏林三一学院',
    'University College Dublin': '都柏林大学学院',
    'University of Helsinki': '赫尔辛基大学',
    'University of Oslo': '奥斯陆大学',
    'University of Bergen': '卑尔根大学',
    'University of Iceland': '冰岛大学',
    // 亚洲大学
    'Chinese University of Hong Kong': '香港中文大学',
    'Hong Kong University of Science and Technology': '香港科技大学',
    'City University of Hong Kong': '香港城市大学',
    'Hong Kong Polytechnic University': '香港理工大学',
    'National Taiwan University': '国立台湾大学',
    'National Tsing Hua University': '国立清华大学',
    'National Chiao Tung University': '国立交通大学',
    'Fudan University': '复旦大学',
    'Shanghai Jiao Tong University': '上海交通大学',
    'Zhejiang University': '浙江大学',
    'Nanjing University': '南京大学',
    'University of Science and Technology of China': '中国科学技术大学',
    'Xi\'an Jiaotong University': '西安交通大学',
    'Harbin Institute of Technology': '哈尔滨工业大学',
    'Beihang University': '北京航空航天大学',
    'Beijing Institute of Technology': '北京理工大学',
    'Tianjin University': '天津大学',
    'Dalian University of Technology': '大连理工大学',
    'South China University of Technology': '华南理工大学',
    'Central South University': '中南大学',
    'Sichuan University': '四川大学',
    'Huazhong University of Science and Technology': '华中科技大学',
    'Wuhan University': '武汉大学',
    'Sun Yat-sen University': '中山大学',
    'Xiamen University': '厦门大学',
    'Tongji University': '同济大学',
    'Southeast University': '东南大学',
    'Beijing Normal University': '北京师范大学',
    'East China Normal University': '华东师范大学',
    'Renmin University of China': '中国人民大学',
    'China Agricultural University': '中国农业大学',
    'Beijing Foreign Studies University': '北京外国语大学',
    'Communication University of China': '中国传媒大学',
    'Central Academy of Fine Arts': '中央美术学院',
    'Central Conservatory of Music': '中央音乐学院',
    // 澳洲大学
    'University of New South Wales': '新南威尔士大学',
    'Monash University': '莫纳什大学',
    'University of Western Australia': '西澳大学',
    'University of Adelaide': '阿德莱德大学',
    'Macquarie University': '麦考瑞大学',
    'Queensland University of Technology': '昆士兰科技大学',
    'University of Technology Sydney': '悉尼科技大学',
    'RMIT University': '皇家墨尔本理工大学',
    'Curtin University': '科廷大学',
    'Deakin University': '迪肯大学',
    'Griffith University': '格里菲斯大学',
    'La Trobe University': '拉筹伯大学',
    'University of Newcastle': '纽卡斯尔大学',
    'University of Wollongong': '卧龙岗大学',
    'Flinders University': '弗林德斯大学',
    'University of Tasmania': '塔斯马尼亚大学'
  }

  // 国家名称翻译字典
  private static countryNames: Record<string, string> = {
    'United States': '美国',
    'United Kingdom': '英国',
    'China': '中国',
    'Australia': '澳大利亚',
    'Canada': '加拿大',
    'Germany': '德国',
    'Japan': '日本',
    'France': '法国',
    'Singapore': '新加坡',
    'Switzerland': '瑞士',
    'Netherlands': '荷兰',
    'South Korea': '韩国',
    'Sweden': '瑞典',
    'Denmark': '丹麦',
    'Norway': '挪威',
    'Finland': '芬兰',
    'Belgium': '比利时',
    'Austria': '奥地利',
    'Italy': '意大利',
    'Spain': '西班牙',
    'Portugal': '葡萄牙',
    'Ireland': '爱尔兰',
    'New Zealand': '新西兰',
    'Hong Kong': '香港',
    'Taiwan': '台湾',
    'Malaysia': '马来西亚',
    'Thailand': '泰国',
    'India': '印度',
    'Israel': '以色列',
    'Russia': '俄罗斯',
    'Brazil': '巴西',
    'Argentina': '阿根廷',
    'Chile': '智利',
    'Mexico': '墨西哥',
    'South Africa': '南非',
    'Egypt': '埃及',
    'Turkey': '土耳其',
    'Poland': '波兰',
    'Czech Republic': '捷克',
    'Hungary': '匈牙利',
    'Greece': '希腊',
    'Croatia': '克罗地亚',
    'Slovenia': '斯洛文尼亚',
    'Estonia': '爱沙尼亚',
    'Latvia': '拉脱维亚',
    'Lithuania': '立陶宛'
  }

  // 地区名称翻译字典
  private static regionNames: Record<string, string> = {
    'North America': '北美洲',
    'Europe': '欧洲',
    'Asia': '亚洲',
    'Oceania': '大洋洲',
    'South America': '南美洲',
    'Africa': '非洲',
    'Middle East': '中东',
    'Eastern Europe': '东欧',
    'Western Europe': '西欧',
    'Northern Europe': '北欧',
    'Southern Europe': '南欧',
    'Central Europe': '中欧',
    'East Asia': '东亚',
    'Southeast Asia': '东南亚',
    'South Asia': '南亚',
    'Central Asia': '中亚',
    'Western Asia': '西亚'
  }

  // 城市名称翻译字典
  private static cityNames: Record<string, string> = {
    'Cambridge': '剑桥',
    'Stanford': '斯坦福',
    'Oxford': '牛津',
    'Pasadena': '帕萨迪纳',
    'London': '伦敦',
    'Zurich': '苏黎世',
    'Chicago': '芝加哥',
    'Singapore': '新加坡',
    'Beijing': '北京',
    'Philadelphia': '费城',
    'Edinburgh': '爱丁堡',
    'Lausanne': '洛桑',
    'Princeton': '普林斯顿',
    'New Haven': '纽黑文',
    'Ithaca': '伊萨卡',
    'Hong Kong': '香港',
    'New York': '纽约',
    'Tokyo': '东京',
    'Baltimore': '巴尔的摩',
    'Ann Arbor': '安娜堡',
    'Paris': '巴黎',
    'Berkeley': '伯克利',
    'Manchester': '曼彻斯特',
    'Canberra': '堪培拉',
    'Toronto': '多伦多',
    'Evanston': '埃文斯顿',
    'Bristol': '布里斯托',
    'Melbourne': '墨尔本',
    'Los Angeles': '洛杉矶',
    'Sydney': '悉尼',
    'Glasgow': '格拉斯哥',
    'Seoul': '首尔',
    'Birmingham': '伯明翰',
    'St Andrews': '圣安德鲁斯',
    'Leeds': '利兹',
    'Sheffield': '谢菲尔德',
    'Nottingham': '诺丁汉',
    'Southampton': '南安普顿',
    'Boston': '波士顿',
    'Coventry': '考文垂',
    'Munich': '慕尼黑',
    'Brisbane': '布里斯班',
    'Amsterdam': '阿姆斯特丹',
    'Delft': '代尔夫特',
    'Copenhagen': '哥本哈根',
    'Lund': '隆德',
    'Stockholm': '斯德哥尔摩',
    'Geneva': '日内瓦',
    'Montreal': '蒙特利尔',
    'Vancouver': '温哥华',
    'Kyoto': '京都',
    'Osaka': '大阪',
    'Sendai': '仙台',
    'Nagoya': '名古屋',
    'Sapporo': '札幌'
  }

  /**
   * 翻译大学名称
   */
  static translateUniversityName(englishName: string): string {
    // 首先查找精确匹配
    if (this.universityNames[englishName]) {
      return this.universityNames[englishName]
    }

    // 如果没有精确匹配，尝试智能翻译
    return this.smartTranslateUniversity(englishName)
  }

  /**
   * 智能翻译大学名称（基于规则）
   */
  private static smartTranslateUniversity(englishName: string): string {
    // 常见的大学名称模式翻译
    const patterns = [
      // University of [地名] -> [地名]大学
      {
        pattern: /^University of (.+)$/i,
        replacement: (match: RegExpMatchArray) => {
          const location = match[1]
          const translatedLocation = this.translateLocationInUniversityName(location)
          return `${translatedLocation}大学`
        }
      },
      // [地名] University -> [地名]大学
      {
        pattern: /^(.+) University$/i,
        replacement: (match: RegExpMatchArray) => {
          const location = match[1]
          const translatedLocation = this.translateLocationInUniversityName(location)
          return `${translatedLocation}大学`
        }
      },
      // [地名] Institute of Technology -> [地名]理工学院
      {
        pattern: /^(.+) Institute of Technology$/i,
        replacement: (match: RegExpMatchArray) => {
          const location = match[1]
          const translatedLocation = this.translateLocationInUniversityName(location)
          return `${translatedLocation}理工学院`
        }
      },
      // [地名] College -> [地名]学院
      {
        pattern: /^(.+) College$/i,
        replacement: (match: RegExpMatchArray) => {
          const location = match[1]
          const translatedLocation = this.translateLocationInUniversityName(location)
          return `${translatedLocation}学院`
        }
      },
      // [地名] State University -> [地名]州立大学
      {
        pattern: /^(.+) State University$/i,
        replacement: (match: RegExpMatchArray) => {
          const location = match[1]
          const translatedLocation = this.translateLocationInUniversityName(location)
          return `${translatedLocation}州立大学`
        }
      }
    ]

    // 尝试匹配模式
    for (const { pattern, replacement } of patterns) {
      const match = englishName.match(pattern)
      if (match) {
        return replacement(match)
      }
    }

    // 如果没有匹配的模式，返回原名
    return englishName
  }

  /**
   * 翻译大学名称中的地名部分
   */
  private static translateLocationInUniversityName(location: string): string {
    // 移除常见的修饰词
    const cleanLocation = location
      .replace(/\s+at\s+/gi, ' ')
      .replace(/\s+in\s+/gi, ' ')
      .replace(/\s+of\s+/gi, ' ')
      .trim()

    // 尝试翻译州名和城市名
    const stateTranslations: Record<string, string> = {
      'California': '加利福尼亚',
      'Texas': '德克萨斯',
      'Florida': '佛罗里达',
      'New York': '纽约',
      'Illinois': '伊利诺伊',
      'Pennsylvania': '宾夕法尼亚',
      'Ohio': '俄亥俄',
      'Georgia': '佐治亚',
      'North Carolina': '北卡罗来纳',
      'Michigan': '密歇根',
      'New Jersey': '新泽西',
      'Virginia': '弗吉尼亚',
      'Washington': '华盛顿',
      'Arizona': '亚利桑那',
      'Massachusetts': '马萨诸塞',
      'Tennessee': '田纳西',
      'Indiana': '印第安纳',
      'Missouri': '密苏里',
      'Maryland': '马里兰',
      'Wisconsin': '威斯康星',
      'Colorado': '科罗拉多',
      'Minnesota': '明尼苏达',
      'South Carolina': '南卡罗来纳',
      'Alabama': '阿拉巴马',
      'Louisiana': '路易斯安那',
      'Kentucky': '肯塔基',
      'Oregon': '俄勒冈',
      'Oklahoma': '俄克拉荷马',
      'Connecticut': '康涅狄格',
      'Utah': '犹他',
      'Iowa': '爱荷华',
      'Nevada': '内华达',
      'Arkansas': '阿肯色',
      'Mississippi': '密西西比',
      'Kansas': '堪萨斯',
      'New Mexico': '新墨西哥',
      'Nebraska': '内布拉斯加',
      'West Virginia': '西弗吉尼亚',
      'Idaho': '爱达荷',
      'Hawaii': '夏威夷',
      'New Hampshire': '新罕布什尔',
      'Maine': '缅因',
      'Montana': '蒙大拿',
      'Rhode Island': '罗德岛',
      'Delaware': '特拉华',
      'South Dakota': '南达科他',
      'North Dakota': '北达科他',
      'Alaska': '阿拉斯加',
      'Vermont': '佛蒙特',
      'Wyoming': '怀俄明'
    }

    // 检查是否是已知的州名或城市名
    if (stateTranslations[cleanLocation]) {
      return stateTranslations[cleanLocation]
    }

    // 检查是否是已知的城市名
    if (this.cityNames[cleanLocation]) {
      return this.cityNames[cleanLocation]
    }

    // 如果都不是，返回原名
    return cleanLocation
  }

  /**
   * 翻译国家名称
   */
  static translateCountryName(englishName: string): string {
    return this.countryNames[englishName] || englishName
  }

  /**
   * 翻译地区名称
   */
  static translateRegionName(englishName: string): string {
    return this.regionNames[englishName] || englishName
  }

  /**
   * 翻译城市名称
   */
  static translateCityName(englishName: string): string {
    return this.cityNames[englishName] || englishName
  }

  /**
   * 获取双语显示格式（中文 + 英文）
   */
  static getBilingualUniversityName(englishName: string): string {
    const chineseName = this.translateUniversityName(englishName)
    if (chineseName === englishName) {
      return englishName // 如果没有翻译，只显示英文
    }
    return `${chineseName} (${englishName})`
  }

  /**
   * 获取双语国家名称
   */
  static getBilingualCountryName(englishName: string): string {
    const chineseName = this.translateCountryName(englishName)
    if (chineseName === englishName) {
      return englishName
    }
    return `${chineseName}`
  }

  /**
   * 获取双语城市名称
   */
  static getBilingualCityName(englishName: string): string {
    const chineseName = this.translateCityName(englishName)
    if (chineseName === englishName) {
      return englishName
    }
    return `${chineseName}`
  }

  /**
   * 批量添加大学名称翻译
   */
  static addUniversityTranslations(translations: Record<string, string>): void {
    Object.assign(this.universityNames, translations)
  }

  /**
   * 批量添加国家名称翻译
   */
  static addCountryTranslations(translations: Record<string, string>): void {
    Object.assign(this.countryNames, translations)
  }

  /**
   * 批量添加城市名称翻译
   */
  static addCityTranslations(translations: Record<string, string>): void {
    Object.assign(this.cityNames, translations)
  }

  /**
   * 获取所有支持的大学翻译
   */
  static getSupportedUniversities(): string[] {
    return Object.keys(this.universityNames)
  }

  /**
   * 获取所有支持的国家翻译
   */
  static getSupportedCountries(): string[] {
    return Object.keys(this.countryNames)
  }

  /**
   * 检查是否支持某个大学的翻译
   */
  static isUniversitySupported(englishName: string): boolean {
    return englishName in this.universityNames
  }

  /**
   * 检查是否支持某个国家的翻译
   */
  static isCountrySupported(englishName: string): boolean {
    return englishName in this.countryNames
  }
}
