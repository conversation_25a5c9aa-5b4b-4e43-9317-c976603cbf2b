// 测试一分一段API调用
async function testOneScoreOneSectionAPI() {
  const API_BASE_URL = 'http://localhost:3001/api/gugudata'
  const API_PATH = '/metadata/ceeline/one-score-one-section'
  const API_KEY = 'VB8WXGDWJ47VAGW54MAVGYMGTBYZNJJ3'
  
  // 测试参数
  const params = new URLSearchParams({
    appkey: API_KEY,
    year: '2024',
    provinceName: '安徽',
    subjectSelection: '物理类',
    pageIndex: '1',
    pageSize: '10'
  })
  
  const url = `${API_BASE_URL}${API_PATH}?${params.toString()}`
  
  console.log('🔍 测试一分一段API调用')
  console.log('📋 请求URL:', url)
  console.log('📋 请求参数:', {
    year: '2024',
    provinceName: '安徽',
    subjectSelection: '物理类',
    pageIndex: 1,
    pageSize: 10
  })
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      mode: 'cors',
    })
    
    console.log('📡 响应状态:', response.status, response.statusText)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('✅ API调用成功!')
    console.log('📊 返回数据:', data)
    
    if (data.DataStatus) {
      console.log('📈 数据状态:')
      console.log('  - 状态码:', data.DataStatus.StatusCode)
      console.log('  - 状态描述:', data.DataStatus.StatusDescription)
      console.log('  - 总数据量:', data.DataStatus.DataTotalCount)
      console.log('  - 响应时间:', data.DataStatus.ResponseDateTime)
    }
    
    if (data.Data && data.Data.length > 0) {
      console.log('📋 数据样例 (前3条):')
      data.Data.slice(0, 3).forEach((item, index) => {
        console.log(`  ${index + 1}. 分数: ${item.ExaminationScore}, 该分数人数: ${item.CandidateCount}, 累计人数: ${item.TotalCandidates}, 位次: ${item.Ranking || item.RankingRange}`)
      })
    }
    
    return data
  } catch (error) {
    console.error('❌ API调用失败:', error)
    throw error
  }
}

// 测试分数查位次功能
async function testScoreRanking() {
  console.log('\n🎯 测试分数查位次功能')

  try {
    const targetScore = 467
    console.log(`\n🔍 查找分数 ${targetScore} 对应的位次...`)

    // 模拟调用findRankingByScore函数
    const API_BASE_URL = 'http://localhost:3001/api/gugudata'
    const API_PATH = '/metadata/ceeline/one-score-one-section'
    const API_KEY = 'VB8WXGDWJ47VAGW54MAVGYMGTBYZNJJ3'

    // 需要查询多页数据来找到目标分数
    let allData = []
    let pageIndex = 1
    let found = false

    console.log('📋 开始查询多页数据以找到目标分数...')

    while (pageIndex <= 25 && !found) {
      const params = new URLSearchParams({
        appkey: API_KEY,
        year: '2024',
        provinceName: '安徽',
        subjectSelection: '物理类',
        pageIndex: pageIndex.toString(),
        pageSize: '20'
      })

      const url = `${API_BASE_URL}${API_PATH}?${params.toString()}`

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Accept': 'application/json' },
          mode: 'cors',
        })

        if (!response.ok) {
          break
        }

        const data = await response.json()

        if (!data.Data || data.Data.length === 0) {
          break
        }

        allData = [...allData, ...data.Data]
        console.log(`  - 第${pageIndex}页: 获取${data.Data.length}条数据`)

        // 检查是否包含目标分数或接近的分数
        const hasTargetOrNearby = data.Data.some(item => {
          const scoreStr = item.ExaminationScore
          if (scoreStr.includes('-')) {
            const [minStr, maxStr] = scoreStr.split('-')
            const min = parseInt(minStr)
            const max = parseInt(maxStr)
            return targetScore >= min && targetScore <= max
          } else {
            const score = parseInt(scoreStr)
            return Math.abs(score - targetScore) <= 50
          }
        })

        if (hasTargetOrNearby) {
          found = true
          console.log(`  ✅ 在第${pageIndex}页找到目标分数附近的数据`)
        }

        pageIndex++
      } catch (error) {
        console.error(`  ❌ 第${pageIndex}页查询失败:`, error.message)
        break
      }
    }

    console.log(`\n📊 总共获取 ${allData.length} 条数据`)

    if (allData.length > 0) {
      // 查找精确匹配
      let exactMatch = null

      for (const item of allData) {
        const scoreStr = item.ExaminationScore
        if (scoreStr.includes('-')) {
          const [minStr, maxStr] = scoreStr.split('-')
          const min = parseInt(minStr)
          const max = parseInt(maxStr)
          if (targetScore >= min && targetScore <= max) {
            exactMatch = item
            break
          }
        } else {
          const score = parseInt(scoreStr)
          if (score === targetScore) {
            exactMatch = item
            break
          }
        }
      }

      if (exactMatch) {
        console.log('✅ 找到精确匹配:')
        console.log(`  - 分数: ${exactMatch.ExaminationScore}`)
        console.log(`  - 该分数人数: ${exactMatch.CandidateCount}`)
        console.log(`  - 累计人数: ${exactMatch.TotalCandidates}`)
        console.log(`  - 位次: ${exactMatch.Ranking || exactMatch.RankingRange}`)
        console.log(`  - 批次: ${exactMatch.AdmissionBatchName}`)
      } else {
        console.log('⚠️ 未找到精确匹配，查找附近分数...')

        // 查找附近分数
        const nearbyScores = allData.filter(item => {
          const scoreStr = item.ExaminationScore
          if (scoreStr.includes('-')) {
            const [minStr, maxStr] = scoreStr.split('-')
            const min = parseInt(minStr)
            const max = parseInt(maxStr)
            return Math.abs((min + max) / 2 - targetScore) <= 50
          } else {
            const score = parseInt(scoreStr)
            return Math.abs(score - targetScore) <= 50
          }
        }).sort((a, b) => {
          const getScore = (scoreStr) => {
            if (scoreStr.includes('-')) {
              const [minStr, maxStr] = scoreStr.split('-')
              return (parseInt(minStr) + parseInt(maxStr)) / 2
            }
            return parseInt(scoreStr)
          }
          return Math.abs(getScore(a.ExaminationScore) - targetScore) - Math.abs(getScore(b.ExaminationScore) - targetScore)
        })

        if (nearbyScores.length > 0) {
          console.log('📋 附近分数数据 (前5个最接近的):')
          nearbyScores.slice(0, 5).forEach((item, index) => {
            console.log(`  ${index + 1}. 分数: ${item.ExaminationScore}, 位次: ${item.Ranking || item.RankingRange}, 累计人数: ${item.TotalCandidates}`)
          })

          const closest = nearbyScores[0]
          console.log(`\n🎯 最接近的分数段: ${closest.ExaminationScore}`)
          console.log(`   估算位次: 约${closest.Ranking || closest.RankingRange}`)
          console.log(`   累计考生: ${closest.TotalCandidates}`)
        } else {
          console.log('❌ 未找到附近分数数据')
        }
      }
    }
  } catch (error) {
    console.error('❌ 分数查位次测试失败:', error)
  }
}

// 运行测试
console.log('🚀 开始测试一分一段API功能...')
testScoreRanking().then(() => {
  console.log('\n✅ 测试完成!')
}).catch(error => {
  console.error('\n❌ 测试失败:', error)
})
