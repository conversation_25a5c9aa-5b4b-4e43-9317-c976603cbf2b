// 大学相关的类型定义

// API响应数据状态
export interface DataStatus {
  StatusCode: number
  StatusDescription: string
  ResponseDateTime: string
  DataTotalCount: number
}

// 专业列表结构
export interface MajorList {
  MajorTitle: string
  Majors: string[]
}

// 大学接口 - 根据咕咕数据API结构定义
export interface University {
  DataId: string
  SchoolUUID: string
  CollegeName: string
  Province: string
  City: string
  District: string
  Coordinate: string // 格式为 [经度,纬度]
  CollegeType: string // 普通本科|独立学院|专科（高职）|高职高专|中外合作办学|成人教育|HND项目|远程教育学院|其它
  Is985: boolean
  Is211: boolean
  IsDualClass: boolean
  CollegeCategory: string // 综合类|理工类|师范类|财经类|医药类|艺术类|农林类|军事类|政法类|语言类|体育类|民族类|其它
  CollegeTags: string[]
  EduLevel: string // 普通本科|本科|专科（高职）|专科|其它
  CollegeProperty: string // 公办|民办|中外合作办学
  CollegeCode: string
  Ranking: number
  RankingInCategory: string
  WebSite: string
  CallNumber: string
  Email: string
  Address: string
  BranchList: string[]
  CoverImage: string
  Intro: string
  Expenses: string
  OldName: string
  ShortName: string
  MajorList: MajorList[]
  IsDeleted: boolean
}

// 搜索筛选条件 - 根据API参数定义
export interface SearchFilters {
  keywords?: string // 搜索关键字
  pagesize?: number // 每页数据量，最大20
  pageindex?: number // 页码
  keywordstrict?: boolean // 是否精确匹配
  collegecategory?: string // 学院类别：理工类|综合类|师范类|财经类|医药类|艺术类|农林类|军事类|政法类|语言类|体育类|民族类|其它
  collegetype?: string // 学院性质：普通本科|远程教育学院|中外合作办学|独立学院|高职高专|HND项目|其它|成人教育|专科（高职）
  is985?: boolean // 是否为985院校
  is211?: boolean // 是否为211院校
  isdualclass?: boolean // 是否为双一流院校
  edulevel?: string // 学制：普通本科|本科|专科（高职）|专科|其它
  collegeproperty?: string // 资质：公办|民办|中外合作办学
}

// API响应结构
export interface ApiResponse {
  DataStatus: DataStatus
  Data: University[]
}

// 搜索结果
export interface SearchResult {
  universities: University[]
  total: number
  page: number
  pageSize: number
}
