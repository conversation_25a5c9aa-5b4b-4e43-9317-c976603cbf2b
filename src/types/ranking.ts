// 位次查询相关类型定义

export interface RankingQuery {
  year: number
  province: string
  score: number
  category: '文科' | '理科' | '综合'
}

export interface RankingResult {
  year: number
  province: string
  score: number
  category: '文科' | '理科' | '综合'
  rankingRange: {
    min: number
    max: number
  }
  sameScoreCount: number
  maxRanking: number
  controlLine: {
    type: '一本' | '二本' | '专科' | '高'
    score: number
  }
  percentile?: number
}

export interface RankingTrend {
  year: number
  ranking: number
  score: number
  sameScoreCount: number
}

export interface RankingDataPoint {
  score: number
  ranking: number
  sameScoreCount: number
}

export interface ControlLine {
  type: '一本' | '二本' | '专科'
  score: number
  name: string
}

export interface ProvinceRankingData {
  province: string
  years: {
    [year: number]: {
      文科: RankingDataPoint[]
      理科: RankingDataPoint[]
      综合?: RankingDataPoint[]
    }
  }
  controlLines: {
    [year: number]: {
      文科: ControlLine[]
      理科: ControlLine[]
      综合?: ControlLine[]
    }
  }
}

export interface RankingSearchFilters {
  year?: number
  province?: string
  minScore?: number
  maxScore?: number
  category?: '文科' | '理科' | '综合'
}
