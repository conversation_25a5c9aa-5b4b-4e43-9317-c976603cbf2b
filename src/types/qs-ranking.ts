// QS Global University Ranking Types
// Based on GuGuData API documentation

export interface QSUniversity {
  Id: string
  UniversityName: string
  Region: string
  Country: string
  City: string
  LogoUrl: string
  Rank: string
  AcademicReputationRank: string
  AcademicReputationScore: string
  CitationsPerFacultyRank: string
  CitationsPerFacultyScore: string
  FacultyStudentRatioRank: string
  FacultyStudentRatioScore: string
  EmployerReputationRank: string
  EmployerReputationScore: string
  EmploymentOutcomesRank: string
  EmploymentOutcomesScore: string
  InternationalStudentRatioRank: string
  InternationalStudentRatioScore: string
  InternationalResearchNetworkRank: string
  InternationalResearchNetworkScore: string
  InternationalFacultyRatioRank: string
  InternationalFacultyRatioScore: string
  SustainabilityRank: string
  SustainabilityScore: string
}

export interface QSRankingResponse {
  DataStatus: {
    RequestParameter: string
    StatusCode: number
    StatusDescription: string
    ResponseDateTime: string
    DataTotalCount: number
  }
  Data: QSUniversity[]
}

export interface QSRankingQuery {
  name?: string
  pageIndex?: number
  pageSize?: number
}

export interface QSRankingFilter {
  searchName: string
  selectedCountry: string
  selectedRegion: string
  rankRange: {
    min: number
    max: number
  }
  showTopOnly: boolean
}

// Processed university data for display
export interface ProcessedQSUniversity {
  id: string
  name: string
  nameZh: string
  country: string
  countryZh: string
  region: string
  regionZh: string
  city: string
  cityZh: string
  logoUrl: string
  rank: number
  rankDisplay: string
  scores: {
    academicReputation: {
      rank: number
      score: number
    }
    citationsPerFaculty: {
      rank: number
      score: number
    }
    facultyStudentRatio: {
      rank: number
      score: number
    }
    employerReputation: {
      rank: number
      score: number
    }
    employmentOutcomes: {
      rank: number
      score: number
    }
    internationalStudentRatio: {
      rank: number
      score: number
    }
    internationalResearchNetwork: {
      rank: number
      score: number
    }
    internationalFacultyRatio: {
      rank: number
      score: number
    }
    sustainability: {
      rank: number
      score: number
    }
  }
}

// Available countries and regions for filtering
export interface CountryRegion {
  country: string
  regions: string[]
}

export const QS_RANKING_INDICATORS = [
  {
    key: 'academicReputation',
    name: '学术声誉',
    weight: '40%',
    description: '基于全球学者调查的学术声誉评分'
  },
  {
    key: 'employerReputation',
    name: '雇主声誉',
    weight: '10%',
    description: '基于雇主调查的毕业生就业能力评分'
  },
  {
    key: 'facultyStudentRatio',
    name: '师生比例',
    weight: '20%',
    description: '教师与学生的比例，反映教学质量'
  },
  {
    key: 'citationsPerFaculty',
    name: '每名教师引用率',
    weight: '20%',
    description: '教师平均被引用次数，反映研究影响力'
  },
  {
    key: 'internationalFacultyRatio',
    name: '国际教师比例',
    weight: '5%',
    description: '国际教师占总教师的比例'
  },
  {
    key: 'internationalStudentRatio',
    name: '国际学生比例',
    weight: '5%',
    description: '国际学生占总学生的比例'
  },
  {
    key: 'employmentOutcomes',
    name: '就业成果',
    weight: '0%',
    description: '毕业生就业情况和职业发展'
  },
  {
    key: 'internationalResearchNetwork',
    name: '国际研究网络',
    weight: '0%',
    description: '国际合作研究网络的广度'
  },
  {
    key: 'sustainability',
    name: '可持续发展',
    weight: '0%',
    description: '大学在可持续发展方面的表现'
  }
] as const

export type QSIndicatorKey = typeof QS_RANKING_INDICATORS[number]['key']
