// 一分一段数据相关类型定义

// 一分一段查询参数接口
export interface OneScoreOneSectionFilters {
  year: string                 // 查询的年份，如 2020、2021、2022、2023、2024
  provincename: string         // 省份名称，如：北京、上海、江苏等
  subjectselection: string     // 科目选择类型，如：物理类、历史类等
  minscore?: string           // 最小分数（可选）
  maxscore?: string           // 最大分数（可选）
  batchname?: string          // 批次名称，如：本科批（可选）
}

// 一分一段数据项接口（根据文档API响应格式）
export interface OneScoreOneSectionItem {
  id: number                           // 主键ID
  year: string                         // 年份
  provinceName: string                 // 省份名称
  subjectSelection: string             // 科目选择类型
  examinationScore: string             // 高考分数
  candidateCount: number               // 该分数考生人数
  totalCandidates: number              // 累计考生人数
  rankingRange: string                 // 位次范围
  admissionBatchName: string           // 录取批次名称
  minimumAdmissionScore: string        // 最低录取控制分数线
  ranking: string                      // 位次
  historicalScores: string             // 历史分数数据
}

// API返回数据接口（根据文档格式）
export interface OneScoreOneSectionResponse {
  code: number                         // 状态码
  data: OneScoreOneSectionItem[]       // 数据数组
  msg: string                          // 消息
}

// 位次查询结果接口
export interface RankingSearchResult {
  exactMatch?: OneScoreOneSectionItem
  nearbyScores: OneScoreOneSectionItem[]
  ranking: string
  totalCandidates: number
}
