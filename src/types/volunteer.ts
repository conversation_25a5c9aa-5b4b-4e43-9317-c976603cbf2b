// 志愿填报相关类型定义

// 填报模式
export type VolunteerMode = 'university' | 'major'

// 录取概率等级
export type AdmissionLevel = 'all' | 'rush' | 'stable' | 'safe'

export interface VolunteerUniversity {
  id: string
  name: string
  shortName?: string
  code: string
  location: {
    province: string
    city: string
  }
  type: '985' | '211' | '双一流' | '普通本科' | '民办' | '独立学院'
  category: '综合' | '理工' | '师范' | '农林' | '医药' | '财经' | '政法' | '艺术' | '体育' | '军事' | '民族' | '语言'
  level: '本科' | '专科'
  isPublic: boolean
  ranking?: {
    national?: number
    regional?: number
    category?: number
  }
  scores: {
    year: number
    province: string
    subject: '理科' | '文科' | '物理' | '历史'
    minScore: number
    avgScore: number
    maxScore: number
    ranking: number
    admissionRate?: number
  }[]
  features: string[]
  keyMajors: string[]
  availableMajors?: VolunteerMajor[] // 可填报的专业列表
  tuition?: {
    min: number
    max: number
    unit: '年' | '学期'
  }
  contact?: {
    phone?: string
    website?: string
    address?: string
  }
  admissionPlan?: {
    total: number
    province: string
    year: number
  }
}

// 志愿专业信息
export interface VolunteerMajor {
  id: string
  code: string
  name: string
  category: string
  subCategory?: string
  university: {
    id: string
    name: string
  }
  admissionScore?: {
    year: number
    minScore: number
    avgScore: number
    ranking: number
  }[]
  features?: string[]
  description?: string
}

export interface VolunteerFilter {
  provinces: string[]
  types: string[]
  categories: string[]
  levels: string[]
  scoreRange: {
    min: number
    max: number
  }
  rankingRange: {
    min: number
    max: number
  }
  features: string[]
  isPublicOnly: boolean
  searchKeyword: string
  admissionLevel: AdmissionLevel // 录取概率等级筛选
}

export interface VolunteerChoice {
  id: string
  university?: VolunteerUniversity // 院校优先模式使用
  major?: VolunteerMajor // 专业优先模式使用
  majors: string[] // 院校优先模式下选择的专业
  priority: number
  isLocked: boolean
  notes?: string
  addedAt: Date
  mode: VolunteerMode // 填报模式
}

export interface VolunteerForm {
  studentInfo: {
    name: string
    province: string
    subject: '理科' | '文科' | '物理' | '历史'
    score: number
    ranking: number
    category: '普通类' | '艺术类' | '体育类'
  }
  choices: VolunteerChoice[]
  maxChoices: number
  currentBatch: '本科提前批' | '本科一批' | '本科二批' | '专科批'
  isDraft: boolean
  lastSaved?: Date
}

export interface ScoreAnalysis {
  probability: 'high' | 'medium' | 'low'
  probabilityScore: number
  suggestion: 'rush' | 'stable' | 'safe'
  historicalData: {
    year: number
    minScore: number
    avgScore: number
    ranking: number
  }[]
  competitiveness: 'very_high' | 'high' | 'medium' | 'low'
}

export interface RecommendationResult {
  university: VolunteerUniversity
  analysis: ScoreAnalysis
  matchScore: number
  reasons: string[]
  warnings?: string[]
}
