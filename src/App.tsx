import { useState, useEffect } from 'react'
import { Button } from './components/ui/button'
import { NavigationPage } from './components/navigation-page'
import { DemoShowcase } from './components/demo-showcase'
import { LucideShowcase } from './components/lucide-showcase'
import { CompleteUniversitySearchPage } from './components/university/CompleteUniversitySearchPage'
import { UniversitySearchPage } from './components/university/UniversitySearchPage'
import { ApiTestPage } from './components/university/ApiTestPage'
import { MajorSearchPage } from './components/major/MajorSearchPage'
import { MajorDetailDemo } from './components/major/MajorDetailDemo'
import { MajorApiTest } from './components/major/MajorApiTest'
import { MajorDetailPageNew } from './components/major/MajorDetailPageNew'
import { MajorDataTest } from './components/major/MajorDataTest'
import { CategoryTest } from './components/major/CategoryTest'
import { ScoreLinePage } from './components/score/ScoreLinePage'
import { ScoreTestPage } from './components/score/ScoreTestPage'
import { OneScoreOneSectionPage } from './components/ranking/OneScoreOneSectionPage'
import { QSRankingPage } from './components/qs-ranking/QSRankingPage'
import { VolunteerApplicationPage } from './components/volunteer/VolunteerApplicationPage'
import { BaiduStyleChatPage } from './components/ai-chat/BaiduStyleChatPage'
import { EnrollmentPlanPage } from './components/enrollment-plan/EnrollmentPlanPageSimple'
import { SubjectSelectionPage } from './components/subject-selection/SubjectSelectionPage'
import { ProvinceControlLinePage } from './components/province-control-line/ProvinceControlLinePage'
import { KnowledgeBasePage } from './components/knowledge-base/KnowledgeBasePage'
import { IndustryTrendsPage } from './components/knowledge-base/IndustryTrendsPage'
import { IndustryTrendDetailPage } from './components/knowledge-base/IndustryTrendDetailPage'
import { VolunteerQAPage } from './components/knowledge-base/VolunteerQAPage'
import { VolunteerQADetailPage } from './components/knowledge-base/VolunteerQADetailPage'
import { PopularMajorsPage } from './components/knowledge-base/PopularMajorsPage'
import { PopularMajorDetailPage } from './components/knowledge-base/PopularMajorDetailPage'
import { CautiousMajorsPage } from './components/knowledge-base/CautiousMajorsPage'
import { CautiousMajorDetailPage } from './components/knowledge-base/CautiousMajorDetailPage'
import { GoldenJobsPage } from './components/knowledge-base/GoldenJobsPage'
import { GoldenJobDetailPage } from './components/knowledge-base/GoldenJobDetailPage'
import { MBTITestPage } from './components/mbti-test/MBTITestPage'
import { HollandCareerOrientationTest } from './components/mbti-test/HollandCareerOrientationTest'
import { CareerPositioningTest } from './components/mbti-test/CareerPositioningTest'
import { HollandInterestTest } from './components/mbti-test/HollandInterestTest'
import { MBTIPersonalityTest } from './components/mbti-test/MBTIPersonalityTest'
import { LoginPage } from './components/auth/LoginPage'
import { RegisterPage } from './components/auth/RegisterPage'
import { LoginTestPage } from './components/auth/LoginTestPage'
import { MyGoalPage } from './components/goal/MyGoalPage'
import { GoalSettingPage } from './components/goal/GoalSettingPage'
import { ScoreEntryPage } from './components/goal/ScoreEntryPage'
import { GoalSettingTest } from './components/test/GoalSettingTest'
import { AuthProvider } from './contexts/AuthContext'
import { Home, Star, ArrowLeft, Palette } from 'lucide-react'

// 类型定义
type DisciplineCategory =
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'

type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: DisciplineCategory
  subCategory?: string
  degree: string
  duration: number
  educationLevel: EducationLevel
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  courses?: {
    core?: string[]
    elective?: string[]
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
}

function AppContent() {
  const [count, setCount] = useState(0)
  const [currentPage, setCurrentPage] = useState<'navigation' | 'demo' | 'icons' | 'universities' | 'majors' | 'major-detail' | 'major-api-test' | 'major-data-test' | 'category-test' | 'rankings' | 'qs-ranking' | 'volunteer' | 'ai-chat' | 'scores' | 'score-test' | 'enrollment-plan' | 'subject-selection' | 'province-control-line' | 'knowledge-base' | 'industry-trends' | 'industry-trend-detail' | 'volunteer-qa' | 'volunteer-qa-detail' | 'popular-majors' | 'popular-major-detail' | 'cautious-majors' | 'cautious-major-detail' | 'golden-jobs' | 'golden-job-detail' | 'mbti-test' | 'holland-career-orientation' | 'career-positioning' | 'holland-interest' | 'mbti-personality' | 'login' | 'register' | 'login-test' | 'my-goal' | 'goal-setting' | 'score-entry'>('navigation')
  const [selectedMajor, setSelectedMajor] = useState<Major | null>(null)
  const [selectedIndustryTrend, setSelectedIndustryTrend] = useState<any>(null)
  const [selectedVolunteerQA, setSelectedVolunteerQA] = useState<any>(null)
  const [selectedPopularMajor, setSelectedPopularMajor] = useState<any>(null)
  const [selectedCautiousMajor, setSelectedCautiousMajor] = useState<any>(null)
  const [selectedGoldenJob, setSelectedGoldenJob] = useState<any>(null)

  // 页面切换时自动滚动到顶部
  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    })
  }, [currentPage])

  // 条件渲染逻辑
  if (currentPage === 'login') {
    return (
      <LoginPage
        onBack={() => setCurrentPage('navigation')}
        onSwitchToRegister={() => setCurrentPage('register')}
      />
    )
  }

  if (currentPage === 'register') {
    return (
      <RegisterPage
        onBack={() => setCurrentPage('navigation')}
        onSwitchToLogin={() => setCurrentPage('login')}
      />
    )
  }

  if (currentPage === 'login-test') {
    return (
      <LoginTestPage
        onBack={() => setCurrentPage('navigation')}
        onLogin={() => setCurrentPage('login')}
        onRegister={() => setCurrentPage('register')}
      />
    )
  }

  if (currentPage === 'my-goal') {
    return (
      <MyGoalPage
        onNavigate={(page) => setCurrentPage(page as any)}
        onBack={() => setCurrentPage('navigation')}
      />
    )
  }

  if (currentPage === 'goal-setting') {
    return (
      <GoalSettingPage
        onNavigate={(page) => setCurrentPage(page as any)}
        onBack={() => setCurrentPage('my-goal')}
      />
    )
  }

  if (currentPage === 'score-entry') {
    return (
      <ScoreEntryPage
        onBack={() => setCurrentPage('my-goal')}
        onSave={() => setCurrentPage('my-goal')}
      />
    )
  }

  if (currentPage === 'goal-setting-test') {
    return (
      <div>
        <div className="p-4 border-b bg-white">
          <button
            onClick={() => setCurrentPage('navigation')}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回导航</span>
          </button>
        </div>
        <GoalSettingTest />
      </div>
    )
  }

  if (currentPage === 'knowledge-base') {
    return (
      <KnowledgeBasePage
        onBack={() => setCurrentPage('navigation')}
        onNavigate={(page) => setCurrentPage(page as any)}
      />
    )
  }

  if (currentPage === 'industry-trends') {
    return (
      <IndustryTrendsPage
        onBack={() => setCurrentPage('knowledge-base')}
        onNavigateToDetail={(trend) => {
          setSelectedIndustryTrend(trend)
          setCurrentPage('industry-trend-detail')
        }}
      />
    )
  }

  if (currentPage === 'industry-trend-detail') {
    return (
      <IndustryTrendDetailPage
        onBack={() => setCurrentPage('industry-trends')}
        selectedTrend={selectedIndustryTrend}
      />
    )
  }

  if (currentPage === 'volunteer-qa') {
    return (
      <VolunteerQAPage
        onBack={() => setCurrentPage('knowledge-base')}
        onNavigateToDetail={(qa) => {
          setSelectedVolunteerQA(qa)
          setCurrentPage('volunteer-qa-detail')
        }}
      />
    )
  }

  if (currentPage === 'volunteer-qa-detail') {
    return (
      <VolunteerQADetailPage
        onBack={() => setCurrentPage('volunteer-qa')}
        selectedQA={selectedVolunteerQA}
      />
    )
  }

  if (currentPage === 'popular-majors') {
    return (
      <PopularMajorsPage
        onBack={() => setCurrentPage('knowledge-base')}
        onNavigateToDetail={(major) => {
          setSelectedPopularMajor(major)
          setCurrentPage('popular-major-detail')
        }}
      />
    )
  }

  if (currentPage === 'popular-major-detail') {
    return (
      <PopularMajorDetailPage
        onBack={() => setCurrentPage('popular-majors')}
        selectedMajor={selectedPopularMajor}
      />
    )
  }

  if (currentPage === 'cautious-majors') {
    return (
      <CautiousMajorsPage
        onBack={() => setCurrentPage('knowledge-base')}
        onNavigateToDetail={(major) => {
          setSelectedCautiousMajor(major)
          setCurrentPage('cautious-major-detail')
        }}
      />
    )
  }

  if (currentPage === 'cautious-major-detail') {
    return (
      <CautiousMajorDetailPage
        onBack={() => setCurrentPage('cautious-majors')}
        selectedMajor={selectedCautiousMajor}
      />
    )
  }

  if (currentPage === 'golden-jobs') {
    return (
      <GoldenJobsPage
        onBack={() => setCurrentPage('knowledge-base')}
        onNavigateToDetail={(job) => {
          setSelectedGoldenJob(job)
          setCurrentPage('golden-job-detail')
        }}
      />
    )
  }

  if (currentPage === 'golden-job-detail') {
    return (
      <GoldenJobDetailPage
        onBack={() => setCurrentPage('golden-jobs')}
        selectedJob={selectedGoldenJob}
      />
    )
  }

  if (currentPage === 'mbti-test') {
    return (
      <MBTITestPage
        onBack={() => setCurrentPage('navigation')}
        onNavigate={(page) => setCurrentPage(page as any)}
      />
    )
  }

  if (currentPage === 'holland-career-orientation') {
    return (
      <HollandCareerOrientationTest onBack={() => setCurrentPage('mbti-test')} />
    )
  }

  if (currentPage === 'career-positioning') {
    return (
      <CareerPositioningTest onBack={() => setCurrentPage('mbti-test')} />
    )
  }

  if (currentPage === 'holland-interest') {
    return (
      <HollandInterestTest onBack={() => setCurrentPage('mbti-test')} />
    )
  }

  if (currentPage === 'mbti-personality') {
    return (
      <MBTIPersonalityTest onBack={() => setCurrentPage('mbti-test')} />
    )
  }

  if (currentPage === 'ai-chat') {
    return (
      <BaiduStyleChatPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'enrollment-plan') {
    return (
      <EnrollmentPlanPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'subject-selection') {
    return (
      <SubjectSelectionPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'province-control-line') {
    return (
      <ProvinceControlLinePage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'volunteer') {
    return (
      <VolunteerApplicationPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'universities') {
    return (
      <UniversitySearchPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'api-test') {
    return (
      <div>
        <div className="p-4 border-b">
          <Button
            variant="ghost"
            onClick={() => setCurrentPage('navigation')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回首页
          </Button>
        </div>
        <ApiTestPage />
      </div>
    )
  }

  if (currentPage === 'rankings') {
    return (
      <OneScoreOneSectionPage onBack={() => setCurrentPage('navigation')} />
    )
  }



  if (currentPage === 'qs-ranking') {
    return (
      <QSRankingPage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'scores') {
    return (
      <ScoreLinePage onBack={() => setCurrentPage('navigation')} />
    )
  }

  if (currentPage === 'score-test') {
    return (
      <div>
        <div className="p-4 border-b">
          <Button
            variant="ghost"
            onClick={() => setCurrentPage('navigation')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回首页
          </Button>
        </div>
        <ScoreTestPage />
      </div>
    )
  }

  if (currentPage === 'majors') {
    return (
      <MajorSearchPage
        onBack={() => setCurrentPage('navigation')}
        onMajorClick={(major) => {
          setSelectedMajor(major)
          setCurrentPage('major-detail')
        }}
      />
    )
  }

  if (currentPage === 'major-api-test') {
    return (
      <div>
        <div className="p-4 bg-white border-b">
          <button
            onClick={() => setCurrentPage('navigation')}
            className="text-blue-600 hover:text-blue-800"
          >
            ← 返回首页
          </button>
        </div>
        <MajorApiTest />
      </div>
    )
  }

  if (currentPage === 'major-data-test') {
    return (
      <div>
        <div className="p-4 bg-white border-b">
          <button
            onClick={() => setCurrentPage('navigation')}
            className="text-blue-600 hover:text-blue-800"
          >
            ← 返回首页
          </button>
        </div>
        <MajorDataTest />
      </div>
    )
  }

  if (currentPage === 'category-test') {
    return (
      <div>
        <div className="p-4 bg-white border-b">
          <button
            onClick={() => setCurrentPage('navigation')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            返回导航
          </button>
        </div>
        <CategoryTest />
      </div>
    )
  }

  if (currentPage === 'major-detail') {
    if (selectedMajor) {
      return (
        <MajorDetailPageNew
          major={selectedMajor}
          onBack={() => {
            setCurrentPage('majors')
            setSelectedMajor(null)
          }}
        />
      )
    } else {
      return (
        <MajorDetailDemo />
      )
    }
  }

  if (currentPage === 'icons') {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8 flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage('navigation')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              返回导航
            </Button>
            <Button
              variant="outline"
              onClick={() => setCurrentPage('demo')}
              className="flex items-center gap-2"
            >
              <Palette className="w-4 h-4" />
              演示页面
            </Button>
          </div>
          <LucideShowcase />
        </div>
      </div>
    )
  }

  if (currentPage === 'demo') {
    return (
      <div className="relative">
        <div className="fixed top-4 right-4 z-50 flex gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage('navigation')}
            className="flex items-center gap-2 bg-background/80 backdrop-blur-sm"
          >
            <Home className="w-4 h-4" />
            返回导航
          </Button>
          <Button
            variant="outline"
            onClick={() => setCurrentPage('icons')}
            className="flex items-center gap-2 bg-background/80 backdrop-blur-sm"
          >
            <Star className="w-4 h-4" />
            查看图标库
          </Button>
        </div>
        <DemoShowcase
          count={count}
          onIncrement={() => setCount(count + 1)}
          onDecrement={() => setCount(count - 1)}
          onReset={() => setCount(0)}
        />
      </div>
    )
  }

  return (
    <NavigationPage
      onNavigate={(page) => setCurrentPage(page as any)}
      onLogin={() => setCurrentPage('login')}
      onRegister={() => setCurrentPage('register')}
    />
  )
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App
