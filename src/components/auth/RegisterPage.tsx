import { useState, useEffect } from 'react'
import { 
  ArrowLeft, 
  Smartphone, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Loader2,
  User,
  UserPlus
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { authService } from '../../services/authService'

// 本地类型定义
type RegisterMethod = 'phone' | 'email' | 'username'
interface FormErrors {
  [key: string]: string
}

interface RegisterPageProps {
  onBack: () => void
  onSwitchToLogin: () => void
}

export function RegisterPage({ onBack, onSwitchToLogin }: RegisterPageProps) {
  const { register, isLoading } = useAuth()
  const [registerMethod, setRegisterMethod] = useState<RegisterMethod>('phone')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [errors, setErrors] = useState<FormErrors>({})
  
  // 表单数据
  const [formData, setFormData] = useState({
    username: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
    verificationCode: '',
    nickname: ''
  })

  // 验证码倒计时
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // 通用验证
    if (!formData.password) {
      newErrors.password = '请输入密码'
    } else if (formData.password.length < 6) {
      newErrors.password = '密码至少6位'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致'
    }

    // 根据注册方式验证
    if (registerMethod === 'phone') {
      if (!formData.phone) {
        newErrors.phone = '请输入手机号'
      } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
        newErrors.phone = '请输入正确的手机号'
      }
      if (!formData.verificationCode) {
        newErrors.verificationCode = '请输入验证码'
      }
    } else if (registerMethod === 'email') {
      if (!formData.email) {
        newErrors.email = '请输入邮箱'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = '请输入正确的邮箱格式'
      }
      if (!formData.verificationCode) {
        newErrors.verificationCode = '请输入验证码'
      }
    } else if (registerMethod === 'username') {
      if (!formData.username) {
        newErrors.username = '请输入用户名'
      } else if (formData.username.length < 3) {
        newErrors.username = '用户名至少3位'
      } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
        newErrors.username = '用户名只能包含字母、数字和下划线'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 发送验证码
  const handleSendCode = async () => {
    let target = ''
    let targetError = ''

    if (registerMethod === 'phone') {
      target = formData.phone
      targetError = '请先输入手机号'
    } else if (registerMethod === 'email') {
      target = formData.email
      targetError = '请先输入邮箱'
    }

    if (!target) {
      setErrors({ [registerMethod]: targetError })
      return
    }

    try {
      await authService.sendVerificationCode({
        type: 'register',
        phone: registerMethod === 'phone' ? formData.phone : undefined,
        email: registerMethod === 'email' ? formData.email : undefined
      })
      setCountdown(60)
      setErrors({})
    } catch (error) {
      setErrors({ verificationCode: error instanceof Error ? error.message : '发送验证码失败' })
    }
  }

  // 处理注册
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      await register({
        method: registerMethod,
        username: registerMethod === 'username' ? formData.username : undefined,
        phone: registerMethod === 'phone' ? formData.phone : undefined,
        email: registerMethod === 'email' ? formData.email : undefined,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        verificationCode: registerMethod !== 'username' ? formData.verificationCode : undefined,
        nickname: formData.nickname || undefined
      })
      onBack() // 注册成功后返回
    } catch (error) {
      setErrors({ 
        general: error instanceof Error ? error.message : '注册失败，请重试' 
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-8">
        {/* 顶部导航 */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>返回</span>
          </button>
          <h1 className="text-2xl font-bold text-gray-800">用户注册</h1>
          <div className="w-16"></div>
        </div>

        {/* 注册表单 */}
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            {/* 注册方式选择 */}
            <div className="bg-gray-50 px-6 py-4">
              <div className="flex space-x-1 bg-white rounded-lg p-1">
                <button
                  onClick={() => setRegisterMethod('phone')}
                  className={`flex-1 flex items-center justify-center space-x-1 py-2 px-2 rounded-md text-xs font-medium transition-all ${
                    registerMethod === 'phone'
                      ? 'bg-orange-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Smartphone className="w-3 h-3" />
                  <span>手机注册</span>
                </button>
                <button
                  onClick={() => setRegisterMethod('email')}
                  className={`flex-1 flex items-center justify-center space-x-1 py-2 px-2 rounded-md text-xs font-medium transition-all ${
                    registerMethod === 'email'
                      ? 'bg-orange-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Mail className="w-3 h-3" />
                  <span>邮箱注册</span>
                </button>
                <button
                  onClick={() => setRegisterMethod('username')}
                  className={`flex-1 flex items-center justify-center space-x-1 py-2 px-2 rounded-md text-xs font-medium transition-all ${
                    registerMethod === 'username'
                      ? 'bg-orange-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <User className="w-3 h-3" />
                  <span>账号注册</span>
                </button>
              </div>
            </div>

            {/* 表单内容 */}
            <form onSubmit={handleRegister} className="p-6 space-y-4">
              {/* 通用错误提示 */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              {/* 昵称（可选） */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  昵称 <span className="text-gray-400 text-xs">(可选)</span>
                </label>
                <div className="relative">
                  <UserPlus className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    value={formData.nickname}
                    onChange={(e) => setFormData({ ...formData, nickname: e.target.value })}
                    placeholder="请输入昵称"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all"
                  />
                </div>
              </div>

              {/* 手机号注册 */}
              {registerMethod === 'phone' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    手机号
                  </label>
                  <div className="relative">
                    <Smartphone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      placeholder="请输入手机号"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                        errors.phone ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>
                  {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                </div>
              )}

              {/* 邮箱注册 */}
              {registerMethod === 'email' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    邮箱
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="请输入邮箱"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                        errors.email ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>
                  {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                </div>
              )}

              {/* 用户名注册 */}
              {registerMethod === 'username' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    用户名
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      value={formData.username}
                      onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                      placeholder="请输入用户名"
                      className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                        errors.username ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>
                  {errors.username && <p className="text-red-500 text-xs mt-1">{errors.username}</p>}
                </div>
              )}

              {/* 验证码（手机和邮箱注册需要） */}
              {(registerMethod === 'phone' || registerMethod === 'email') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    验证码
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        value={formData.verificationCode}
                        onChange={(e) => setFormData({ ...formData, verificationCode: e.target.value })}
                        placeholder="请输入验证码"
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                          errors.verificationCode ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    <button
                      type="button"
                      onClick={handleSendCode}
                      disabled={countdown > 0}
                      className="px-4 py-3 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                    >
                      {countdown > 0 ? `${countdown}s` : '获取验证码'}
                    </button>
                  </div>
                  {errors.verificationCode && <p className="text-red-500 text-xs mt-1">{errors.verificationCode}</p>}
                </div>
              )}

              {/* 密码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  密码
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    placeholder="请输入密码（至少6位）"
                    className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                      errors.password ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
              </div>

              {/* 确认密码 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  确认密码
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    placeholder="请再次输入密码"
                    className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                      errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
                {errors.confirmPassword && <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>}
              </div>

              {/* 注册按钮 */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>注册中...</span>
                  </>
                ) : (
                  <span>立即注册</span>
                )}
              </button>

              {/* 登录链接 */}
              <div className="text-center pt-4">
                <span className="text-gray-600 text-sm">已有账号？</span>
                <button
                  type="button"
                  onClick={onSwitchToLogin}
                  className="text-orange-500 hover:text-orange-600 font-medium text-sm ml-1 transition-colors"
                >
                  立即登录
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
