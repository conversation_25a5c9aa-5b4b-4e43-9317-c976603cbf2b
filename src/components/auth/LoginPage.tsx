import { useState, useEffect } from 'react'
import { 
  ArrowLeft, 
  Smartphone, 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  Loader2,
  MessageCircle,
  User
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { authService } from '../../services/authService'

// 本地类型定义
type LoginMethod = 'phone' | 'email' | 'wechat'
interface FormErrors {
  [key: string]: string
}

interface LoginPageProps {
  onBack: () => void
  onSwitchToRegister: () => void
}

export function LoginPage({ onBack, onSwitchToRegister }: LoginPageProps) {
  const { login, isLoading } = useAuth()
  const [loginMethod, setLoginMethod] = useState<LoginMethod>('phone')
  const [showPassword, setShowPassword] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [errors, setErrors] = useState<FormErrors>({})
  
  // 表单数据
  const [formData, setFormData] = useState({
    phone: '',
    email: '',
    password: '',
    verificationCode: ''
  })

  // 验证码倒计时
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (loginMethod === 'phone') {
      if (!formData.phone) {
        newErrors.phone = '请输入手机号'
      } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
        newErrors.phone = '请输入正确的手机号'
      }
      if (!formData.verificationCode) {
        newErrors.verificationCode = '请输入验证码'
      }
    } else if (loginMethod === 'email') {
      if (!formData.email) {
        newErrors.email = '请输入邮箱'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = '请输入正确的邮箱格式'
      }
      if (!formData.password) {
        newErrors.password = '请输入密码'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (loginMethod === 'phone' && !formData.phone) {
      setErrors({ phone: '请先输入手机号' })
      return
    }

    try {
      await authService.sendVerificationCode({
        type: 'login',
        phone: loginMethod === 'phone' ? formData.phone : undefined
      })
      setCountdown(60)
      setErrors({})
    } catch (error) {
      setErrors({ verificationCode: error instanceof Error ? error.message : '发送验证码失败' })
    }
  }

  // 处理登录
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      await login({
        method: loginMethod,
        phone: loginMethod === 'phone' ? formData.phone : undefined,
        email: loginMethod === 'email' ? formData.email : undefined,
        password: loginMethod === 'email' ? formData.password : undefined,
        verificationCode: loginMethod === 'phone' ? formData.verificationCode : undefined
      })
      onBack() // 登录成功后返回
    } catch (error) {
      setErrors({ 
        general: error instanceof Error ? error.message : '登录失败，请重试' 
      })
    }
  }

  // 微信登录
  const handleWechatLogin = () => {
    const wechatUrl = authService.generateWechatLoginUrl()
    window.open(wechatUrl, '_blank', 'width=800,height=600')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-8">
        {/* 顶部导航 */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>返回</span>
          </button>
          <h1 className="text-2xl font-bold text-gray-800">用户登录</h1>
          <div className="w-16"></div>
        </div>

        {/* 登录表单 */}
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            {/* 登录方式选择 */}
            <div className="bg-gray-50 px-6 py-4">
              <div className="flex space-x-1 bg-white rounded-lg p-1">
                <button
                  onClick={() => setLoginMethod('phone')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                    loginMethod === 'phone'
                      ? 'bg-orange-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Smartphone className="w-4 h-4" />
                  <span>手机登录</span>
                </button>
                <button
                  onClick={() => setLoginMethod('email')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                    loginMethod === 'email'
                      ? 'bg-orange-500 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <Mail className="w-4 h-4" />
                  <span>邮箱登录</span>
                </button>
              </div>
            </div>

            {/* 表单内容 */}
            <form onSubmit={handleLogin} className="p-6 space-y-4">
              {/* 通用错误提示 */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{errors.general}</p>
                </div>
              )}

              {/* 手机号登录 */}
              {loginMethod === 'phone' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      手机号
                    </label>
                    <div className="relative">
                      <Smartphone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        placeholder="请输入手机号"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                          errors.phone ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      验证码
                    </label>
                    <div className="flex space-x-2">
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={formData.verificationCode}
                          onChange={(e) => setFormData({ ...formData, verificationCode: e.target.value })}
                          placeholder="请输入验证码"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                            errors.verificationCode ? 'border-red-300' : 'border-gray-300'
                          }`}
                        />
                      </div>
                      <button
                        type="button"
                        onClick={handleSendCode}
                        disabled={countdown > 0}
                        className="px-4 py-3 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
                      >
                        {countdown > 0 ? `${countdown}s` : '获取验证码'}
                      </button>
                    </div>
                    {errors.verificationCode && <p className="text-red-500 text-xs mt-1">{errors.verificationCode}</p>}
                  </div>
                </>
              )}

              {/* 邮箱登录 */}
              {loginMethod === 'email' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        placeholder="请输入邮箱"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                          errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      密码
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                        placeholder="请输入密码"
                        className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all ${
                          errors.password ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {errors.password && <p className="text-red-500 text-xs mt-1">{errors.password}</p>}
                  </div>
                </>
              )}

              {/* 登录按钮 */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>登录中...</span>
                  </>
                ) : (
                  <span>立即登录</span>
                )}
              </button>

              {/* 微信登录 */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">或</span>
                </div>
              </div>

              <button
                type="button"
                onClick={handleWechatLogin}
                className="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg flex items-center justify-center space-x-2"
              >
                <MessageCircle className="w-5 h-5" />
                <span>微信登录</span>
              </button>

              {/* 注册链接 */}
              <div className="text-center pt-4">
                <span className="text-gray-600 text-sm">还没有账号？</span>
                <button
                  type="button"
                  onClick={onSwitchToRegister}
                  className="text-orange-500 hover:text-orange-600 font-medium text-sm ml-1 transition-colors"
                >
                  立即注册
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
