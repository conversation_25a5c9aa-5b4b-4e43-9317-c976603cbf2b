import { useState, useEffect } from "react"
import { FeatureCard } from "./feature-card"
import { cn } from "../lib/utils"
import {
  GraduationCap,
  School,
  BookOpen,
  TrendingUp,
  BarChart3,
  FileText,
  Trophy,
  MessageCircle,
  Settings,
  HelpCircle,
  Home,
  ChevronLeft,
  ChevronRight,
  MapPin,
  Search,
  User,
  Download,
  Bell,
  BookOpenCheck,
  LineChart,
  Bot,
  Flame,
  Target,
  Zap,
  Lock,
  UserCheck
} from "lucide-react"

interface NavigationPageProps {
  onNavigate?: (page: string) => void
}

export function NavigationPage({ onNavigate }: NavigationPageProps = {}) {
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null)
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0)
  const [userScore, setUserScore] = useState("")
  const [userProvince] = useState("")
  const [userSubject] = useState("")
  const [userRanking, setUserRanking] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("普通类")
  const [firstChoice, setFirstChoice] = useState("物理")
  const [secondChoices, setSecondChoices] = useState(["生物", "地理"])
  const [showSubjectSelector, setShowSubjectSelector] = useState(false)

  // 广告banner数据
  const bannerAds = [
    {
      id: 1,
      title: "抓住暑期",
      subtitle: "开学轻松领跑",
      highlight: "同龄人",
      description: "7天特训改写你的高考命运",
      features: ["掌握提分秘籍", "低分高分", "专业名校"],
      bgColor: "from-blue-400 to-blue-600",
      image: GraduationCap
    },
    {
      id: 2,
      title: "AI智能",
      subtitle: "精准推荐",
      highlight: "理想大学",
      description: "大数据分析，科学填报志愿",
      features: ["智能匹配", "风险评估", "专业指导"],
      bgColor: "from-purple-400 to-purple-600",
      image: Bot
    },
    {
      id: 3,
      title: "专家团队",
      subtitle: "一对一指导",
      highlight: "名师护航",
      description: "资深专家为您量身定制方案",
      features: ["个性化方案", "实时答疑", "全程跟踪"],
      bgColor: "from-green-400 to-green-600",
      image: UserCheck
    }
  ]

  // 自动轮播
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBannerIndex((prev) => (prev + 1) % bannerAds.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [bannerAds.length])

  const features = [
    {
      id: "simulation",
      title: "模拟志愿填报",
      icon: FileText,
      color: "bg-gradient-to-br from-blue-500 to-blue-600",
      size: "large" as const,
      priority: 1
    },
    {
      id: "my-goal",
      title: "我的目标",
      icon: Target,
      color: "bg-gradient-to-br from-orange-500 to-red-500",
      size: "medium" as const,
      priority: 2
    },
    {
      id: "knowledge-base",
      title: "高考知识库",
      icon: BookOpen,
      color: "bg-gradient-to-br from-blue-500 to-indigo-600",
      size: "wide" as const,
      priority: 2
    },
    {
      id: "subject-selection",
      title: "高考选科",
      icon: BookOpenCheck,
      color: "bg-gradient-to-br from-amber-500 to-amber-600",
      size: "medium" as const,
      priority: 3
    },
    {
      id: "universities",
      title: "查大学",
      icon: School,
      color: "bg-gradient-to-br from-green-500 to-green-600",
      size: "wide" as const,
      priority: 3
    },
    {
      id: "majors",
      title: "查专业",
      icon: BookOpen,
      color: "bg-gradient-to-br from-purple-500 to-purple-600",
      size: "tall" as const,
      priority: 3
    },
    {
      id: "rankings",
      title: "查位次",
      icon: TrendingUp,
      color: "bg-gradient-to-br from-orange-500 to-orange-600",
      size: "medium" as const,
      priority: 4
    },
    {
      id: "scores",
      title: "查录取分数",
      icon: BarChart3,
      color: "bg-gradient-to-br from-red-500 to-red-600",
      size: "medium" as const,
      priority: 5
    },
    {
      id: "qs-ranking",
      title: "全球QS大学排名",
      icon: Trophy,
      color: "bg-gradient-to-br from-teal-500 to-teal-600",
      size: "medium" as const,
      priority: 6
    },
    {
      id: "ai-chat",
      title: "智能问答",
      icon: MessageCircle,
      color: "bg-gradient-to-br from-indigo-500 to-indigo-600",
      size: "wide" as const,
      priority: 7
    },
    {
      id: "enrollment-plan",
      title: "招生计划",
      icon: GraduationCap,
      color: "bg-gradient-to-br from-pink-500 to-pink-600",
      size: "small" as const,
      priority: 8
    },
    {
      id: "province-control-line",
      title: "省控线",
      icon: LineChart,
      color: "bg-gradient-to-br from-cyan-500 to-cyan-600",
      size: "medium" as const,
      priority: 6
    },
    {
      id: "api-test",
      title: "API测试",
      icon: Settings,
      color: "bg-gradient-to-br from-gray-500 to-gray-600",
      size: "small" as const,
      priority: 8
    },
    {
      id: "settings",
      title: "系统设置",
      icon: Settings,
      color: "bg-gradient-to-br from-gray-500 to-gray-600",
      size: "small" as const,
      priority: 9
    }
  ]

  const handleFeatureClick = (featureId: string) => {
    setSelectedFeature(featureId)
    console.log(`点击了功能: ${featureId}`)

    // 处理特定功能的导航
    if (featureId === 'simulation' && onNavigate) {
      onNavigate('volunteer')
    } else if (featureId === 'my-goal' && onNavigate) {
      onNavigate('my-goal')
    } else if (featureId === 'knowledge-base' && onNavigate) {
      onNavigate('knowledge-base')
    } else if (featureId === 'universities' && onNavigate) {
      onNavigate('universities')
    } else if (featureId === 'majors' && onNavigate) {
      onNavigate('majors')
    } else if (featureId === 'major-detail' && onNavigate) {
      onNavigate('major-detail')
    } else if (featureId === 'rankings' && onNavigate) {
      onNavigate('rankings')
    } else if (featureId === 'qs-ranking' && onNavigate) {
      onNavigate('qs-ranking')
    } else if (featureId === 'scores' && onNavigate) {
      onNavigate('scores')
    } else if (featureId === 'score-test' && onNavigate) {
      onNavigate('score-test')
    } else if (featureId === 'ai-chat' && onNavigate) {
      onNavigate('ai-chat')
    } else if (featureId === 'enrollment-plan' && onNavigate) {
      onNavigate('enrollment-plan')
    } else if (featureId === 'subject-selection' && onNavigate) {
      onNavigate('subject-selection')
    } else if (featureId === 'province-control-line' && onNavigate) {
      onNavigate('province-control-line')
    }
  }

  const handleBannerPrev = () => {
    setCurrentBannerIndex((prev) => (prev - 1 + bannerAds.length) % bannerAds.length)
  }

  const handleBannerNext = () => {
    setCurrentBannerIndex((prev) => (prev + 1) % bannerAds.length)
  }

  const handleSimulateVolunteer = () => {
    if (onNavigate) {
      onNavigate('volunteer')
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 移除动态背景以提升性能 */}
      {/* <DotPattern
        className="absolute inset-0 opacity-30"
        width={20}
        height={20}
        glow={true}
      /> */}

      {/* 移除浮动装饰地球以提升性能 */}
      {/* <div className="fixed top-20 right-10 w-32 h-32 opacity-20 pointer-events-none z-20 hidden lg:block animate-float">
        <Globe className="w-full h-full" />
      </div> */}

      <div className="relative z-10 bg-gradient-to-br from-blue-50/80 via-white/80 to-blue-50/80 backdrop-blur-sm min-h-screen">
        {/* 顶部导航栏 */}
        <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              {/* 左侧Logo区域 */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 ml-4">
                  <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                    <GraduationCap className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-gray-700 font-medium">飞鸿AI学业规划</span>
                </div>
              </div>

              {/* 中间搜索框 */}
              <div className="flex-1 max-w-md mx-8">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="查大学、查专业、提问答"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 text-sm"
                  />
                  <button className="absolute right-0 top-0 bottom-0 px-4 bg-orange-500 text-white rounded-r-lg hover:bg-orange-600 transition-colors duration-200 text-sm font-medium">
                    搜索
                  </button>
                </div>
              </div>

              {/* 右侧用户信息区域 */}
              <div className="flex items-center space-x-6">
                {/* 地区信息 */}
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  <span>安徽</span>
                  <span>物生地</span>
                  <span className="font-medium text-orange-600">467分</span>
                  <Flame className="w-4 h-4 text-orange-500" />
                </div>

                {/* 通知铃铛 */}
                <div className="relative">
                  <Bell className="w-5 h-5 text-gray-600 hover:text-orange-500 cursor-pointer transition-colors" />
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                </div>

                {/* 用户头像和信息 */}
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2 bg-orange-50 px-3 py-1.5 rounded-full">
                    <span className="text-sm text-gray-700">会员中心</span>
                  </div>
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                </div>

                {/* 下载APP按钮 */}
                <button className="flex items-center space-x-1 bg-orange-500 text-white px-3 py-1.5 rounded-lg hover:bg-orange-600 transition-colors duration-200 text-sm font-medium">
                  <Download className="w-4 h-4" />
                  <span>下载APP</span>
                </button>
              </div>
            </div>
          </div>
        </header>
        {/* 顶部Banner区域 */}
        <div className="container mx-auto px-4 py-6">
          <div className="flex gap-6">
            {/* 左侧广告轮播 */}
            <div className="flex-1 relative">
              <div className="relative overflow-hidden rounded-3xl shadow-2xl h-[600px]">
                {bannerAds.map((ad, index) => (
                  <div
                    key={ad.id}
                    className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
                      index === currentBannerIndex ? 'translate-x-0' :
                      index < currentBannerIndex ? '-translate-x-full' : 'translate-x-full'
                    }`}
                  >
                    <div className={`w-full h-full bg-gradient-to-r ${ad.bgColor} relative overflow-hidden`}>
                      {/* 移除背景装饰以提升性能 */}
                      {/* <div className="absolute inset-0 opacity-20">
                        <DotPattern width={30} height={30} className="text-white/30" />
                      </div> */}

                      <div className="relative z-10 flex items-center justify-between h-full p-8">
                        {/* 左侧内容 */}
                        <div className="flex-1 text-white">
                          <div className="mb-4">
                            <h2 className="text-4xl font-bold mb-2 text-white">
                              {ad.title}
                            </h2>
                            <h3 className="text-2xl font-semibold mb-1">{ad.subtitle}</h3>
                            <span className="text-3xl font-bold text-yellow-300">{ad.highlight}</span>
                          </div>

                          <p className="text-xl mb-6 text-white/90">{ad.description}</p>

                          <div className="flex flex-wrap gap-3 mb-6">
                            {ad.features.map((feature, idx) => (
                              <span
                                key={idx}
                                className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium border border-white/30"
                              >
                                {feature}
                              </span>
                            ))}
                          </div>

                          <button
                            className="bg-white/20 hover:bg-white/30 text-white border border-white/30 px-6 py-2 rounded-lg transition-colors duration-200"
                            onClick={() => handleFeatureClick("simulation")}
                          >
                            立即体验
                          </button>
                        </div>

                        {/* 右侧图像 */}
                        <div className="flex-shrink-0 ml-8">
                          <ad.image className="w-32 h-32 opacity-80 text-white" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* 导航按钮 */}
                <button
                  onClick={handleBannerPrev}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3"
                >
                  <ChevronLeft className="w-6 h-6 text-white" />
                </button>
                <button
                  onClick={handleBannerNext}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3"
                >
                  <ChevronRight className="w-6 h-6 text-white" />
                </button>

                {/* 指示器 */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
                  {bannerAds.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentBannerIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-200 ${
                        index === currentBannerIndex
                          ? 'bg-white scale-125'
                          : 'bg-white/50 hover:bg-white/70'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* 右侧模拟报志愿功能区 */}
            <div className="w-96 flex-shrink-0">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden h-[600px] flex flex-col">
                {/* 标题区域 */}
                <div className="bg-blue-50 px-6 py-4 border-b border-blue-100">
                  <h3 className="text-lg font-bold text-gray-800 text-center">模拟报志愿</h3>
                </div>

                {/* 表单区域 */}
                <div className="flex-1 p-4 space-y-3">
                  {/* 类别选择 */}
                  <div className="flex space-x-2">
                    {["普通类", "艺术类"].map((category) => (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                          selectedCategory === category
                            ? "bg-orange-500 text-white"
                            : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>

                  {/* 首选科目 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      首选 <span className="text-gray-500">(2选1)</span>
                    </label>
                    <div className="flex space-x-2">
                      {["物理", "历史"].map((subject) => (
                        <button
                          key={subject}
                          onClick={() => setFirstChoice(subject)}
                          className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 border ${
                            firstChoice === subject
                              ? "bg-orange-500 text-white border-orange-500"
                              : "bg-white text-gray-600 border-gray-300 hover:border-orange-300"
                          }`}
                        >
                          {subject}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 次选科目 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      次选 <span className="text-gray-500">(4选2)</span>
                    </label>
                    <div className="flex space-x-2">
                      {["化学", "生物", "政治", "地理"].map((subject) => (
                        <button
                          key={subject}
                          onClick={() => {
                            if (secondChoices.includes(subject)) {
                              setSecondChoices(secondChoices.filter(s => s !== subject))
                            } else if (secondChoices.length < 2) {
                              setSecondChoices([...secondChoices, subject])
                            }
                          }}
                          className={`px-2 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 border ${
                            secondChoices.includes(subject)
                              ? "bg-orange-500 text-white border-orange-500"
                              : "bg-white text-gray-600 border-gray-300 hover:border-orange-300"
                          }`}
                        >
                          {subject}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 分数输入 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">分数</label>
                    <div className="flex space-x-2">
                      <div className="flex-1 relative">
                        <input
                          type="number"
                          value={userScore}
                          onChange={(e) => setUserScore(e.target.value)}
                          placeholder="467"
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 text-sm"
                        />
                        <span className="absolute right-2 top-2 text-xs text-gray-500">分</span>
                      </div>
                      <div className="flex-1 relative">
                        <input
                          type="number"
                          value={userRanking}
                          onChange={(e) => setUserRanking(e.target.value)}
                          placeholder="183207"
                          className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 text-sm"
                        />
                        <span className="absolute right-2 top-2 text-xs text-gray-500">位</span>
                      </div>
                    </div>
                  </div>

                  {/* 分数分析图表 */}
                  <div className="bg-orange-50 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-700">467 分</span>
                      <span className="text-xs text-gray-500">181797-183207名</span>
                    </div>
                    <div className="text-xs text-orange-600 mb-2">超过本省42%考生</div>

                    {/* 简化的分数线图 */}
                    <div className="relative h-6 bg-gradient-to-r from-red-200 via-yellow-200 to-green-200 rounded-full overflow-hidden">
                      <div className="absolute left-0 top-0 h-full w-0.5 bg-gray-400"></div>
                      <div className="absolute right-0 top-0 h-full w-0.5 bg-gray-400"></div>
                      <div
                        className="absolute top-0 h-full w-0.5 bg-orange-500"
                        style={{ left: '42%' }}
                      ></div>
                      <div className="absolute -top-5 text-xs text-gray-500" style={{ left: '0%' }}>200分</div>
                      <div className="absolute -top-5 text-xs text-gray-500" style={{ right: '0%' }}>750分</div>
                    </div>
                  </div>

                  {/* 推荐院校统计 */}
                  <div className="bg-gray-50 rounded-lg p-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-600">推荐院校</span>
                      <div className="flex space-x-3">
                        <span className="text-red-500">227 可冲</span>
                        <span className="text-green-500">-- 较稳妥</span>
                        <span className="text-blue-500">-- 可保底</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 底部按钮 */}
                <div className="p-3 border-t border-gray-100">
                  <button
                    onClick={handleSimulateVolunteer}
                    className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white py-2.5 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm"
                  >
                    智能推荐大学
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="container mx-auto px-4 py-8">
          {/* 功能网格 */}
          <div className="feature-grid mb-8">
            {features.map((feature) => (
              <div
                key={feature.id}
                className={cn(
                  "relative",
                  feature.size === 'wide' && "feature-grid-item-wide",
                  feature.size === 'large' && "feature-grid-item-large",
                  feature.size === 'tall' && "feature-grid-item-tall"
                )}
              >
                <FeatureCard
                  title={feature.title}
                  icon={feature.icon}
                  color={feature.color}
                  size={feature.size}
                  onClick={() => handleFeatureClick(feature.id)}
                  className={cn(
                    "h-full w-full",
                    selectedFeature === feature.id ? "ring-4 ring-blue-300 shadow-xl" : ""
                  )}
                />
              </div>
            ))}
          </div>

          {/* 数据统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">2856</div>
              <div className="text-sm text-gray-600 font-medium">全国高校</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">703</div>
              <div className="text-sm text-gray-600 font-medium">本科专业</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">1247</div>
              <div className="text-sm text-gray-600 font-medium">今日查询</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">98.5<span className="text-orange-600 text-xl font-bold">%</span></div>
              <div className="text-sm text-gray-600 font-medium">推荐准确率</div>
            </div>
          </div>

          {/* 快速入口 */}
          <div className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 rounded-2xl shadow-2xl p-1 mb-12">
            <div className="bg-white rounded-xl p-6">
              <div className="flex items-center justify-center mb-6">
                <div className="bg-gradient-to-r from-orange-500 to-pink-500 p-2 rounded-full mr-3">
                  <Home className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent">
                  快速入口
                </h3>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                <button
                  className="group relative overflow-hidden bg-white border-2 border-orange-200 hover:border-orange-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("simulation")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors duration-300">
                      <FileText className="w-6 h-6 text-orange-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">开始填报</span>
                  </div>
                </button>
                <button
                  className="group relative overflow-hidden bg-white border-2 border-green-200 hover:border-green-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("universities")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-green-50 to-emerald-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors duration-300">
                      <School className="w-6 h-6 text-green-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">院校查询</span>
                  </div>
                </button>
                <button
                  className="group relative overflow-hidden bg-white border-2 border-purple-200 hover:border-purple-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("majors")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-violet-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors duration-300">
                      <BookOpen className="w-6 h-6 text-purple-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">专业查询</span>
                  </div>
                </button>
                <button
                  className="group relative overflow-hidden bg-white border-2 border-blue-200 hover:border-blue-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("rankings")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-cyan-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors duration-300">
                      <TrendingUp className="w-6 h-6 text-blue-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">查位次</span>
                  </div>
                </button>
                <button
                  className="group relative overflow-hidden bg-white border-2 border-red-200 hover:border-red-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("scores")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-red-50 to-rose-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors duration-300">
                      <BarChart3 className="w-6 h-6 text-red-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">分数查询</span>
                  </div>
                </button>
                <button
                  className="group relative overflow-hidden bg-white border-2 border-gray-200 hover:border-gray-400 rounded-xl p-4 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
                  onClick={() => handleFeatureClick("score-test")}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-slate-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex flex-col items-center space-y-2">
                    <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-gray-200 transition-colors duration-300">
                      <Settings className="w-6 h-6 text-gray-600" />
                    </div>
                    <span className="text-gray-700 font-medium text-sm">API测试</span>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* 技术特色展示 */}
          <div className="mb-12">
            <div className="bg-gray-900 rounded-3xl p-8 relative overflow-hidden">
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black opacity-90"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-500/20 to-transparent rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-purple-500/20 to-transparent rounded-full blur-3xl"></div>
              
              <div className="relative z-10">
                <div className="text-center mb-10">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-4">
                    <Zap className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-3">
                    技术特色
                  </h3>
                  <p className="text-gray-300 text-lg">基于先进技术栈，为您提供最佳体验</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    { 
                      name: "AI智能推荐", 
                      icon: Bot, 
                      description: "基于机器学习算法，智能分析匹配最适合的院校和专业",
                      color: "from-blue-400 to-cyan-400"
                    },
                    { 
                      name: "大数据分析", 
                      icon: BarChart3, 
                      description: "整合海量历史数据，提供精准的录取概率预测",
                      color: "from-green-400 to-emerald-400"
                    },
                    { 
                      name: "实时更新", 
                      icon: Zap, 
                      description: "数据实时同步更新，确保信息的准确性和时效性",
                      color: "from-yellow-400 to-orange-400"
                    },
                    { 
                      name: "精准匹配", 
                      icon: Target, 
                      description: "多维度智能匹配算法，找到最符合条件的选择",
                      color: "from-purple-400 to-pink-400"
                    },
                    { 
                      name: "专业指导", 
                      icon: UserCheck, 
                      description: "专业团队提供一对一咨询，全程指导志愿填报",
                      color: "from-indigo-400 to-blue-400"
                    },
                    { 
                      name: "安全可靠", 
                      icon: Lock, 
                      description: "采用银行级安全标准，保护用户隐私和数据安全",
                      color: "from-red-400 to-rose-400"
                    }
                  ].map((tech, idx) => (
                    <div
                      key={idx}
                      className="group bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300 hover:scale-105 cursor-pointer"
                    >
                      <div className="flex items-start space-x-4">
                        <div className={`flex-shrink-0 w-12 h-12 bg-gradient-to-r ${tech.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                          <tech.icon className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-white font-bold text-lg mb-2 group-hover:text-blue-300 transition-colors duration-300">
                            {tech.name}
                          </h4>
                          <p className="text-gray-300 text-sm leading-relaxed">
                            {tech.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* 底部统计信息 */}
                <div className="mt-10 grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-1">99.9%</div>
                    <div className="text-gray-400 text-sm">系统可用性</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-1">50ms</div>
                    <div className="text-gray-400 text-sm">平均响应时间</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-1">24/7</div>
                    <div className="text-gray-400 text-sm">全天候服务</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-1">100万+</div>
                    <div className="text-gray-400 text-sm">服务用户数</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部信息 */}
          <div className="text-center text-gray-500 text-sm bg-white/50 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <div className="text-gray-700 font-medium mb-2">
              © 2024 高考志愿辅助填报系统 · 助力每一个梦想
            </div>
            <p className="mt-2">数据来源：教育部阳光高考平台</p>
            <div className="flex justify-center items-center mt-4 space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs">系统正常运行</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-600 font-bold text-sm">99.9</span>
                <span className="text-xs">% 可用性</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
