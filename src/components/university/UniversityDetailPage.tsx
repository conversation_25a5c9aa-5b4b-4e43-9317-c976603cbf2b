import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'

// 大学类型定义
interface University {
  id: string
  name: string
  englishName?: string
  logo?: string
  type: '985' | '211' | '双一流' | '普通本科' | '专科'
  level: '本科' | '专科'
  location: {
    province: string
    city: string
  }
  category: '综合' | '理工' | '师范' | '农林' | '医药' | '财经' | '政法' | '艺术' | '体育' | '民族' | '军事' | '其他'
  isPublic: boolean
  foundedYear?: number
  website?: string
  phone?: string
  address?: string
  description?: string
  ranking?: {
    national?: number
    category?: number
  }
  admissionInfo?: {
    minScore?: number
    avgScore?: number
    maxScore?: number
    year?: number
    province?: string
  }
  majors?: string[]
  features?: string[]
  images?: string[]
}
import { 
  ArrowLeft,
  MapPin, 
  Calendar, 
  GraduationCap, 
  Star, 
  TrendingUp,
  ExternalLink,
  Phone,
  Globe,
  Users,
  BookOpen,
  Award,
  Building,
  Heart,
  Share2,
  Download,
  ChevronRight,
  Target,
  Trophy,
  Clock
} from 'lucide-react'

interface UniversityDetailPageProps {
  university: University
  onBack?: () => void
  className?: string
}

export function UniversityDetailPage({ university, onBack, className }: UniversityDetailPageProps) {
  const getTypeColor = (type: University['type']) => {
    switch (type) {
      case '985':
        return 'bg-red-100 text-red-800 border-red-200'
      case '211':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case '双一流':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case '普通本科':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case '专科':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryColor = (category: University['category']) => {
    const colors = {
      '综合': 'bg-green-100 text-green-800',
      '理工': 'bg-blue-100 text-blue-800',
      '师范': 'bg-yellow-100 text-yellow-800',
      '医药': 'bg-red-100 text-red-800',
      '财经': 'bg-purple-100 text-purple-800',
      '艺术': 'bg-pink-100 text-pink-800',
      '农林': 'bg-emerald-100 text-emerald-800',
      '政法': 'bg-indigo-100 text-indigo-800',
      '体育': 'bg-orange-100 text-orange-800',
      '民族': 'bg-cyan-100 text-cyan-800',
      '军事': 'bg-slate-100 text-slate-800',
      '其他': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其他']
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Building className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-gray-900">{university.name}</h1>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Heart className="w-4 h-4" />
                收藏
              </Button>
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Share2 className="w-4 h-4" />
                分享
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 大学头部横幅 */}
        <Card className="p-8 mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-4xl font-bold text-gray-900">{university.name}</h1>
                {university.ranking?.national && (
                  <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium border border-yellow-200">
                    <Trophy className="w-4 h-4" />
                    全国第{university.ranking.national}名
                  </div>
                )}
              </div>
              
              {university.englishName && (
                <p className="text-xl text-gray-600 mb-4 font-medium">{university.englishName}</p>
              )}
              
              {/* 标签组 */}
              <div className="flex flex-wrap gap-3 mb-6">
                <span className={cn("px-4 py-2 rounded-full text-sm font-medium border", getTypeColor(university.type))}>
                  {university.type}
                </span>
                <span className={cn("px-4 py-2 rounded-full text-sm font-medium", getCategoryColor(university.category))}>
                  {university.category}类
                </span>
                <span className="px-4 py-2 rounded-full text-sm font-medium bg-gray-100 text-gray-700 border border-gray-200">
                  {university.isPublic ? '公办' : '民办'}
                </span>
                <span className="px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-700 border border-blue-200">
                  {university.level}院校
                </span>
              </div>

              {/* 基本信息快览 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="flex items-center gap-3">
                  <MapPin className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="text-sm text-gray-500">所在地区</div>
                    <div className="font-semibold text-gray-900">{university.location.province} · {university.location.city}</div>
                  </div>
                </div>
                
                {university.foundedYear && (
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-green-600" />
                    <div>
                      <div className="text-sm text-gray-500">建校时间</div>
                      <div className="font-semibold text-gray-900">{university.foundedYear}年</div>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center gap-3">
                  <GraduationCap className="w-5 h-5 text-purple-600" />
                  <div>
                    <div className="text-sm text-gray-500">办学层次</div>
                    <div className="font-semibold text-gray-900">{university.level}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Users className="w-5 h-5 text-orange-600" />
                  <div>
                    <div className="text-sm text-gray-500">办学性质</div>
                    <div className="font-semibold text-gray-900">{university.isPublic ? '公办' : '民办'}</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 分数信息卡片 */}
            {university.admissionInfo && (
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200 min-w-[200px]">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {university.admissionInfo.avgScore}
                  </div>
                  <div className="text-sm text-gray-600 mb-3">平均录取分</div>
                  {university.admissionInfo.minScore && university.admissionInfo.maxScore && (
                    <div className="text-sm text-gray-500 mb-3">
                      分数区间：{university.admissionInfo.minScore}-{university.admissionInfo.maxScore}
                    </div>
                  )}
                  <div className="text-xs text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                    {university.admissionInfo.year}年数据
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center gap-4 pt-6 border-t border-blue-200">
            {university.website && (
              <Button className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2">
                <Globe className="w-4 h-4" />
                访问官网
                <ExternalLink className="w-3 h-3" />
              </Button>
            )}
            {university.phone && (
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                联系电话
              </Button>
            )}
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              下载招生简章
            </Button>
          </div>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 学校简介 */}
            {university.description && (
              <Card className="p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-6 h-6 text-blue-600" />
                  学校简介
                </h2>
                <div className="prose prose-gray max-w-none">
                  <p className="text-gray-700 leading-relaxed text-base">{university.description}</p>
                </div>
              </Card>
            )}

            {/* 热门专业 */}
            {university.majors && university.majors.length > 0 && (
              <Card className="p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Award className="w-6 h-6 text-green-600" />
                  热门专业
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {university.majors.map((major, index) => (
                    <div 
                      key={index}
                      className="group p-4 bg-green-50 hover:bg-green-100 text-green-800 rounded-lg border border-green-200 transition-colors cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{major}</span>
                        <ChevronRight className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* 录取分析 */}
            {university.admissionInfo && (
              <Card className="p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Target className="w-6 h-6 text-orange-600" />
                  录取分析
                </h2>
                <div className="space-y-4">
                  <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-orange-800">录取分数趋势</span>
                      <span className="text-xs text-orange-600">{university.admissionInfo.year}年</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">{university.admissionInfo.minScore}</div>
                        <div className="text-xs text-orange-700">最低分</div>
                      </div>
                      <div className="flex-1 h-2 bg-orange-200 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-orange-400 to-orange-600 rounded-full" style={{width: '75%'}}></div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">{university.admissionInfo.maxScore}</div>
                        <div className="text-xs text-orange-700">最高分</div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* 右侧信息栏 */}
          <div className="space-y-6">
            {/* 学校特色 */}
            {university.features && university.features.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-600" />
                  学校特色
                </h3>
                <div className="space-y-3">
                  {university.features.map((feature, index) => (
                    <div 
                      key={index}
                      className="p-3 bg-blue-50 text-blue-800 rounded-lg text-sm font-medium border border-blue-200"
                    >
                      {feature}
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* 排名信息 */}
            {university.ranking && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                  排名信息
                </h3>
                <div className="space-y-4">
                  {university.ranking.national && (
                    <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <span className="text-purple-700 font-medium">全国排名</span>
                      <span className="font-bold text-purple-600 text-lg">第{university.ranking.national}名</span>
                    </div>
                  )}
                  {university.ranking.category && (
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200">
                      <span className="text-green-700 font-medium">同类排名</span>
                      <span className="font-bold text-green-600 text-lg">第{university.ranking.category}名</span>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* 联系信息 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">联系信息</h3>
              <div className="space-y-4 text-sm">
                {university.address && (
                  <div className="flex items-start gap-3">
                    <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                    <div>
                      <span className="text-gray-600 block">学校地址</span>
                      <span className="text-gray-900 font-medium">{university.address}</span>
                    </div>
                  </div>
                )}
                {university.phone && (
                  <div className="flex items-start gap-3">
                    <Phone className="w-4 h-4 text-gray-500 mt-0.5" />
                    <div>
                      <span className="text-gray-600 block">联系电话</span>
                      <span className="text-gray-900 font-medium">{university.phone}</span>
                    </div>
                  </div>
                )}
                {university.website && (
                  <div className="flex items-start gap-3">
                    <Globe className="w-4 h-4 text-gray-500 mt-0.5" />
                    <div>
                      <span className="text-gray-600 block">官方网站</span>
                      <a 
                        href={university.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1"
                      >
                        访问官网
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* 快速操作 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">快速操作</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Target className="w-4 h-4 mr-2" />
                  查看录取概率
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Users className="w-4 h-4 mr-2" />
                  对比其他院校
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <BookOpen className="w-4 h-4 mr-2" />
                  查看专业详情
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  下载资料
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
