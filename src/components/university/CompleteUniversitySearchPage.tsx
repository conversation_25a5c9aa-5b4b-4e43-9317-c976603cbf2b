import { useState, useEffect, useMemo } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { UniversityCard } from './UniversityCard'
import { AdvancedSearchFilters } from './AdvancedSearchFilters'
import { UniversityDetailPage } from './UniversityDetailPage'
import { mockUniversities } from '../../data/universities'
import { cn } from '../../lib/utils'

// 搜索筛选类型
interface SearchFilters {
  keyword?: string
  province?: string
  city?: string
  type?: string[]
  category?: string[]
  isPublic?: boolean
  minScore?: number
  maxScore?: number
  sortBy?: 'name' | 'ranking' | 'score' | 'founded'
  sortOrder?: 'asc' | 'desc'
}

// 大学类型定义
interface University {
  id: string
  name: string
  englishName?: string
  logo?: string
  type: '985' | '211' | '双一流' | '普通本科' | '专科'
  level: '本科' | '专科'
  location: {
    province: string
    city: string
  }
  category: '综合' | '理工' | '师范' | '农林' | '医药' | '财经' | '政法' | '艺术' | '体育' | '民族' | '军事' | '其他'
  isPublic: boolean
  foundedYear?: number
  website?: string
  phone?: string
  address?: string
  description?: string
  ranking?: {
    national?: number
    category?: number
  }
  admissionInfo?: {
    minScore?: number
    avgScore?: number
    maxScore?: number
    year?: number
    province?: string
  }
  majors?: string[]
  features?: string[]
  images?: string[]
}
import { 
  ArrowLeft, 
  Grid3X3, 
  List, 
  SortAsc, 
  SortDesc,
  School,
  TrendingUp,
  Users,
  MapPin,
  BarChart3,
  Award
} from 'lucide-react'

interface CompleteUniversitySearchPageProps {
  onBack?: () => void
}

export function CompleteUniversitySearchPage({ onBack }: CompleteUniversitySearchPageProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    sortBy: 'ranking',
    sortOrder: 'asc'
  })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedUniversity, setSelectedUniversity] = useState<University | null>(null)
  const pageSize = 12

  // 筛选和排序逻辑
  const filteredUniversities = useMemo(() => {
    let result = [...mockUniversities]

    // 关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      result = result.filter(uni => 
        uni.name.toLowerCase().includes(keyword) ||
        uni.englishName?.toLowerCase().includes(keyword) ||
        uni.location.province.toLowerCase().includes(keyword) ||
        uni.location.city.toLowerCase().includes(keyword)
      )
    }

    // 省份筛选
    if (filters.province) {
      result = result.filter(uni => uni.location.province === filters.province)
    }

    // 城市筛选
    if (filters.city) {
      result = result.filter(uni => uni.location.city === filters.city)
    }

    // 院校类型筛选
    if (filters.type && filters.type.length > 0) {
      result = result.filter(uni => filters.type!.includes(uni.type))
    }

    // 院校分类筛选
    if (filters.category && filters.category.length > 0) {
      result = result.filter(uni => filters.category!.includes(uni.category))
    }

    // 公办/民办筛选
    if (filters.isPublic !== undefined) {
      result = result.filter(uni => uni.isPublic === filters.isPublic)
    }

    // 分数范围筛选
    if (filters.minScore || filters.maxScore) {
      result = result.filter(uni => {
        if (!uni.admissionInfo?.avgScore) return false
        const score = uni.admissionInfo.avgScore
        if (filters.minScore && score < filters.minScore) return false
        if (filters.maxScore && score > filters.maxScore) return false
        return true
      })
    }

    // 排序
    result.sort((a, b) => {
      let aValue: any, bValue: any

      switch (filters.sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'ranking':
          aValue = a.ranking?.national || 999
          bValue = b.ranking?.national || 999
          break
        case 'score':
          aValue = a.admissionInfo?.avgScore || 0
          bValue = b.admissionInfo?.avgScore || 0
          break
        case 'founded':
          aValue = a.foundedYear || 0
          bValue = b.foundedYear || 0
          break
        default:
          return 0
      }

      if (typeof aValue === 'string') {
        return filters.sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      return filters.sortOrder === 'asc' 
        ? aValue - bValue 
        : bValue - aValue
    })

    return result
  }, [filters])

  // 分页数据
  const paginatedUniversities = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredUniversities.slice(startIndex, startIndex + pageSize)
  }, [filteredUniversities, currentPage, pageSize])

  const totalPages = Math.ceil(filteredUniversities.length / pageSize)

  // 重置页码当筛选条件改变时
  useEffect(() => {
    setCurrentPage(1)
  }, [filters])

  // 统计信息
  const stats = useMemo(() => {
    const total = filteredUniversities.length
    const types = filteredUniversities.reduce((acc, uni) => {
      acc[uni.type] = (acc[uni.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const provinces = new Set(filteredUniversities.map(uni => uni.location.province)).size
    const avgScore = filteredUniversities
      .filter(uni => uni.admissionInfo?.avgScore)
      .reduce((sum, uni) => sum + (uni.admissionInfo?.avgScore || 0), 0) / 
      filteredUniversities.filter(uni => uni.admissionInfo?.avgScore).length

    return { total, types, provinces, avgScore: Math.round(avgScore) || 0 }
  }, [filteredUniversities])

  // 如果选择了大学，显示详情页
  if (selectedUniversity) {
    return (
      <UniversityDetailPage
        university={selectedUniversity}
        onBack={() => setSelectedUniversity(null)}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <School className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-gray-900">查大学</h1>
                <span className="text-sm text-gray-500">发现理想院校</span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧筛选器 */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <AdvancedSearchFilters 
                filters={filters} 
                onFiltersChange={setFilters}
              />
            </div>
          </div>

          {/* 右侧内容区 */}
          <div className="lg:col-span-3">
            {/* 统计信息卡片 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <Card className="p-4 text-center bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <div className="flex items-center justify-center mb-2">
                  <School className="w-5 h-5 text-blue-600 mr-2" />
                  <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                </div>
                <div className="text-sm text-blue-700 font-medium">找到院校</div>
              </Card>
              <Card className="p-4 text-center bg-gradient-to-br from-red-50 to-red-100 border-red-200">
                <div className="flex items-center justify-center mb-2">
                  <Award className="w-5 h-5 text-red-600 mr-2" />
                  <div className="text-2xl font-bold text-red-600">{stats.types['985'] || 0}</div>
                </div>
                <div className="text-sm text-red-700 font-medium">985院校</div>
              </Card>
              <Card className="p-4 text-center bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <div className="flex items-center justify-center mb-2">
                  <MapPin className="w-5 h-5 text-green-600 mr-2" />
                  <div className="text-2xl font-bold text-green-600">{stats.provinces}</div>
                </div>
                <div className="text-sm text-green-700 font-medium">覆盖省份</div>
              </Card>
              <Card className="p-4 text-center bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <div className="flex items-center justify-center mb-2">
                  <BarChart3 className="w-5 h-5 text-orange-600 mr-2" />
                  <div className="text-2xl font-bold text-orange-600">{stats.avgScore}</div>
                </div>
                <div className="text-sm text-orange-700 font-medium">平均分数</div>
              </Card>
            </div>

            {/* 结果头部信息 */}
            <div className="flex items-center justify-between mb-6 bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center gap-4">
                <div className="text-sm text-gray-600">
                  共找到 <span className="font-bold text-gray-900">{filteredUniversities.length}</span> 所院校
                  {filters.keyword && (
                    <span className="ml-2">
                      搜索：<span className="font-medium text-blue-600">"{filters.keyword}"</span>
                    </span>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">排序：</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setFilters(prev => ({
                    ...prev,
                    sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc'
                  }))}
                  className="flex items-center gap-1 text-sm"
                >
                  {filters.sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                  {filters.sortBy === 'ranking' && '排名'}
                  {filters.sortBy === 'name' && '名称'}
                  {filters.sortBy === 'score' && '分数'}
                  {filters.sortBy === 'founded' && '建校时间'}
                </Button>
              </div>
            </div>

            {/* 大学列表 */}
            {paginatedUniversities.length > 0 ? (
              <div className={cn(
                viewMode === 'list' ? "space-y-4" : "grid grid-cols-1 xl:grid-cols-2 gap-4"
              )}>
                {paginatedUniversities.map((university) => (
                  <UniversityCard
                    key={university.id}
                    university={university}
                    onClick={() => console.log('点击大学卡片:', university.name)}
                    onViewDetail={() => setSelectedUniversity(university)}
                    className="university-card"
                  />
                ))}
              </div>
            ) : (
              <Card className="p-12 text-center">
                <School className="w-20 h-20 text-gray-300 mx-auto mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">未找到匹配的院校</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  请尝试调整搜索关键词或筛选条件，或者清除所有筛选重新开始
                </p>
                <Button 
                  onClick={() => setFilters({ sortBy: 'ranking', sortOrder: 'asc' })}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  清除所有筛选
                </Button>
              </Card>
            )}

            {/* 分页控件 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-8 bg-white p-4 rounded-lg border border-gray-200">
                <Button
                  variant="outline"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  上一页
                </Button>
                
                <div className="flex gap-1">
                  {Array.from({ length: Math.min(7, totalPages) }, (_, i) => {
                    let page: number
                    if (totalPages <= 7) {
                      page = i + 1
                    } else if (currentPage <= 4) {
                      page = i + 1
                    } else if (currentPage >= totalPages - 3) {
                      page = totalPages - 6 + i
                    } else {
                      page = currentPage - 3 + i
                    }
                    
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className="w-10 h-10 p-0"
                      >
                        {page}
                      </Button>
                    )
                  })}
                  {totalPages > 7 && currentPage < totalPages - 3 && (
                    <>
                      <span className="px-2 py-1 text-gray-500">...</span>
                      <Button
                        variant={currentPage === totalPages ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                        className="w-10 h-10 p-0"
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>
                
                <Button
                  variant="outline"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  className="flex items-center gap-2"
                >
                  下一页
                  <ArrowLeft className="w-4 h-4 rotate-180" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
