import { useState } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import { provinces, universityTypes, universityCategories } from '../../data/universities'
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown,
  MapPin,
  GraduationCap,
  Building,
  TrendingUp,
  Sliders
} from 'lucide-react'

// 搜索筛选类型
interface SearchFilters {
  keyword?: string
  province?: string
  city?: string
  type?: string[]
  category?: string[]
  isPublic?: boolean
  minScore?: number
  maxScore?: number
  sortBy?: 'name' | 'ranking' | 'score' | 'founded'
  sortOrder?: 'asc' | 'desc'
}

interface AdvancedSearchFiltersProps {
  filters: SearchFilters
  onFiltersChange: (filters: SearchFilters) => void
  className?: string
}

export function AdvancedSearchFilters({ filters, onFiltersChange, className }: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showProvinces, setShowProvinces] = useState(false)

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const toggleArrayFilter = (key: 'type' | 'category', value: string) => {
    const currentArray = filters[key] || []
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    
    updateFilter(key, newArray.length > 0 ? newArray : undefined)
  }

  const clearFilters = () => {
    onFiltersChange({
      keyword: filters.keyword,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder
    })
  }

  const hasActiveFilters = !!(
    filters.province || 
    filters.city || 
    filters.type?.length || 
    filters.category?.length || 
    filters.isPublic !== undefined ||
    filters.minScore ||
    filters.maxScore
  )

  return (
    <Card className={cn("p-6", className)}>
      {/* 搜索框 */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          placeholder="搜索大学名称、地区..."
          value={filters.keyword || ''}
          onChange={(e) => updateFilter('keyword', e.target.value || undefined)}
          className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-base"
        />
      </div>

      {/* 快速筛选标签 */}
      <div className="mb-6">
        <div className="text-sm font-medium text-gray-700 mb-3">快速筛选</div>
        <div className="flex flex-wrap gap-2">
          <Button
            variant={filters.type?.includes('985') ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleArrayFilter('type', '985')}
            className="text-sm"
          >
            985高校
          </Button>
          <Button
            variant={filters.type?.includes('211') ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleArrayFilter('type', '211')}
            className="text-sm"
          >
            211高校
          </Button>
          <Button
            variant={filters.type?.includes('双一流') ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleArrayFilter('type', '双一流')}
            className="text-sm"
          >
            双一流
          </Button>
          <Button
            variant={filters.isPublic === true ? 'default' : 'outline'}
            size="sm"
            onClick={() => updateFilter('isPublic', filters.isPublic === true ? undefined : true)}
            className="text-sm"
          >
            公办院校
          </Button>
          <Button
            variant={filters.category?.includes('理工') ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleArrayFilter('category', '理工')}
            className="text-sm"
          >
            理工类
          </Button>
          <Button
            variant={filters.category?.includes('综合') ? 'default' : 'outline'}
            size="sm"
            onClick={() => toggleArrayFilter('category', '综合')}
            className="text-sm"
          >
            综合类
          </Button>
        </div>
      </div>

      {/* 展开/收起按钮 */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
        >
          <Sliders className="w-4 h-4" />
          高级筛选
          <ChevronDown className={cn("w-4 h-4 transition-transform", isExpanded && "rotate-180")} />
        </Button>
        
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-red-600 hover:text-red-700 flex items-center gap-1"
          >
            <X className="w-4 h-4" />
            清除筛选
          </Button>
        )}
      </div>

      {/* 高级筛选选项 */}
      {isExpanded && (
        <div className="space-y-6 border-t border-gray-200 pt-6">
          {/* 地区筛选 */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <MapPin className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">地区选择</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={!filters.province ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateFilter('province', undefined)}
                className="text-sm"
              >
                全部省份
              </Button>
              {provinces.map((province) => (
                <Button
                  key={province}
                  variant={filters.province === province ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateFilter('province', province)}
                  className="text-sm"
                >
                  {province}
                </Button>
              ))}
            </div>
          </div>

          {/* 院校类型 */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <GraduationCap className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">院校类型</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {universityTypes.map((type) => (
                <Button
                  key={type}
                  variant={filters.type?.includes(type) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => toggleArrayFilter('type', type)}
                  className="text-sm"
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {/* 院校分类 */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Building className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">院校分类</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {universityCategories.map((category) => (
                <Button
                  key={category}
                  variant={filters.category?.includes(category) ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => toggleArrayFilter('category', category)}
                  className="text-sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* 分数范围 */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <TrendingUp className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">录取分数范围</span>
            </div>
            <div className="flex gap-3 items-center">
              <input
                type="number"
                placeholder="最低分"
                value={filters.minScore || ''}
                onChange={(e) => updateFilter('minScore', e.target.value ? parseInt(e.target.value) : undefined)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                placeholder="最高分"
                value={filters.maxScore || ''}
                onChange={(e) => updateFilter('maxScore', e.target.value ? parseInt(e.target.value) : undefined)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>

          {/* 排序方式 */}
          <div>
            <div className="text-sm font-medium text-gray-700 mb-3">排序方式</div>
            <div className="flex gap-3">
              <select
                value={filters.sortBy || 'ranking'}
                onChange={(e) => updateFilter('sortBy', e.target.value as any)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="ranking">按排名</option>
                <option value="name">按名称</option>
                <option value="score">按分数</option>
                <option value="founded">按建校时间</option>
              </select>
              <select
                value={filters.sortOrder || 'asc'}
                onChange={(e) => updateFilter('sortOrder', e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="asc">升序</option>
                <option value="desc">降序</option>
              </select>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
