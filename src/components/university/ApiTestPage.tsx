import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { searchUniversities } from '../../services/universityApi'
import { getScoreSegment } from '../../services/scoreApi'
import { getMajorAdmission } from '../../services/majorApi'
import { ApiType, checkApiKeysConfiguration, getApiKeyInfo } from '../../services/apiKeys'
import type { ApiResponse } from '../../types/university'

export function ApiTestPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTest, setActiveTest] = useState<string>('')

  // 获取API配置状态
  const configStatus = checkApiKeysConfiguration()

  const testApi = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('🧪 开始测试API...')
      const response = await searchUniversities({
        keywords: '',
        pagesize: 5,
        pageindex: 1
      })
      setResult(response)
      console.log('✅ API测试成功:', response)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      setError(errorMessage)
      console.error('❌ API测试失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const testSpecificUniversity = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('🧪 测试搜索北京大学...')
      const response = await searchUniversities({
        keywords: '北京大学',
        pagesize: 5,
        pageindex: 1,
        keywordstrict: true
      })
      setResult(response)
      console.log('✅ 搜索测试成功:', response)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误'
      setError(errorMessage)
      console.error('❌ 搜索测试失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const testProxyHealth = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    setActiveTest('代理服务器健康检查')

    try {
      console.log('🧪 测试代理服务器健康状态...')
      const response = await fetch('http://localhost:3001/health')
      const data = await response.json()
      console.log('✅ 代理服务器健康检查成功:', data)
      setResult({
        type: 'proxy',
        data: data,
        message: '代理服务器运行正常'
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '代理服务器连接失败'
      setError(errorMessage)
      console.error('❌ 代理服务器健康检查失败:', err)
    } finally {
      setLoading(false)
      setActiveTest('')
    }
  }

  const testScoreApi = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    setActiveTest('一分一段数据')

    try {
      console.log('🧪 测试一分一段API...')
      const response = await getScoreSegment({
        pagesize: 5,
        pageindex: 1
      })
      setResult({
        type: 'score',
        data: response,
        message: '一分一段数据获取成功'
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '一分一段API测试失败'
      setError(errorMessage)
      console.error('❌ 一分一段API测试失败:', err)
    } finally {
      setLoading(false)
      setActiveTest('')
    }
  }

  const testMajorApi = async () => {
    setLoading(true)
    setError(null)
    setResult(null)
    setActiveTest('专业录取数据')

    try {
      console.log('🧪 测试专业录取API...')
      const response = await getMajorAdmission({
        pagesize: 5,
        pageindex: 1
      })
      setResult({
        type: 'major',
        data: response,
        message: '专业录取数据获取成功'
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '专业录取API测试失败'
      setError(errorMessage)
      console.error('❌ 专业录取API测试失败:', err)
    } finally {
      setLoading(false)
      setActiveTest('')
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">咕咕数据API测试中心</h1>

        {/* API密钥配置状态 */}
        <Card className="p-4 mb-6 bg-blue-50 border-blue-200">
          <h3 className="font-medium mb-3">API密钥配置状态</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <div>✅ 已配置: {configStatus.configured.length}/{configStatus.total}</div>
            <div>❌ 使用默认值: {configStatus.missing.length}/{configStatus.total}</div>
          </div>

          <details className="mt-3">
            <summary className="cursor-pointer text-sm font-medium">查看详细配置</summary>
            <div className="mt-2 space-y-1 text-xs">
              {Object.values(ApiType).map(apiType => {
                const info = getApiKeyInfo(apiType)
                return (
                  <div key={apiType} className="flex justify-between">
                    <span>{apiType}:</span>
                    <span className={info.source === 'env' ? 'text-green-600' : 'text-orange-600'}>
                      {info.preview} ({info.source})
                    </span>
                  </div>
                )
              })}
            </div>
          </details>
        </Card>

        <div className="space-y-4 mb-6">
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={testProxyHealth}
              disabled={loading}
              variant="secondary"
            >
              {loading && activeTest === '代理服务器健康检查' ? '测试中...' : '测试代理服务器'}
            </Button>

            <Button
              onClick={testApi}
              disabled={loading}
            >
              {loading && activeTest === '获取所有大学' ? '测试中...' : '测试高校基础信息'}
            </Button>

            <Button
              onClick={testSpecificUniversity}
              disabled={loading}
              variant="outline"
            >
              {loading && activeTest === '搜索北京大学' ? '测试中...' : '测试搜索北京大学'}
            </Button>

            <Button
              onClick={testScoreApi}
              disabled={loading}
              variant="outline"
            >
              {loading && activeTest === '一分一段数据' ? '测试中...' : '测试一分一段'}
            </Button>

            <Button
              onClick={testMajorApi}
              disabled={loading}
              variant="outline"
            >
              {loading && activeTest === '专业录取数据' ? '测试中...' : '测试专业录取'}
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            <p>💡 提示：</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>首先测试代理服务器是否运行正常</li>
              <li>然后测试各个API接口是否正常工作</li>
              <li>查看浏览器控制台获取详细日志</li>
              <li>每个接口使用不同的API密钥</li>
            </ul>
          </div>
        </div>

        {loading && (
          <div className="text-blue-600 mb-4">
            正在请求API...
          </div>
        )}

        {error && (
          <Card className="p-4 mb-4 border-red-200 bg-red-50">
            <h3 className="text-red-800 font-medium mb-2">错误信息</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </Card>
        )}

        {result && (
          <Card className="p-4 border-green-200 bg-green-50">
            <h3 className="text-green-800 font-medium mb-4">API响应结果</h3>
            
            <div className="space-y-2 text-sm">
              <div>
                <strong>状态码:</strong> {result.DataStatus?.StatusCode}
              </div>
              <div>
                <strong>状态描述:</strong> {result.DataStatus?.StatusDescription}
              </div>
              <div>
                <strong>响应时间:</strong> {result.DataStatus?.ResponseDateTime}
              </div>
              <div>
                <strong>总数据量:</strong> {result.DataStatus?.DataTotalCount}
              </div>
              <div>
                <strong>返回数据条数:</strong> {result.Data?.length || 0}
              </div>
            </div>

            {result.Data && result.Data.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">第一条数据示例:</h4>
                <div className="bg-white p-3 rounded border text-xs">
                  <div><strong>学校名称:</strong> {result.Data[0].CollegeName}</div>
                  <div><strong>所在地区:</strong> {result.Data[0].Province} {result.Data[0].City}</div>
                  <div><strong>院校类别:</strong> {result.Data[0].CollegeCategory}</div>
                  <div><strong>院校性质:</strong> {result.Data[0].CollegeType}</div>
                  <div><strong>是否985:</strong> {result.Data[0].Is985 ? '是' : '否'}</div>
                  <div><strong>是否211:</strong> {result.Data[0].Is211 ? '是' : '否'}</div>
                  <div><strong>是否双一流:</strong> {result.Data[0].IsDualClass ? '是' : '否'}</div>
                  <div><strong>全国排名:</strong> {result.Data[0].Ranking || '未排名'}</div>
                </div>
              </div>
            )}

            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium">查看完整响应数据</summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            </details>
          </Card>
        )}
      </Card>
    </div>
  )
}
