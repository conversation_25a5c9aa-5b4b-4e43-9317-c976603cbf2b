import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { UniversityCard } from '../UniversityCard'
import { University } from '@/types/university'

const mockUniversity: University = {
  id: '1',
  name: '清华大学',
  englishName: 'Tsinghua University',
  type: '985',
  level: '本科',
  location: {
    province: '北京',
    city: '北京'
  },
  category: '理工',
  isPublic: true,
  foundedYear: 1911,
  description: '清华大学是中国著名高等学府',
  ranking: {
    national: 1,
    category: 1
  },
  admissionInfo: {
    minScore: 680,
    avgScore: 690,
    maxScore: 700,
    year: 2023,
    province: '全国'
  },
  features: ['985工程', '211工程', '双一流'],
  majors: ['计算机科学与技术', '电子信息工程']
}

describe('UniversityCard', () => {
  it('renders university information correctly', () => {
    render(<UniversityCard university={mockUniversity} />)
    
    expect(screen.getByText('清华大学')).toBeInTheDocument()
    expect(screen.getByText('Tsinghua University')).toBeInTheDocument()
    expect(screen.getByText('985')).toBeInTheDocument()
    expect(screen.getByText('理工')).toBeInTheDocument()
    expect(screen.getByText('公办')).toBeInTheDocument()
    expect(screen.getByText('北京 · 北京')).toBeInTheDocument()
    expect(screen.getByText('建校于 1911 年')).toBeInTheDocument()
    expect(screen.getByText('690')).toBeInTheDocument() // 平均分
    expect(screen.getByText('全国第1')).toBeInTheDocument()
  })

  it('calls onClick when card is clicked', () => {
    const handleClick = vi.fn()
    render(<UniversityCard university={mockUniversity} onClick={handleClick} />)
    
    const card = screen.getByRole('button', { name: /查看详情/i }).closest('[data-slot="card"]')
    if (card) {
      fireEvent.click(card)
      expect(handleClick).toHaveBeenCalledTimes(1)
    }
  })

  it('displays features and majors correctly', () => {
    render(<UniversityCard university={mockUniversity} />)
    
    expect(screen.getByText('985工程')).toBeInTheDocument()
    expect(screen.getByText('211工程')).toBeInTheDocument()
    expect(screen.getByText('双一流')).toBeInTheDocument()
    expect(screen.getByText('计算机科学与技术')).toBeInTheDocument()
    expect(screen.getByText('电子信息工程')).toBeInTheDocument()
  })

  it('handles missing optional fields gracefully', () => {
    const minimalUniversity: University = {
      id: '2',
      name: '测试大学',
      type: '普通本科',
      level: '本科',
      location: {
        province: '测试省',
        city: '测试市'
      },
      category: '综合',
      isPublic: true
    }

    render(<UniversityCard university={minimalUniversity} />)
    
    expect(screen.getByText('测试大学')).toBeInTheDocument()
    expect(screen.getByText('普通本科')).toBeInTheDocument()
    expect(screen.getByText('综合')).toBeInTheDocument()
    expect(screen.getByText('测试省 · 测试市')).toBeInTheDocument()
  })
})
