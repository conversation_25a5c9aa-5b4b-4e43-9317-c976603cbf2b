import { University } from '../../types/university.ts'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import { 
  ArrowLeft,
  MapPin, 
  Calendar, 
  GraduationCap, 
  Star, 
  TrendingUp,
  ExternalLink,
  Phone,
  Globe,
  Users,
  BookOpen,
  Award,
  Building
} from 'lucide-react'

interface UniversityDetailProps {
  university: University
  onBack?: () => void
  className?: string
}

export function UniversityDetail({ university, onBack, className }: UniversityDetailProps) {
  const getTypeColor = (type: University['type']) => {
    switch (type) {
      case '985':
        return 'bg-red-100 text-red-800 border-red-200'
      case '211':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case '双一流':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case '普通本科':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case '专科':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryColor = (category: University['category']) => {
    const colors = {
      '综合': 'bg-green-100 text-green-800',
      '理工': 'bg-blue-100 text-blue-800',
      '师范': 'bg-yellow-100 text-yellow-800',
      '医药': 'bg-red-100 text-red-800',
      '财经': 'bg-purple-100 text-purple-800',
      '艺术': 'bg-pink-100 text-pink-800',
      '农林': 'bg-emerald-100 text-emerald-800',
      '政法': 'bg-indigo-100 text-indigo-800',
      '体育': 'bg-orange-100 text-orange-800',
      '民族': 'bg-cyan-100 text-cyan-800',
      '军事': 'bg-slate-100 text-slate-800',
      '其他': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其他']
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <Building className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">{university.name}</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 大学头部信息 */}
        <Card className="p-8 mb-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold text-gray-900">{university.name}</h1>
                {university.ranking?.national && (
                  <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                    <Star className="w-4 h-4" />
                    全国第{university.ranking.national}
                  </div>
                )}
              </div>
              
              {university.englishName && (
                <p className="text-lg text-gray-600 mb-4">{university.englishName}</p>
              )}
              
              {/* 标签 */}
              <div className="flex flex-wrap gap-3 mb-6">
                <span className={cn("px-3 py-1 rounded-full text-sm font-medium border", getTypeColor(university.type))}>
                  {university.type}
                </span>
                <span className={cn("px-3 py-1 rounded-full text-sm font-medium", getCategoryColor(university.category))}>
                  {university.category}
                </span>
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700">
                  {university.isPublic ? '公办' : '民办'}
                </span>
                <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-700">
                  {university.level}院校
                </span>
              </div>
            </div>
            
            {/* 分数信息 */}
            {university.admissionInfo && (
              <div className="text-center bg-blue-50 p-6 rounded-lg">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {university.admissionInfo.avgScore}
                </div>
                <div className="text-sm text-gray-600 mb-2">平均录取分</div>
                {university.admissionInfo.minScore && university.admissionInfo.maxScore && (
                  <div className="text-sm text-gray-500">
                    {university.admissionInfo.minScore}-{university.admissionInfo.maxScore}分
                  </div>
                )}
                <div className="text-xs text-gray-500 mt-2">
                  {university.admissionInfo.year}年数据
                </div>
              </div>
            )}
          </div>

          {/* 基本信息网格 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
            <div className="flex items-center gap-3">
              <MapPin className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">所在地区</div>
                <div className="font-medium">{university.location.province} · {university.location.city}</div>
              </div>
            </div>
            
            {university.foundedYear && (
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-500" />
                <div>
                  <div className="text-sm text-gray-500">建校时间</div>
                  <div className="font-medium">{university.foundedYear}年</div>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-3">
              <GraduationCap className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">办学层次</div>
                <div className="font-medium">{university.level}</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Users className="w-5 h-5 text-gray-500" />
              <div>
                <div className="text-sm text-gray-500">办学性质</div>
                <div className="font-medium">{university.isPublic ? '公办' : '民办'}</div>
              </div>
            </div>
          </div>

          {/* 联系方式 */}
          <div className="flex items-center gap-6 pt-6 border-t border-gray-200">
            {university.website && (
              <Button variant="outline" className="flex items-center gap-2">
                <Globe className="w-4 h-4" />
                官方网站
              </Button>
            )}
            {university.phone && (
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                联系电话
              </Button>
            )}
          </div>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 学校简介 */}
            {university.description && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  学校简介
                </h2>
                <p className="text-gray-700 leading-relaxed">{university.description}</p>
              </Card>
            )}

            {/* 热门专业 */}
            {university.majors && university.majors.length > 0 && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  热门专业
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {university.majors.map((major, index) => (
                    <div 
                      key={index}
                      className="p-3 bg-green-50 text-green-800 rounded-lg border border-green-200"
                    >
                      {major}
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>

          {/* 右侧信息 */}
          <div className="space-y-6">
            {/* 学校特色 */}
            {university.features && university.features.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">学校特色</h3>
                <div className="space-y-2">
                  {university.features.map((feature, index) => (
                    <div 
                      key={index}
                      className="p-2 bg-blue-50 text-blue-800 rounded text-sm"
                    >
                      {feature}
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* 排名信息 */}
            {university.ranking && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  排名信息
                </h3>
                <div className="space-y-3">
                  {university.ranking.national && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">全国排名</span>
                      <span className="font-bold text-blue-600">第{university.ranking.national}名</span>
                    </div>
                  )}
                  {university.ranking.category && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">同类排名</span>
                      <span className="font-bold text-green-600">第{university.ranking.category}名</span>
                    </div>
                  )}
                </div>
              </Card>
            )}

            {/* 联系信息 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">联系信息</h3>
              <div className="space-y-3 text-sm">
                {university.address && (
                  <div>
                    <span className="text-gray-600">学校地址：</span>
                    <span className="text-gray-900">{university.address}</span>
                  </div>
                )}
                {university.phone && (
                  <div>
                    <span className="text-gray-600">联系电话：</span>
                    <span className="text-gray-900">{university.phone}</span>
                  </div>
                )}
                {university.website && (
                  <div>
                    <span className="text-gray-600">官方网站：</span>
                    <a 
                      href={university.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-700 flex items-center gap-1"
                    >
                      访问官网
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
