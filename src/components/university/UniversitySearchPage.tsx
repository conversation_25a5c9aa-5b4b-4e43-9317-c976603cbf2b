import { useState, useEffect, useMemo } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { UniversityCard } from './UniversityCard'
import { SearchFilters } from './SearchFilters'
import type { University, SearchFilters as SearchFiltersType, ApiResponse } from '../../types/university'
import { searchUniversities } from '../../services/universityApi'
import { cn } from '../../lib/utils'
import {
  ArrowLeft,
  Grid3X3,
  List,
  School,
  TrendingUp,
  Users,
  MapPin
} from 'lucide-react'

interface UniversitySearchPageProps {
  onBack?: () => void
}

export function UniversitySearchPage({ onBack }: UniversitySearchPageProps) {
  const [filters, setFilters] = useState<SearchFiltersType>({
    pagesize: 12,
    pageindex: 1
  })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [universities, setUniversities] = useState<University[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const pageSize = filters.pagesize || 12
  const currentPage = filters.pageindex || 1

  // 加载大学数据
  const loadUniversities = async (searchFilters: SearchFiltersType) => {
    console.log('🚀 开始加载大学数据，筛选条件:', searchFilters)
    setLoading(true)
    setError(null)

    try {
      const response: ApiResponse = await searchUniversities(searchFilters)
      console.log('📊 收到API响应:', response)
      setUniversities(response.Data || [])
      setTotalCount(response.DataStatus.DataTotalCount || 0)
      console.log('✅ 数据加载成功，大学数量:', response.Data?.length || 0)
    } catch (err) {
      console.error('❌ 数据加载失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
      setUniversities([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  // 当筛选条件改变时重新加载数据
  useEffect(() => {
    loadUniversities(filters)
  }, [filters])

  // 分页逻辑
  const totalPages = Math.ceil(totalCount / pageSize)

  // 处理筛选条件变化
  const handleFiltersChange = (newFilters: SearchFiltersType) => {
    // 如果是搜索条件变化，重置到第一页
    const shouldResetPage =
      newFilters.keywords !== filters.keywords ||
      newFilters.collegecategory !== filters.collegecategory ||
      newFilters.collegetype !== filters.collegetype ||
      newFilters.is985 !== filters.is985 ||
      newFilters.is211 !== filters.is211 ||
      newFilters.isdualclass !== filters.isdualclass ||
      newFilters.edulevel !== filters.edulevel ||
      newFilters.collegeproperty !== filters.collegeproperty ||
      newFilters.keywordstrict !== filters.keywordstrict

    setFilters({
      ...newFilters,
      pageindex: shouldResetPage ? 1 : newFilters.pageindex
    })
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters({
      ...filters,
      pageindex: page
    })
  }

  // 统计信息
  const stats = useMemo(() => {
    const total = totalCount
    const types = universities.reduce((acc, uni) => {
      if (uni.Is985) acc['985'] = (acc['985'] || 0) + 1
      if (uni.Is211) acc['211'] = (acc['211'] || 0) + 1
      if (uni.IsDualClass) acc['双一流'] = (acc['双一流'] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const provinces = new Set(universities.map(uni => uni.Province)).size

    return { total, types, provinces }
  }, [universities, totalCount])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <School className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">查大学</h1>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="university-search-layout">
          {/* 左侧筛选器 */}
          <div className="w-full lg:w-80">
            <SearchFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              className="sticky top-24"
            />
          </div>

          {/* 右侧内容区 */}
          <div className="flex-1 min-w-0">
            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-gray-600">找到院校</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.types['985'] || 0}</div>
                <div className="text-sm text-gray-600">985院校</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.provinces}</div>
                <div className="text-sm text-gray-600">覆盖省份</div>
              </Card>
              <Card className="p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.types['211'] || 0}</div>
                <div className="text-sm text-gray-600">211院校</div>
              </Card>
            </div>

            {/* 结果头部 */}
            <div className="flex items-center justify-between mb-4">
              <div className="text-sm text-gray-600">
                共找到 <span className="font-medium text-gray-900">{totalCount}</span> 所院校
                {filters.keywords && (
                  <span>，搜索关键词：<span className="font-medium text-blue-600">"{filters.keywords}"</span></span>
                )}
                {loading && <span className="ml-2 text-blue-600">加载中...</span>}
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">显示：</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
                  className="flex items-center gap-1"
                >
                  {viewMode === 'list' ? <Grid3X3 className="w-4 h-4" /> : <List className="w-4 h-4" />}
                  {viewMode === 'list' ? '网格' : '列表'}
                </Button>
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <Card className="p-6 mb-4 border-red-200 bg-red-50">
                <div className="text-red-800 text-center">
                  <p className="font-medium">加载失败</p>
                  <p className="text-sm mt-1">{error}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => loadUniversities(filters)}
                  >
                    重试
                  </Button>
                </div>
              </Card>
            )}

            {/* 大学列表 */}
            {!error && universities.length > 0 ? (
              <div className={cn(
                "space-y-4",
                viewMode === 'grid' && "grid grid-cols-1 lg:grid-cols-2 gap-4 space-y-0"
              )}>
                {universities.map((university) => (
                  <UniversityCard
                    key={university.DataId}
                    university={university}
                    onClick={() => console.log('查看大学详情:', university.CollegeName)}
                  />
                ))}
              </div>
            ) : !error && !loading && (
              <Card className="p-12 text-center">
                <School className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的院校</h3>
                <p className="text-gray-600 mb-4">请尝试调整筛选条件或搜索关键词</p>
                <Button onClick={() => handleFiltersChange({ pagesize: 12, pageindex: 1 })}>
                  清除所有筛选
                </Button>
              </Card>
            )}

            {/* 分页 */}
            {!error && totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-8">
                <Button
                  variant="outline"
                  disabled={currentPage === 1 || loading}
                  onClick={() => handlePageChange(currentPage - 1)}
                >
                  上一页
                </Button>

                <div className="flex gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        disabled={loading}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="px-2 py-1 text-gray-500">...</span>
                      <Button
                        variant={currentPage === totalPages ? 'default' : 'outline'}
                        size="sm"
                        disabled={loading}
                        onClick={() => handlePageChange(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>

                <Button
                  variant="outline"
                  disabled={currentPage === totalPages || loading}
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  下一页
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
