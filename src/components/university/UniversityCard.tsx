import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import type { University } from '../../types/university'
import {
  MapPin,
  Calendar,
  GraduationCap,
  Star,
  TrendingUp,
  ExternalLink,
  Phone,
  Globe,
  Award
} from 'lucide-react'

interface UniversityCardProps {
  university: University
  onClick?: () => void
  onViewDetail?: () => void
  className?: string
}

export function UniversityCard({ university, onClick, onViewDetail, className }: UniversityCardProps) {
  // 调试：输出大学数据结构
  console.log('🏫 大学数据:', university.CollegeName, {
    CollegeTags: university.CollegeTags,
    MajorList: university.MajorList,
    tagsType: typeof university.CollegeTags,
    majorListType: typeof university.MajorList,
    isTagsArray: Array.isArray(university.CollegeTags),
    isMajorListArray: Array.isArray(university.MajorList)
  })

  // 安全的数组检查函数
  const safeArray = (arr: any): any[] => {
    return Array.isArray(arr) ? arr : []
  }

  // 获取院校类型标签
  const getTypeLabels = () => {
    const labels = []
    if (university.Is985) labels.push({ text: '985', color: 'bg-red-100 text-red-800 border-red-200' })
    if (university.Is211) labels.push({ text: '211', color: 'bg-orange-100 text-orange-800 border-orange-200' })
    if (university.IsDualClass) labels.push({ text: '双一流', color: 'bg-purple-100 text-purple-800 border-purple-200' })
    return labels
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      '综合类': 'bg-green-100 text-green-800',
      '理工类': 'bg-blue-100 text-blue-800',
      '师范类': 'bg-yellow-100 text-yellow-800',
      '医药类': 'bg-red-100 text-red-800',
      '财经类': 'bg-purple-100 text-purple-800',
      '艺术类': 'bg-pink-100 text-pink-800',
      '农林类': 'bg-emerald-100 text-emerald-800',
      '政法类': 'bg-indigo-100 text-indigo-800',
      '体育类': 'bg-orange-100 text-orange-800',
      '民族类': 'bg-cyan-100 text-cyan-800',
      '军事类': 'bg-slate-100 text-slate-800',
      '语言类': 'bg-teal-100 text-teal-800',
      '其它': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其它']
  }

  return (
    <Card
      className={cn(
        "group university-card hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600",
        className
      )}
      onClick={onClick}
    >
      <div className="p-6">
        {/* 头部信息 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {university.CoverImage && (
                <img
                  src={university.CoverImage}
                  alt={`${university.CollegeName}校徽`}
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                {university.CollegeName}
              </h3>
              {university.Ranking && university.Ranking > 0 && (
                <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                  <Star className="w-3 h-3" />
                  全国第{university.Ranking}
                </div>
              )}
            </div>
            {university.ShortName && university.ShortName !== university.CollegeName && (
              <p className="text-sm text-gray-600 mb-2">简称：{university.ShortName}</p>
            )}

            {/* 标签 */}
            <div className="flex flex-wrap gap-2 mb-3">
              {getTypeLabels().map((label, index) => (
                <span key={index} className={cn("px-2 py-1 rounded-full text-xs font-medium border", label.color)}>
                  {label.text}
                </span>
              ))}
              <span className={cn("px-2 py-1 rounded-full text-xs font-medium", getCategoryColor(university.CollegeCategory))}>
                {university.CollegeCategory}
              </span>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                {university.CollegeProperty}
              </span>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                {university.EduLevel}
              </span>
            </div>
          </div>

          {/* 排名信息 */}
          {university.RankingInCategory && (
            <div className="text-right">
              <div className="text-lg font-bold text-blue-600">
                {university.RankingInCategory}
              </div>
              <div className="text-xs text-gray-500">类别排名</div>
            </div>
          )}
        </div>

        {/* 基本信息 */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <MapPin className="w-4 h-4" />
            <span>{university.Province} · {university.City}</span>
            {university.District && <span>· {university.District}</span>}
          </div>

          {university.CollegeCode && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Award className="w-4 h-4" />
              <span>院校代码：{university.CollegeCode}</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <GraduationCap className="w-4 h-4" />
            <span>{university.CollegeType}</span>
          </div>
        </div>

        {/* 描述 */}
        {university.Intro && (
          <p className="text-sm text-gray-700 mb-4 overflow-hidden" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {university.Intro}
          </p>
        )}

        {/* 特色标签 */}
        {(() => {
          const tags = safeArray(university.CollegeTags)
          return tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-4">
              {tags.slice(0, 4).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs"
                >
                  {tag}
                </span>
              ))}
              {tags.length > 4 && (
                <span className="px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs">
                  +{tags.length - 4}
                </span>
              )}
            </div>
          )
        })()}

        {/* 开设专业 */}
        {(() => {
          const majorList = safeArray(university.MajorList)
          return majorList.length > 0 && (
            <div className="mb-4">
              <div className="text-sm font-medium text-gray-700 mb-2">开设专业</div>
              <div className="space-y-1">
                {majorList.slice(0, 2).map((majorGroup, index) => (
                  <div key={index} className="text-xs">
                    <span className="font-medium text-gray-600">{majorGroup?.MajorTitle || '未知专业类别'}：</span>
                    <span className="text-gray-500">
                      {(() => {
                        const majors = safeArray(majorGroup?.Majors)
                        return majors.length > 0 ? (
                          <>
                            {majors.slice(0, 3).join('、')}
                            {majors.length > 3 && `等${majors.length}个专业`}
                          </>
                        ) : (
                          '暂无专业信息'
                        )
                      })()}
                    </span>
                  </div>
                ))}
                {majorList.length > 2 && (
                  <div className="text-xs text-gray-500">
                    还有{majorList.length - 2}个专业大类...
                  </div>
                )}
              </div>
            </div>
          )
        })()}

        {/* 底部操作 */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4">
            {university.WebSite && (
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:text-blue-700 p-0 h-auto"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(university.WebSite, '_blank')
                }}
              >
                <Globe className="w-4 h-4 mr-1" />
                官网
              </Button>
            )}
            {university.CallNumber && (
              <Button
                variant="ghost"
                size="sm"
                className="text-green-600 hover:text-green-700 p-0 h-auto"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(`tel:${university.CallNumber}`, '_self')
                }}
              >
                <Phone className="w-4 h-4 mr-1" />
                电话
              </Button>
            )}
            {university.Expenses && (
              <span className="text-xs text-gray-500">
                学费：{university.Expenses}
              </span>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            className="group-hover:bg-blue-50 group-hover:border-blue-200"
            onClick={(e) => {
              e.stopPropagation()
              onViewDetail?.()
            }}
          >
            查看详情
            <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </div>
      </div>
    </Card>
  )
}
