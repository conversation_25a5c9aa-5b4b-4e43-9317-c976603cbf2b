import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import type { University } from '../../types/university'
import {
  MapPin,
  Calendar,
  GraduationCap,
  Star,
  TrendingUp,
  ExternalLink,
  Phone,
  Globe,
  Award
} from 'lucide-react'

interface UniversityCardProps {
  university: University
  onClick?: () => void
  onViewDetail?: () => void
  className?: string
}

export function UniversityCard({ university, onClick, onViewDetail, className }: UniversityCardProps) {
  // 获取院校类型标签
  const getTypeLabels = () => {
    const labels = []
    if (university.Is985) labels.push({ text: '985', color: 'bg-red-100 text-red-800' })
    if (university.Is211) labels.push({ text: '211', color: 'bg-orange-100 text-orange-800' })
    if (university.IsDualClass) labels.push({ text: '双一流', color: 'bg-purple-100 text-purple-800' })
    return labels
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      '综合类': 'bg-green-100 text-green-800',
      '理工类': 'bg-blue-100 text-blue-800',
      '师范类': 'bg-yellow-100 text-yellow-800',
      '医药类': 'bg-red-100 text-red-800',
      '财经类': 'bg-purple-100 text-purple-800',
      '艺术类': 'bg-pink-100 text-pink-800',
      '农林类': 'bg-emerald-100 text-emerald-800',
      '政法类': 'bg-indigo-100 text-indigo-800',
      '体育类': 'bg-orange-100 text-orange-800',
      '民族类': 'bg-cyan-100 text-cyan-800',
      '军事类': 'bg-slate-100 text-slate-800',
      '语言类': 'bg-teal-100 text-teal-800',
      '其它': 'bg-gray-100 text-gray-800'
    }
    return colors[category] || colors['其它']
  }

  return (
    <Card
      className={cn(
        "group university-card hover:shadow-md transition-all duration-200 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600",
        className
      )}
      onClick={onClick}
    >
      <div className="p-4">
        {/* 头部信息 - 简化版 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {university.CoverImage && (
                <img
                  src={university.CoverImage}
                  alt={`${university.CollegeName}校徽`}
                  className="w-6 h-6 rounded-full object-cover flex-shrink-0"
                />
              )}
              <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors truncate flex-1">
                {university.CollegeName}
              </h3>
              {university.Ranking && university.Ranking > 0 && (
                <div className="flex items-center gap-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium flex-shrink-0">
                  <Star className="w-3 h-3" />
                  {university.Ranking}
                </div>
              )}
            </div>

            {/* 简化的标签 - 只显示最重要的 */}
            <div className="flex flex-wrap gap-1 mb-2">
              {getTypeLabels().slice(0, 2).map((label, index) => (
                <span key={index} className={cn("px-2 py-1 rounded-full text-xs font-medium", label.color)}>
                  {label.text}
                </span>
              ))}
              <span className={cn("px-2 py-1 rounded-full text-xs font-medium", getCategoryColor(university.CollegeCategory))}>
                {university.CollegeCategory}
              </span>
            </div>
          </div>
        </div>

        {/* 基本信息 - 简化版 */}
        <div className="space-y-1 mb-3">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <MapPin className="w-3 h-3" />
            <span>{university.Province} · {university.City}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <GraduationCap className="w-3 h-3" />
            <span>{university.CollegeProperty}</span>
          </div>
        </div>

        {/* 底部操作 - 简化版 */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center gap-3 text-xs text-gray-500 flex-1 min-w-0">
            {university.Expenses && (
              <span className="truncate">学费：{university.Expenses}</span>
            )}
            {university.CollegeCode && (
              <span className="truncate">代码：{university.CollegeCode}</span>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            className="group-hover:bg-blue-50 group-hover:border-blue-200 text-xs px-3 py-1 flex-shrink-0 ml-2"
            onClick={(e) => {
              e.stopPropagation()
              onViewDetail?.()
            }}
          >
            详情
            <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </div>
      </div>
    </Card>
  )
}
