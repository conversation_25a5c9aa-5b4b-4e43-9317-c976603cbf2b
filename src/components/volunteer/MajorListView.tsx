import { useState } from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import type { VolunteerMajor, AdmissionLevel } from '../../types/volunteer'
import {
  Search,
  Plus,
  Check,
  MapPin,
  TrendingUp,
  Users,
  BookOpen,
  Star,
  AlertTriangle
} from 'lucide-react'

interface MajorListViewProps {
  majors: VolunteerMajor[]
  selectedMajors: string[]
  userScore?: number
  userRanking?: number
  admissionLevel: AdmissionLevel
  searchKeyword: string
  onMajorSelect: (major: VolunteerMajor) => void
  onSearchChange: (keyword: string) => void
  className?: string
}

export function MajorListView({
  majors,
  selectedMajors,
  userScore,
  userRanking,
  admissionLevel,
  searchKeyword,
  onMajorSelect,
  onSearchChange,
  className
}: MajorListViewProps) {
  // 分析录取概率
  const analyzeMajorAdmission = (major: VolunteerMajor): 'rush' | 'stable' | 'safe' | null => {
    if (!userScore || !userRanking || !major.admissionScore?.length) return null

    const latestScore = major.admissionScore[0]
    const scoreDiff = userScore - latestScore.minScore
    const rankingDiff = userRanking - latestScore.ranking

    if (scoreDiff >= 20 && rankingDiff <= -1000) {
      return 'safe'
    } else if (scoreDiff >= 10 && rankingDiff <= -500) {
      return 'stable'
    } else if (scoreDiff >= -10 && rankingDiff <= 500) {
      return 'rush'
    }
    return null
  }

  // 根据等级筛选专业
  const filteredMajors = majors.filter(major => {
    if (admissionLevel === 'all') return true
    
    const suggestion = analyzeMajorAdmission(major)
    return suggestion === admissionLevel
  })

  // 获取建议标签样式
  const getSuggestionStyle = (suggestion: string | null) => {
    switch (suggestion) {
      case 'safe':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'stable':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'rush':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSuggestionText = (suggestion: string | null) => {
    switch (suggestion) {
      case 'safe':
        return '保'
      case 'stable':
        return '稳'
      case 'rush':
        return '冲'
      default:
        return '未知'
    }
  }

  return (
    <div className={className}>
      {/* 搜索栏 */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="搜索专业名称..."
            value={searchKeyword}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* 专业列表 */}
      <div className="space-y-2">
        {filteredMajors.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <BookOpen className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>没有找到符合条件的专业</p>
          </div>
        ) : (
          filteredMajors.map((major) => {
            const isSelected = selectedMajors.includes(major.id)
            const suggestion = analyzeMajorAdmission(major)
            const latestScore = major.admissionScore?.[0]

            return (
              <div
                key={major.id}
                className={`border rounded-lg p-4 hover:shadow-md transition-all duration-200 ${
                  isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'bg-white'
                }`}
              >
                <div className="flex items-center justify-between">
                  {/* 左侧专业信息 */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {major.name}
                      </h3>
                      <span className="text-sm text-gray-500">({major.code})</span>
                      {suggestion && (
                        <Badge className={getSuggestionStyle(suggestion)}>
                          {getSuggestionText(suggestion)}
                        </Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{major.university.name}</span>
                      </div>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {major.category}
                      </Badge>
                      {major.subCategory && (
                        <Badge variant="outline" className="bg-gray-50 text-gray-600">
                          {major.subCategory}
                        </Badge>
                      )}
                    </div>

                    {/* 分数信息 */}
                    {latestScore && (
                      <div className="flex items-center gap-6 text-sm">
                        <div>
                          <span className="text-gray-600">最低分: </span>
                          <span className="font-semibold text-gray-900">{latestScore.minScore}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">平均分: </span>
                          <span className="font-semibold text-gray-900">{latestScore.avgScore}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">位次: </span>
                          <span className="font-semibold text-gray-900">{latestScore.ranking}</span>
                        </div>
                      </div>
                    )}

                    {/* 专业特色 */}
                    {major.features && major.features.length > 0 && (
                      <div className="mt-2">
                        <div className="flex flex-wrap gap-1">
                          {major.features.slice(0, 3).map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                              {feature}
                            </Badge>
                          ))}
                          {major.features.length > 3 && (
                            <Badge variant="outline" className="text-xs bg-gray-50 text-gray-600">
                              +{major.features.length - 3}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 右侧操作按钮 */}
                  <div className="ml-4">
                    <Button
                      onClick={() => onMajorSelect(major)}
                      disabled={isSelected}
                      className={`${
                        isSelected 
                          ? 'bg-green-600 hover:bg-green-700' 
                          : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {isSelected ? (
                        <>
                          <Check className="w-4 h-4 mr-1" />
                          已选择
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-1" />
                          选择
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* 分析提醒 */}
                {suggestion === 'rush' && (
                  <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-center gap-2 text-sm text-orange-800">
                      <AlertTriangle className="w-4 h-4" />
                      <span>该专业录取难度较大，建议作为冲刺志愿</span>
                    </div>
                  </div>
                )}
              </div>
            )
          })
        )}
      </div>

      {/* 统计信息 */}
      <div className="mt-4 text-sm text-gray-500 text-center">
        共找到 {filteredMajors.length} 个专业
      </div>
    </div>
  )
}
