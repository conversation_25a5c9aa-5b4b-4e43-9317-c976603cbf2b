import { useState } from 'react'
import { Card } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import type { VolunteerUniversity, ScoreAnalysis } from '../../types/volunteer'
import {
  MapPin,
  Star,
  TrendingUp,
  Users,
  Phone,
  Globe,
  Plus,
  Check,
  AlertTriangle,
  Info
} from 'lucide-react'

interface UniversityCardProps {
  university: VolunteerUniversity
  userScore?: number
  userRanking?: number
  isSelected?: boolean
  onSelect?: (university: VolunteerUniversity) => void
  onViewDetail?: (university: VolunteerUniversity) => void
  className?: string
}

export function UniversityCard({
  university,
  userScore,
  userRanking,
  isSelected = false,
  onSelect,
  onViewDetail,
  className
}: UniversityCardProps) {
  const [showDetails, setShowDetails] = useState(false)

  // 获取院校类型颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case '985':
        return 'bg-red-100 text-red-800 border-red-200'
      case '211':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case '双一流':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case '普通本科':
        return 'bg-green-100 text-green-800 border-green-200'
      case '民办':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取类别颜色
  const getCategoryColor = (category: string) => {
    switch (category) {
      case '综合':
        return 'bg-blue-50 text-blue-700'
      case '理工':
        return 'bg-green-50 text-green-700'
      case '师范':
        return 'bg-yellow-50 text-yellow-700'
      case '医药':
        return 'bg-red-50 text-red-700'
      case '财经':
        return 'bg-orange-50 text-orange-700'
      default:
        return 'bg-gray-50 text-gray-700'
    }
  }

  // 分析录取概率
  const analyzeAdmissionProbability = (): ScoreAnalysis | null => {
    if (!userScore || !userRanking || !university.scores.length) return null

    const latestScore = university.scores[0]
    const scoreDiff = userScore - latestScore.minScore
    const rankingDiff = userRanking - latestScore.ranking

    let probability: 'high' | 'medium' | 'low'
    let suggestion: 'rush' | 'stable' | 'safe'
    let probabilityScore: number

    if (scoreDiff >= 20 && rankingDiff <= -1000) {
      probability = 'high'
      suggestion = 'safe'
      probabilityScore = 85
    } else if (scoreDiff >= 10 && rankingDiff <= -500) {
      probability = 'high'
      suggestion = 'stable'
      probabilityScore = 75
    } else if (scoreDiff >= 0 && rankingDiff <= 0) {
      probability = 'medium'
      suggestion = 'stable'
      probabilityScore = 60
    } else if (scoreDiff >= -10 && rankingDiff <= 500) {
      probability = 'medium'
      suggestion = 'rush'
      probabilityScore = 45
    } else {
      probability = 'low'
      suggestion = 'rush'
      probabilityScore = 25
    }

    return {
      probability,
      probabilityScore,
      suggestion,
      historicalData: university.scores,
      competitiveness: scoreDiff > 30 ? 'low' : scoreDiff > 10 ? 'medium' : 'high'
    }
  }

  const analysis = analyzeAdmissionProbability()

  // 获取建议颜色
  const getSuggestionColor = (suggestion: string) => {
    switch (suggestion) {
      case 'safe':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'stable':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'rush':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getSuggestionText = (suggestion: string) => {
    switch (suggestion) {
      case 'safe':
        return '保底'
      case 'stable':
        return '稳妥'
      case 'rush':
        return '冲刺'
      default:
        return '未知'
    }
  }

  const latestScore = university.scores[0]

  return (
    <Card className={`${className} ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''} hover:shadow-md transition-all duration-200`}>
      <div className="p-4">
        {/* 院校基本信息 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {university.ranking?.national && (
                <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                  #{university.ranking.national}
                </Badge>
              )}
              <h3 className="font-semibold text-gray-900 text-lg">
                {university.name}
              </h3>
              {university.shortName && (
                <span className="text-sm text-gray-500">({university.shortName})</span>
              )}
            </div>
            
            <div className="flex items-center gap-2 mb-2">
              <Badge className={getTypeColor(university.type)}>
                {university.type}
              </Badge>
              <Badge variant="outline" className={getCategoryColor(university.category)}>
                {university.category}
              </Badge>
              {!university.isPublic && (
                <Badge variant="outline" className="bg-purple-50 text-purple-700">
                  民办
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                <span>{university.location.province} · {university.location.city}</span>
              </div>
              {university.admissionPlan && (
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>招生 {university.admissionPlan.total}人</span>
                </div>
              )}
            </div>
          </div>

          {/* 录取概率分析 */}
          {analysis && (
            <div className="text-right">
              <Badge className={getSuggestionColor(analysis.suggestion)}>
                {getSuggestionText(analysis.suggestion)}
              </Badge>
              <div className="text-sm text-gray-600 mt-1">
                录取概率: {analysis.probabilityScore}%
              </div>
            </div>
          )}
        </div>

        {/* 分数信息 */}
        {latestScore && (
          <div className="bg-gray-50 rounded-lg p-3 mb-3">
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">最低分</span>
                <div className="font-semibold text-gray-900">{latestScore.minScore}</div>
              </div>
              <div>
                <span className="text-gray-600">平均分</span>
                <div className="font-semibold text-gray-900">{latestScore.avgScore}</div>
              </div>
              <div>
                <span className="text-gray-600">位次</span>
                <div className="font-semibold text-gray-900">{latestScore.ranking}</div>
              </div>
            </div>
            <div className="text-xs text-gray-500 mt-2">
              {latestScore.year}年 · {latestScore.province} · {latestScore.subject}
            </div>
          </div>
        )}

        {/* 院校特色 */}
        {university.features.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {university.features.slice(0, 3).map((feature, index) => (
                <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                  {feature}
                </Badge>
              ))}
              {university.features.length > 3 && (
                <Badge variant="outline" className="text-xs bg-gray-50 text-gray-600">
                  +{university.features.length - 3}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* 重点专业 */}
        {university.keyMajors.length > 0 && (
          <div className="mb-3">
            <div className="text-sm text-gray-600 mb-1">重点专业:</div>
            <div className="text-sm text-gray-900">
              {university.keyMajors.slice(0, 3).join('、')}
              {university.keyMajors.length > 3 && '等'}
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center gap-2">
            {university.contact?.website && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(university.contact?.website, '_blank')}
                className="text-gray-600 hover:text-blue-600"
              >
                <Globe className="w-4 h-4" />
              </Button>
            )}
            {university.contact?.phone && (
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-green-600"
              >
                <Phone className="w-4 h-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetail?.(university)}
              className="text-gray-600 hover:text-blue-600"
            >
              <Info className="w-4 h-4 mr-1" />
              详情
            </Button>
          </div>

          <Button
            onClick={() => onSelect?.(university)}
            disabled={isSelected}
            className={`${
              isSelected 
                ? 'bg-green-600 hover:bg-green-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isSelected ? (
              <>
                <Check className="w-4 h-4 mr-1" />
                已选择
              </>
            ) : (
              <>
                <Plus className="w-4 h-4 mr-1" />
                选择
              </>
            )}
          </Button>
        </div>

        {/* 分析提醒 */}
        {analysis && analysis.suggestion === 'rush' && (
          <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-orange-800">
              <AlertTriangle className="w-4 h-4" />
              <span>该院校录取难度较大，建议作为冲刺志愿</span>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}
