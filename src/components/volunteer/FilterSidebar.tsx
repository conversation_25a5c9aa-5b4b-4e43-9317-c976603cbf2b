import { useState } from 'react'
import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { Label } from '../ui/label'
import { Checkbox } from '../ui/checkbox'
import { Badge } from '../ui/badge'
import type { VolunteerFilter } from '../../types/volunteer'
import { filterOptions } from '../../data/volunteer-universities'
import {
  ChevronDown,
  ChevronUp,
  Filter,
  X,
  RotateCcw
} from 'lucide-react'

interface FilterSidebarProps {
  filters: VolunteerFilter
  onFiltersChange: (filters: VolunteerFilter) => void
  className?: string
}

export function FilterSidebar({ filters, onFiltersChange, className }: FilterSidebarProps) {
  const [expandedSections, setExpandedSections] = useState({
    location: true,
    type: true,
    category: false,
    score: true,
    features: false
  })

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleProvinceChange = (province: string, checked: boolean) => {
    const newProvinces = checked
      ? [...filters.provinces, province]
      : filters.provinces.filter(p => p !== province)
    
    onFiltersChange({
      ...filters,
      provinces: newProvinces
    })
  }

  const handleTypeChange = (type: string, checked: boolean) => {
    const newTypes = checked
      ? [...filters.types, type]
      : filters.types.filter(t => t !== type)
    
    onFiltersChange({
      ...filters,
      types: newTypes
    })
  }

  const handleCategoryChange = (category: string, checked: boolean) => {
    const newCategories = checked
      ? [...filters.categories, category]
      : filters.categories.filter(c => c !== category)
    
    onFiltersChange({
      ...filters,
      categories: newCategories
    })
  }

  const handleFeatureChange = (feature: string, checked: boolean) => {
    const newFeatures = checked
      ? [...filters.features, feature]
      : filters.features.filter(f => f !== feature)
    
    onFiltersChange({
      ...filters,
      features: newFeatures
    })
  }

  const handleScoreRangeChange = (field: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0
    onFiltersChange({
      ...filters,
      scoreRange: {
        ...filters.scoreRange,
        [field]: numValue
      }
    })
  }

  const handleRankingRangeChange = (field: 'min' | 'max', value: string) => {
    const numValue = parseInt(value) || 0
    onFiltersChange({
      ...filters,
      rankingRange: {
        ...filters.rankingRange,
        [field]: numValue
      }
    })
  }

  const resetFilters = () => {
    onFiltersChange({
      provinces: [],
      types: [],
      categories: [],
      levels: ['本科'],
      scoreRange: { min: 300, max: 750 },
      rankingRange: { min: 1, max: 50000 },
      features: [],
      isPublicOnly: false,
      searchKeyword: ''
    })
  }

  const getActiveFiltersCount = () => {
    return filters.provinces.length + 
           filters.types.length + 
           filters.categories.length + 
           filters.features.length +
           (filters.isPublicOnly ? 1 : 0)
  }

  const FilterSection = ({ 
    title, 
    sectionKey, 
    children 
  }: { 
    title: string
    sectionKey: keyof typeof expandedSections
    children: React.ReactNode 
  }) => (
    <div className="border-b border-gray-200 last:border-b-0">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
      >
        <span className="font-medium text-gray-900">{title}</span>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </button>
      {expandedSections[sectionKey] && (
        <div className="px-4 pb-4">
          {children}
        </div>
      )}
    </div>
  )

  return (
    <Card className={`${className}`}>
      {/* 筛选器标题 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">筛选条件</h3>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 地区筛选 */}
      <FilterSection title="地区" sectionKey="location">
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {filterOptions.provinces.map(province => (
            <div key={province} className="flex items-center space-x-2">
              <Checkbox
                id={`province-${province}`}
                checked={filters.provinces.includes(province)}
                onCheckedChange={(checked) => handleProvinceChange(province, checked as boolean)}
              />
              <Label 
                htmlFor={`province-${province}`}
                className="text-sm text-gray-700 cursor-pointer"
              >
                {province}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      {/* 院校类型 */}
      <FilterSection title="院校类型" sectionKey="type">
        <div className="space-y-2">
          {filterOptions.types.map(type => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${type}`}
                checked={filters.types.includes(type)}
                onCheckedChange={(checked) => handleTypeChange(type, checked as boolean)}
              />
              <Label 
                htmlFor={`type-${type}`}
                className="text-sm text-gray-700 cursor-pointer"
              >
                {type}
              </Label>
            </div>
          ))}
        </div>
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="public-only"
              checked={filters.isPublicOnly}
              onCheckedChange={(checked) => onFiltersChange({
                ...filters,
                isPublicOnly: checked as boolean
              })}
            />
            <Label htmlFor="public-only" className="text-sm text-gray-700 cursor-pointer">
              仅显示公办院校
            </Label>
          </div>
        </div>
      </FilterSection>

      {/* 院校类别 */}
      <FilterSection title="院校类别" sectionKey="category">
        <div className="space-y-2">
          {filterOptions.categories.map(category => (
            <div key={category} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category}`}
                checked={filters.categories.includes(category)}
                onCheckedChange={(checked) => handleCategoryChange(category, checked as boolean)}
              />
              <Label 
                htmlFor={`category-${category}`}
                className="text-sm text-gray-700 cursor-pointer"
              >
                {category}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>

      {/* 分数范围 */}
      <FilterSection title="分数范围" sectionKey="score">
        <div className="space-y-4">
          <div>
            <Label className="text-sm text-gray-700 mb-2 block">录取分数范围</Label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={filters.scoreRange.min}
                onChange={(e) => handleScoreRangeChange('min', e.target.value)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                min={300}
                max={750}
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                value={filters.scoreRange.max}
                onChange={(e) => handleScoreRangeChange('max', e.target.value)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                min={300}
                max={750}
              />
            </div>
          </div>
          <div>
            <Label className="text-sm text-gray-700 mb-2 block">录取位次范围</Label>
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={filters.rankingRange.min}
                onChange={(e) => handleRankingRangeChange('min', e.target.value)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                min={1}
                max={50000}
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                value={filters.rankingRange.max}
                onChange={(e) => handleRankingRangeChange('max', e.target.value)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                min={1}
                max={50000}
              />
            </div>
          </div>
        </div>
      </FilterSection>

      {/* 院校特色 */}
      <FilterSection title="院校特色" sectionKey="features">
        <div className="space-y-2">
          {filterOptions.features.map(feature => (
            <div key={feature} className="flex items-center space-x-2">
              <Checkbox
                id={`feature-${feature}`}
                checked={filters.features.includes(feature)}
                onCheckedChange={(checked) => handleFeatureChange(feature, checked as boolean)}
              />
              <Label 
                htmlFor={`feature-${feature}`}
                className="text-sm text-gray-700 cursor-pointer"
              >
                {feature}
              </Label>
            </div>
          ))}
        </div>
      </FilterSection>
    </Card>
  )
}
