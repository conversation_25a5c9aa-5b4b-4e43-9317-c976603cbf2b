import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { FilterSidebar } from './FilterSidebar'
import { UniversityList } from './UniversityList'
import { UniversityListView } from './UniversityListView'
import { MajorListView } from './MajorListView'
import { VolunteerForm } from './VolunteerForm'
import { volunteerUniversities } from '../../data/volunteer-universities'
import type { VolunteerFilter, VolunteerChoice, VolunteerUniversity, VolunteerMajor, VolunteerMode, AdmissionLevel } from '../../types/volunteer'
import {
  ArrowLeft,
  Settings,
  User,
  GraduationCap,
  Target,
  BookOpen,
  Save,
  Download,
  Upload,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  School,
  Briefcase
} from 'lucide-react'

interface VolunteerApplicationPageProps {
  onBack: () => void
}

export function VolunteerApplicationPage({ onBack }: VolunteerApplicationPageProps) {
  // 填报模式状态
  const [volunteerMode, setVolunteerMode] = useState<VolunteerMode>('university')

  // 录取等级筛选状态
  const [admissionLevel, setAdmissionLevel] = useState<AdmissionLevel>('all')

  // 学生信息状态
  const [studentInfo, setStudentInfo] = useState({
    name: '',
    province: '河南',
    subject: '理科' as '理科' | '文科' | '物理' | '历史',
    score: 0,
    ranking: 0,
    category: '普通类' as '普通类' | '艺术类' | '体育类'
  })

  // 筛选条件状态
  const [filters, setFilters] = useState<VolunteerFilter>({
    provinces: [],
    types: [],
    categories: [],
    levels: ['本科'],
    scoreRange: { min: 300, max: 750 },
    rankingRange: { min: 1, max: 50000 },
    features: [],
    isPublicOnly: false,
    searchKeyword: '',
    admissionLevel: 'all'
  })

  // 志愿选择状态
  const [volunteerChoices, setVolunteerChoices] = useState<VolunteerChoice[]>([])
  const [maxChoices] = useState(12) // 最大志愿数量

  // 模拟专业数据
  const mockMajors: VolunteerMajor[] = [
    {
      id: '1',
      code: '080901',
      name: '计算机科学与技术',
      category: '工学',
      subCategory: '计算机类',
      university: { id: '1', name: '清华大学' },
      admissionScore: [{
        year: 2023,
        minScore: 685,
        avgScore: 690,
        ranking: 500
      }],
      features: ['热门专业', '就业前景好', '薪资待遇高'],
      description: '培养计算机科学与技术领域的高级专门人才'
    },
    {
      id: '2',
      code: '100201K',
      name: '临床医学',
      category: '医学',
      subCategory: '临床医学类',
      university: { id: '2', name: '北京大学' },
      admissionScore: [{
        year: 2023,
        minScore: 680,
        avgScore: 685,
        ranking: 600
      }],
      features: ['五年制', '社会地位高', '就业稳定'],
      description: '培养具备基础医学、临床医学基本理论的医学人才'
    },
    {
      id: '3',
      code: '020301K',
      name: '金融学',
      category: '经济学',
      subCategory: '金融学类',
      university: { id: '3', name: '复旦大学' },
      admissionScore: [{
        year: 2023,
        minScore: 670,
        avgScore: 675,
        ranking: 800
      }],
      features: ['高薪专业', '发展前景好', '就业面广'],
      description: '培养金融理论知识和业务技能的专门人才'
    }
  ]
  const [showStudentForm, setShowStudentForm] = useState(true)

  // 检查学生信息是否完整
  const isStudentInfoComplete = studentInfo.name && studentInfo.score > 0 && studentInfo.ranking > 0

  // 处理院校选择（院校优先模式）
  const handleUniversitySelect = (university: VolunteerUniversity, major: VolunteerMajor) => {
    if (volunteerChoices.length >= maxChoices) {
      alert(`最多只能选择 ${maxChoices} 个志愿`)
      return
    }

    const newChoice: VolunteerChoice = {
      id: `choice-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      university,
      major,
      majors: [major.name],
      priority: volunteerChoices.length + 1,
      isLocked: false,
      addedAt: new Date(),
      mode: 'university'
    }

    setVolunteerChoices([...volunteerChoices, newChoice])
  }

  // 处理专业选择（专业优先模式）
  const handleMajorSelect = (major: VolunteerMajor) => {
    if (volunteerChoices.length >= maxChoices) {
      alert(`最多只能选择 ${maxChoices} 个志愿`)
      return
    }

    const newChoice: VolunteerChoice = {
      id: `choice-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      major,
      majors: [major.name],
      priority: volunteerChoices.length + 1,
      isLocked: false,
      addedAt: new Date(),
      mode: 'major'
    }

    setVolunteerChoices([...volunteerChoices, newChoice])
  }

  // 处理志愿移除
  const handleRemoveChoice = (choiceId: string) => {
    const updatedChoices = volunteerChoices
      .filter(choice => choice.id !== choiceId)
      .map((choice, index) => ({
        ...choice,
        priority: index + 1
      }))
    setVolunteerChoices(updatedChoices)
  }

  // 处理志愿更新
  const handleChoicesChange = (choices: VolunteerChoice[]) => {
    setVolunteerChoices(choices)
  }

  // 处理院校详情查看
  const handleUniversityDetail = (university: VolunteerUniversity) => {
    // 这里可以打开详情弹窗或跳转到详情页
    console.log('查看院校详情:', university.name)
  }

  // 保存志愿表
  const handleSaveVolunteerForm = () => {
    const volunteerForm = {
      studentInfo,
      choices: volunteerChoices,
      maxChoices,
      currentBatch: '本科一批' as const,
      isDraft: true,
      lastSaved: new Date()
    }
    
    // 这里可以调用API保存到后端
    localStorage.setItem('volunteerForm', JSON.stringify(volunteerForm))
    alert('志愿表已保存')
  }

  // 加载保存的志愿表
  const handleLoadVolunteerForm = () => {
    const saved = localStorage.getItem('volunteerForm')
    if (saved) {
      const volunteerForm = JSON.parse(saved)
      setStudentInfo(volunteerForm.studentInfo)
      setVolunteerChoices(volunteerForm.choices)
      alert('志愿表已加载')
    } else {
      alert('没有找到保存的志愿表')
    }
  }

  // 获取已选择的院校ID列表
  const selectedUniversityIds = volunteerChoices
    .filter(choice => choice.mode === 'university' && choice.university)
    .map(choice => choice.university!.id)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                返回
              </Button>
              <div className="flex items-center gap-2">
                <GraduationCap className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">志愿填报</h1>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {isStudentInfoComplete && (
                <div className="flex items-center gap-2 text-sm">
                  <User className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-700">{studentInfo.name}</span>
                  <Badge variant="outline">{studentInfo.score}分</Badge>
                  <Badge variant="outline">位次 {studentInfo.ranking}</Badge>
                </div>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowStudentForm(!showStudentForm)}
                className="flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                设置
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleLoadVolunteerForm}
                className="flex items-center gap-2"
              >
                <Upload className="w-4 h-4" />
                加载
              </Button>

              <Button
                size="sm"
                onClick={handleSaveVolunteerForm}
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                保存
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 学生信息表单 */}
      {showStudentForm && (
        <div className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 py-4">
            <Card className="p-4">
              <div className="flex items-center gap-2 mb-4">
                <User className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">考生信息</h3>
                {isStudentInfoComplete ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-orange-600" />
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div>
                  <Label htmlFor="name">姓名</Label>
                  <Input
                    id="name"
                    value={studentInfo.name}
                    onChange={(e) => setStudentInfo({...studentInfo, name: e.target.value})}
                    placeholder="请输入姓名"
                  />
                </div>
                
                <div>
                  <Label htmlFor="province">省份</Label>
                  <select
                    id="province"
                    value={studentInfo.province}
                    onChange={(e) => setStudentInfo({...studentInfo, province: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="河南">河南</option>
                    <option value="北京">北京</option>
                    <option value="上海">上海</option>
                    <option value="广东">广东</option>
                    <option value="江苏">江苏</option>
                    <option value="浙江">浙江</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="subject">科目</Label>
                  <select
                    id="subject"
                    value={studentInfo.subject}
                    onChange={(e) => setStudentInfo({...studentInfo, subject: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="理科">理科</option>
                    <option value="文科">文科</option>
                    <option value="物理">物理</option>
                    <option value="历史">历史</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="score">高考分数</Label>
                  <Input
                    id="score"
                    type="number"
                    value={studentInfo.score || ''}
                    onChange={(e) => setStudentInfo({...studentInfo, score: parseInt(e.target.value) || 0})}
                    placeholder="分数"
                    min={0}
                    max={750}
                  />
                </div>

                <div>
                  <Label htmlFor="ranking">省内位次</Label>
                  <Input
                    id="ranking"
                    type="number"
                    value={studentInfo.ranking || ''}
                    onChange={(e) => setStudentInfo({...studentInfo, ranking: parseInt(e.target.value) || 0})}
                    placeholder="位次"
                    min={1}
                  />
                </div>

                <div>
                  <Label htmlFor="category">类别</Label>
                  <select
                    id="category"
                    value={studentInfo.category}
                    onChange={(e) => setStudentInfo({...studentInfo, category: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="普通类">普通类</option>
                    <option value="艺术类">艺术类</option>
                    <option value="体育类">体育类</option>
                  </select>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* 模式切换和等级筛选 */}
      <div className="container mx-auto px-4 py-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            {/* 填报模式切换 */}
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">填报模式:</span>
              <div className="flex items-center gap-2">
                <Button
                  variant={volunteerMode === 'university' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setVolunteerMode('university')}
                  className="flex items-center gap-2"
                >
                  <School className="w-4 h-4" />
                  院校优先
                </Button>
                <Button
                  variant={volunteerMode === 'major' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setVolunteerMode('major')}
                  className="flex items-center gap-2"
                >
                  <Briefcase className="w-4 h-4" />
                  专业优先
                </Button>
              </div>
            </div>

            {/* 录取等级筛选 */}
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">录取等级:</span>
              <div className="flex items-center gap-2">
                <Button
                  variant={admissionLevel === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdmissionLevel('all')}
                >
                  全部
                </Button>
                <Button
                  variant={admissionLevel === 'rush' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdmissionLevel('rush')}
                  className="bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200"
                >
                  冲
                </Button>
                <Button
                  variant={admissionLevel === 'stable' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdmissionLevel('stable')}
                  className="bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200"
                >
                  稳
                </Button>
                <Button
                  variant={admissionLevel === 'safe' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdmissionLevel('safe')}
                  className="bg-green-100 text-green-800 border-green-200 hover:bg-green-200"
                >
                  保
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧筛选栏 */}
          <div className="lg:col-span-1">
            <FilterSidebar
              filters={filters}
              onFiltersChange={setFilters}
              className="sticky top-24"
            />
          </div>

          {/* 中间列表区域 */}
          <div className="lg:col-span-2">
            {volunteerMode === 'university' ? (
              <UniversityListView
                universities={volunteerUniversities}
                selectedUniversities={selectedUniversityIds}
                userScore={studentInfo.score}
                userRanking={studentInfo.ranking}
                admissionLevel={admissionLevel}
                searchKeyword={filters.searchKeyword}
                onUniversitySelect={handleUniversitySelect}
                onSearchChange={(keyword) => setFilters({...filters, searchKeyword: keyword})}
              />
            ) : (
              <MajorListView
                majors={mockMajors}
                selectedMajors={volunteerChoices.filter(c => c.mode === 'major').map(c => c.major?.id || '')}
                userScore={studentInfo.score}
                userRanking={studentInfo.ranking}
                admissionLevel={admissionLevel}
                searchKeyword={filters.searchKeyword}
                onMajorSelect={handleMajorSelect}
                onSearchChange={(keyword) => setFilters({...filters, searchKeyword: keyword})}
              />
            )}
          </div>

          {/* 右侧志愿表 */}
          <div className="lg:col-span-1">
            <VolunteerForm
              choices={volunteerChoices}
              maxChoices={maxChoices}
              onChoicesChange={handleChoicesChange}
              onRemoveChoice={handleRemoveChoice}
              className="sticky top-24"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
