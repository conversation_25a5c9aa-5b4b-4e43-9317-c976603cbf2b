import { useState } from 'react'
import { Card } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import type { VolunteerChoice, VolunteerUniversity } from '../../types/volunteer'
import {
  GripVertical,
  X,
  Edit3,
  Save,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Trash2
} from 'lucide-react'

interface VolunteerFormProps {
  choices: VolunteerChoice[]
  maxChoices: number
  onChoicesChange: (choices: VolunteerChoice[]) => void
  onRemoveChoice: (choiceId: string) => void
  className?: string
}

export function VolunteerForm({
  choices,
  maxChoices,
  onChoicesChange,
  onRemoveChoice,
  className
}: VolunteerFormProps) {
  const [editingChoice, setEditingChoice] = useState<string | null>(null)
  const [editingMajors, setEditingMajors] = useState<string>('')
  const [editingNotes, setEditingNotes] = useState<string>('')

  const handleEditChoice = (choice: VolunteerChoice) => {
    setEditingChoice(choice.id)
    setEditingMajors(choice.majors.join('、'))
    setEditingNotes(choice.notes || '')
  }

  const handleSaveChoice = (choiceId: string) => {
    const updatedChoices = choices.map(choice => {
      if (choice.id === choiceId) {
        return {
          ...choice,
          majors: editingMajors.split('、').filter(m => m.trim()),
          notes: editingNotes.trim()
        }
      }
      return choice
    })
    onChoicesChange(updatedChoices)
    setEditingChoice(null)
    setEditingMajors('')
    setEditingNotes('')
  }

  const handleCancelEdit = () => {
    setEditingChoice(null)
    setEditingMajors('')
    setEditingNotes('')
  }

  const handleLockChoice = (choiceId: string) => {
    const updatedChoices = choices.map(choice => {
      if (choice.id === choiceId) {
        return {
          ...choice,
          isLocked: !choice.isLocked
        }
      }
      return choice
    })
    onChoicesChange(updatedChoices)
  }

  const moveChoice = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= choices.length) return
    
    const newChoices = [...choices]
    const [movedChoice] = newChoices.splice(fromIndex, 1)
    newChoices.splice(toIndex, 0, movedChoice)
    
    // 更新优先级
    const updatedChoices = newChoices.map((choice, index) => ({
      ...choice,
      priority: index + 1
    }))
    
    onChoicesChange(updatedChoices)
  }

  const getChoiceStatusColor = (choice: VolunteerChoice) => {
    if (choice.isLocked) return 'bg-green-100 border-green-300'
    if (editingChoice === choice.id) return 'bg-blue-100 border-blue-300'
    return 'bg-white border-gray-200'
  }

  const getChoiceStatusIcon = (choice: VolunteerChoice) => {
    if (choice.isLocked) return <CheckCircle className="w-4 h-4 text-green-600" />
    if (editingChoice === choice.id) return <Edit3 className="w-4 h-4 text-blue-600" />
    return <Clock className="w-4 h-4 text-gray-400" />
  }

  const getSuggestionBadge = (priority: number) => {
    if (priority <= Math.ceil(maxChoices * 0.3)) {
      return <Badge className="bg-orange-100 text-orange-800 border-orange-200">冲刺</Badge>
    } else if (priority <= Math.ceil(maxChoices * 0.7)) {
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">稳妥</Badge>
    } else {
      return <Badge className="bg-green-100 text-green-800 border-green-200">保底</Badge>
    }
  }

  return (
    <Card className={className}>
      {/* 表头 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">我的志愿表</h3>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              {choices.length}/{maxChoices}
            </Badge>
          </div>
          <div className="text-sm text-gray-500">
            最后保存: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>

      {/* 志愿列表 */}
      <div className="p-4">
        {choices.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 mb-2">还没有添加志愿</p>
            <p className="text-sm text-gray-400">从左侧院校列表中选择心仪的大学</p>
          </div>
        ) : (
          <div className="space-y-3">
            {choices.map((choice, index) => (
              <div
                key={choice.id}
                className={`border rounded-lg p-3 transition-all duration-200 ${getChoiceStatusColor(choice)}`}
              >
                <div className="flex items-start gap-3">
                  {/* 拖拽手柄和序号 */}
                  <div className="flex flex-col items-center gap-1">
                    <GripVertical className="w-4 h-4 text-gray-400 cursor-move" />
                    <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {choice.priority}
                    </div>
                  </div>

                  {/* 志愿信息 */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {choice.mode === 'university' ? (
                          <h4 className="font-medium text-gray-900">{choice.university?.name}</h4>
                        ) : (
                          <div>
                            <h4 className="font-medium text-gray-900">{choice.major?.name}</h4>
                            <p className="text-sm text-gray-600">{choice.major?.university.name}</p>
                          </div>
                        )}
                        {getSuggestionBadge(choice.priority)}
                        {getChoiceStatusIcon(choice)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleLockChoice(choice.id)}
                          className={`${choice.isLocked ? 'text-green-600' : 'text-gray-400'}`}
                        >
                          {choice.isLocked ? <CheckCircle className="w-4 h-4" /> : <Clock className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditChoice(choice)}
                          disabled={choice.isLocked}
                          className="text-blue-600"
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveChoice(choice.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="text-sm text-gray-600 mb-2">
                      {choice.mode === 'university' ? (
                        `${choice.university?.location.province} · ${choice.university?.type} · ${choice.university?.category}`
                      ) : (
                        `${choice.major?.category} · ${choice.major?.subCategory || ''}`
                      )}
                    </div>

                    {/* 专业编辑 */}
                    {editingChoice === choice.id ? (
                      <div className="space-y-3">
                        {choice.mode === 'university' && (
                          <div>
                            <Label className="text-sm text-gray-700">专业选择 (用"、"分隔)</Label>
                            <Input
                              value={editingMajors}
                              onChange={(e) => setEditingMajors(e.target.value)}
                              placeholder="请输入专业名称，如：计算机科学与技术、软件工程"
                              className="mt-1"
                            />
                          </div>
                        )}
                        <div>
                          <Label className="text-sm text-gray-700">备注</Label>
                          <Input
                            value={editingNotes}
                            onChange={(e) => setEditingNotes(e.target.value)}
                            placeholder="添加备注信息..."
                            className="mt-1"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleSaveChoice(choice.id)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Save className="w-4 h-4 mr-1" />
                            保存
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                          >
                            取消
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        {choice.mode === 'university' && choice.majors.length > 0 ? (
                          <div className="mb-2">
                            <span className="text-sm text-gray-600">专业: </span>
                            <span className="text-sm text-gray-900">{choice.majors.join('、')}</span>
                          </div>
                        ) : choice.mode === 'major' ? (
                          <div className="mb-2">
                            <span className="text-sm text-gray-600">专业代码: </span>
                            <span className="text-sm text-gray-900">{choice.major?.code}</span>
                          </div>
                        ) : (
                          <div className="text-sm text-orange-600 mb-2">
                            <AlertCircle className="w-4 h-4 inline mr-1" />
                            请选择专业
                          </div>
                        )}
                        {choice.notes && (
                          <div className="text-sm text-gray-600">
                            <span className="text-gray-500">备注: </span>
                            {choice.notes}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部操作 */}
      {choices.length > 0 && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              建议: 冲刺 {Math.ceil(maxChoices * 0.3)} 个，稳妥 {Math.ceil(maxChoices * 0.4)} 个，保底 {Math.floor(maxChoices * 0.3)} 个
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <FileText className="w-4 h-4 mr-1" />
                导出
              </Button>
              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-1" />
                保存志愿表
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
