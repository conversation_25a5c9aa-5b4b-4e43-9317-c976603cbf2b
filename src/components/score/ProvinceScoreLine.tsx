import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'
import { Loader2, Search, MapPin, Calendar, BookOpen, Trophy } from 'lucide-react'
import {
  getProvinceScoreLine,
  PROVINCES,
  SUBJECT_TYPES,
  getAvailableSubjectSelections
} from '../../services/scoreLineApiSimple'
import type {
  ProvinceScoreLineFilters,
  ProvinceScoreLineData
} from '../../services/scoreLineApiSimple'

interface ProvinceScoreLineProps {
  className?: string
}

export function ProvinceScoreLine({ className }: ProvinceScoreLineProps) {
  const [filters, setFilters] = useState<ProvinceScoreLineFilters>({
    keyword: '安徽',
    year: 2024,
    category: '物理类'
  })

  const [data, setData] = useState<ProvinceScoreLineData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)

  // 根据当前选择的省份和年份获取可用的科目类型
  const availableSubjects = getAvailableSubjectSelections(filters.keyword, filters.year?.toString())

  // 查询省录取分数线
  const handleSearch = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await getProvinceScoreLine(filters)
      
      if (response.DataStatus.StatusCode === 100) {
        setData(response.Data || [])
        setTotalCount(response.DataStatus.DataTotalCount || 0)
        console.log('✅ 省录取分数线查询成功，获取到', response.Data?.length || 0, '条数据')
      } else {
        throw new Error(response.DataStatus.StatusDescription || '查询失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败'
      setError(errorMessage)
      console.error('❌ 省录取分数线查询失败:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // 重置筛选条件
  const handleReset = () => {
    setFilters({
      keyword: '',
      year: undefined,
      category: ''
    })
    setData([])
    setError(null)
  }

  // 获取批次颜色
  const getBatchColor = (batch: string) => {
    if (batch.includes('本科一批') || batch.includes('特殊类型')) return 'bg-red-100 text-red-800'
    if (batch.includes('本科二批')) return 'bg-orange-100 text-orange-800'
    if (batch.includes('本科批')) return 'bg-blue-100 text-blue-800'
    if (batch.includes('专科')) return 'bg-green-100 text-green-800'
    if (batch.includes('艺术') || batch.includes('体育')) return 'bg-purple-100 text-purple-800'
    return 'bg-gray-100 text-gray-800'
  }

  return (
    <div className={className}>
      {/* 查询表单 */}
      <Card className="p-6 mb-6">
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="w-5 h-5 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">省录取分数线查询</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* 省份选择 */}
          <div>
            <Label htmlFor="province" className="text-sm font-medium text-gray-700">省份</Label>
            <Select
              value={filters.keyword || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, keyword: value }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择省份" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部省份</SelectItem>
                {PROVINCES.map(province => (
                  <SelectItem key={province} value={province}>{province}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 年份选择 */}
          <div>
            <Label htmlFor="year" className="text-sm font-medium text-gray-700">年份</Label>
            <Select
              value={filters.year?.toString() || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, year: value ? parseInt(value) : undefined }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择年份" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部年份</SelectItem>
                {Array.from({ length: 12 }, (_, i) => 2025 - i).map(year => (
                  <SelectItem key={year} value={year.toString()}>{year}年</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 科目类型选择 */}
          <div>
            <Label htmlFor="category" className="text-sm font-medium text-gray-700">科目类型</Label>
            <Select
              value={filters.category || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择科目类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部类型</SelectItem>
                {availableSubjects.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-end gap-2">
            <Button
              onClick={handleSearch}
              disabled={isLoading}
              className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Search className="w-4 h-4 mr-2" />
              )}
              查询
            </Button>
            <Button
              onClick={handleReset}
              variant="outline"
              className="px-3"
            >
              重置
            </Button>
          </div>
        </div>

        {/* 查询统计 */}
        {totalCount > 0 && (
          <div className="text-sm text-gray-600">
            共找到 <span className="font-medium text-blue-600">{totalCount}</span> 条录取分数线数据
          </div>
        )}
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="p-4 mb-6 border-red-200 bg-red-50">
          <div className="text-red-800 text-sm">{error}</div>
        </Card>
      )}

      {/* 数据列表 */}
      {data.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Trophy className="w-5 h-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-gray-900">录取分数线</h3>
          </div>
          
          <div className="space-y-4">
            {data.map((item, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-gray-900">{item.Province}</h4>
                      <Badge variant="outline" className={getBatchColor(item.ScoreBatch)}>
                        {item.ScoreBatch}
                      </Badge>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">
                        {item.Category}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-600">年份:</span>
                        <span className="font-medium">{item.Year}年</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <BookOpen className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-600">分数线:</span>
                        <span className="font-bold text-red-600 text-lg">{item.Score}分</span>
                      </div>
                      
                      {item.SpecialName && (
                        <div className="col-span-2">
                          <span className="text-gray-600">专业说明:</span>
                          <span className="ml-2 text-gray-800">{item.SpecialName}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 空状态 */}
      {!isLoading && data.length === 0 && !error && (
        <Card className="p-8 text-center">
          <div className="text-gray-500 mb-2">暂无数据</div>
          <div className="text-sm text-gray-400">请调整查询条件后重试</div>
        </Card>
      )}
    </div>
  )
}
