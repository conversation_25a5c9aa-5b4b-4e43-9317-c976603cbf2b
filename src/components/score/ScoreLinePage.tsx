import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Card } from '../ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs'
import { ArrowLeft, BarChart3, MapPin, School, Info } from 'lucide-react'
import { ProvinceScoreLine } from './ProvinceScoreLine'
import { CollegeScoreLine } from './CollegeScoreLine'

interface ScoreLinePageProps {
  onBack?: () => void
}

export function ScoreLinePage({ onBack }: ScoreLinePageProps) {
  const [activeTab, setActiveTab] = useState('province')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/80 via-white/80 to-blue-50/80">
      {/* 顶部导航 */}
      <div className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  返回首页
                </Button>
              )}
              <div className="flex items-center gap-2">
                <BarChart3 className="w-6 h-6 text-red-600" />
                <h1 className="text-xl font-bold text-gray-900">查录取分数</h1>
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              数据来源：咕咕数据API
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="container mx-auto px-4 py-8">
        {/* 功能介绍 */}
        <Card className="p-6 mb-8 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">录取分数线查询功能</h2>
              <div className="text-sm text-gray-700 space-y-1">
                <p>• <strong>省录取分数线：</strong>查询各省份历年高考录取分数线，包含不同批次和科目类型</p>
                <p>• <strong>高校录取分数线：</strong>查询具体高校在各省的录取分数线，支持按省份或高校名称查询</p>
                <p>• <strong>数据范围：</strong>涵盖2014-2025年全国各省份和高校的录取数据</p>
              </div>
            </div>
          </div>
        </Card>

        {/* 功能选项卡 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="province" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              省录取分数线
            </TabsTrigger>
            <TabsTrigger value="college" className="flex items-center gap-2">
              <School className="w-4 h-4" />
              高校录取分数线
            </TabsTrigger>
          </TabsList>

          <TabsContent value="province" className="space-y-6">
            <ProvinceScoreLine />
          </TabsContent>

          <TabsContent value="college" className="space-y-6">
            <CollegeScoreLine />
          </TabsContent>
        </Tabs>

        {/* 使用说明 */}
        <Card className="p-6 mt-8 bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">使用说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-700">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">省录取分数线查询</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>选择省份、年份和科目类型进行查询</li>
                <li>可查看各批次的录取分数线</li>
                <li>支持查看专业说明和详细信息</li>
                <li>数据按年份和批次分类显示</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">高校录取分数线查询</h4>
              <ul className="space-y-1 list-disc list-inside">
                <li>支持按省份或高校名称查询</li>
                <li>可查看最高分、平均分、最低分和位次</li>
                <li>显示985/211/双一流等学校标识</li>
                <li>支持分页浏览和高级筛选</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* 数据说明 */}
        <Card className="p-6 mt-6 border-orange-200 bg-orange-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">数据说明</h3>
          <div className="text-sm text-gray-700 space-y-2">
            <p>• 数据来源于官方教育部门和各省教育考试院</p>
            <p>• 录取分数线会根据当年招生计划和考生情况有所变化</p>
            <p>• 建议结合多年数据进行参考，不作为唯一填报依据</p>
            <p>• 部分特殊类型招生可能有单独的录取标准</p>
          </div>
        </Card>
      </div>
    </div>
  )
}
