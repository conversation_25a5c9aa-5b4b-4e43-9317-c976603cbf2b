"use client";

import { cn } from "@/lib/utils";
import { useEffect, useRef } from "react";

export function SimpleGlobe({
  className,
}: {
  className?: string;
}) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const size = 400;
    canvas.width = size;
    canvas.height = size;

    let rotation = 0;

    const animate = () => {
      // Clear canvas
      ctx.clearRect(0, 0, size, size);

      // Draw globe background
      const centerX = size / 2;
      const centerY = size / 2;
      const radius = size / 2 - 20;

      // Create gradient for globe
      const gradient = ctx.createRadialGradient(
        centerX - radius / 3, centerY - radius / 3, 0,
        centerX, centerY, radius
      );
      gradient.addColorStop(0, '#4f46e5');
      gradient.addColorStop(0.7, '#1e40af');
      gradient.addColorStop(1, '#1e3a8a');

      // Draw main globe
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();

      // Draw grid lines
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 1;

      // Vertical lines (longitude)
      for (let i = 0; i < 12; i++) {
        const angle = (i * Math.PI) / 6 + rotation;
        const x1 = centerX + Math.cos(angle) * radius;
        const y1 = centerY;
        const x2 = centerX + Math.cos(angle + Math.PI) * radius;
        const y2 = centerY;

        ctx.beginPath();
        ctx.ellipse(centerX, centerY, Math.abs(x1 - centerX), radius, 0, 0, Math.PI * 2);
        ctx.stroke();
      }

      // Horizontal lines (latitude)
      for (let i = 1; i < 6; i++) {
        const y = centerY + (radius * Math.cos((i * Math.PI) / 6)) * Math.cos(rotation * 0.5);
        const radiusX = radius * Math.sin((i * Math.PI) / 6);

        ctx.beginPath();
        ctx.ellipse(centerX, y, radiusX, radiusX * 0.3, 0, 0, Math.PI * 2);
        ctx.stroke();

        if (i !== 3) { // Don't draw the equator twice
          const y2 = centerY - (radius * Math.cos((i * Math.PI) / 6)) * Math.cos(rotation * 0.5);
          ctx.beginPath();
          ctx.ellipse(centerX, y2, radiusX, radiusX * 0.3, 0, 0, Math.PI * 2);
          ctx.stroke();
        }
      }

      // Draw some "continents" (simple shapes)
      ctx.fillStyle = 'rgba(34, 197, 94, 0.8)';
      
      // Continent 1
      const cont1X = centerX + Math.cos(rotation + 0.5) * radius * 0.6;
      const cont1Y = centerY + Math.sin(rotation + 0.5) * radius * 0.3;
      if (Math.cos(rotation + 0.5) > 0) { // Only draw if on visible side
        ctx.beginPath();
        ctx.ellipse(cont1X, cont1Y, 30, 20, rotation, 0, Math.PI * 2);
        ctx.fill();
      }

      // Continent 2
      const cont2X = centerX + Math.cos(rotation + 2) * radius * 0.7;
      const cont2Y = centerY + Math.sin(rotation + 2) * radius * 0.4;
      if (Math.cos(rotation + 2) > 0) {
        ctx.beginPath();
        ctx.ellipse(cont2X, cont2Y, 25, 35, rotation + 0.5, 0, Math.PI * 2);
        ctx.fill();
      }

      // Continent 3
      const cont3X = centerX + Math.cos(rotation + 4) * radius * 0.5;
      const cont3Y = centerY + Math.sin(rotation + 4) * radius * 0.6;
      if (Math.cos(rotation + 4) > 0) {
        ctx.beginPath();
        ctx.ellipse(cont3X, cont3Y, 20, 15, rotation - 0.3, 0, Math.PI * 2);
        ctx.fill();
      }

      // Add some "cities" (small dots)
      ctx.fillStyle = 'rgba(251, 191, 36, 0.9)';
      const cities = [
        { angle: 1, radius: 0.8 },
        { angle: 2.5, radius: 0.6 },
        { angle: 4.2, radius: 0.7 },
        { angle: 5.8, radius: 0.5 },
      ];

      cities.forEach(city => {
        const cityX = centerX + Math.cos(rotation + city.angle) * radius * city.radius;
        const cityY = centerY + Math.sin(rotation + city.angle) * radius * city.radius * 0.5;
        if (Math.cos(rotation + city.angle) > 0) {
          ctx.beginPath();
          ctx.arc(cityX, cityY, 3, 0, Math.PI * 2);
          ctx.fill();
        }
      });

      // Update rotation
      rotation += 0.005;

      requestAnimationFrame(animate);
    };

    animate();

    // Cleanup function
    return () => {
      // Animation will stop when component unmounts
    };
  }, []);

  return (
    <div
      className={cn(
        "absolute inset-0 mx-auto aspect-[1/1] w-full max-w-[600px] flex items-center justify-center",
        className,
      )}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full max-w-[400px] max-h-[400px] cursor-grab active:cursor-grabbing"
        style={{ filter: 'drop-shadow(0 0 20px rgba(79, 70, 229, 0.3))' }}
      />
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <p className="text-sm text-muted-foreground text-center">
          🌍 简化版地球动画
        </p>
      </div>
    </div>
  );
}
