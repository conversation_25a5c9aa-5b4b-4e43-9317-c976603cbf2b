import { useState } from "react"
import { ArrowLeft, User, ChevronLeft, ChevronRight, Clock, CheckCircle, Brain, Eye, Heart, Zap } from "lucide-react"
import { Button } from "../ui/button"
import { Progress } from "../ui/progress"

interface MBTIPersonalityTestProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

// 模拟MBTI测试题目数据
const testQuestions = [
  {
    id: 1,
    question: "在聚会中，我通常：",
    options: [
      { text: "主动与很多人交谈", value: "E", dimension: "EI" },
      { text: "只与少数几个人深入交流", value: "I", dimension: "EI" }
    ]
  },
  {
    id: 2,
    question: "我更倾向于：",
    options: [
      { text: "关注具体的事实和细节", value: "S", dimension: "SN" },
      { text: "关注整体的概念和可能性", value: "N", dimension: "SN" }
    ]
  },
  {
    id: 3,
    question: "做决定时，我更依赖：",
    options: [
      { text: "逻辑分析和客观标准", value: "T", dimension: "TF" },
      { text: "个人价值观和他人感受", value: "F", dimension: "TF" }
    ]
  },
  {
    id: 4,
    question: "我更喜欢：",
    options: [
      { text: "有明确的计划和截止日期", value: "J", dimension: "JP" },
      { text: "保持灵活性和开放性", value: "P", dimension: "JP" }
    ]
  },
  {
    id: 5,
    question: "休息时，我更愿意：",
    options: [
      { text: "参加社交活动", value: "E", dimension: "EI" },
      { text: "独自安静地放松", value: "I", dimension: "EI" }
    ]
  },
  {
    id: 6,
    question: "学习新事物时，我更喜欢：",
    options: [
      { text: "从具体例子开始", value: "S", dimension: "SN" },
      { text: "先了解整体理论", value: "N", dimension: "SN" }
    ]
  },
  {
    id: 7,
    question: "批评别人时，我更关注：",
    options: [
      { text: "事实和逻辑", value: "T", dimension: "TF" },
      { text: "对方的感受", value: "F", dimension: "TF" }
    ]
  },
  {
    id: 8,
    question: "我的工作风格是：",
    options: [
      { text: "按计划有序进行", value: "J", dimension: "JP" },
      { text: "随机应变，灵活调整", value: "P", dimension: "JP" }
    ]
  },
  {
    id: 9,
    question: "我更容易注意到：",
    options: [
      { text: "周围环境的变化", value: "E", dimension: "EI" },
      { text: "内心想法的变化", value: "I", dimension: "EI" }
    ]
  },
  {
    id: 10,
    question: "我更信任：",
    options: [
      { text: "经验和实践", value: "S", dimension: "SN" },
      { text: "直觉和灵感", value: "N", dimension: "SN" }
    ]
  },
  {
    id: 11,
    question: "解决冲突时，我更看重：",
    options: [
      { text: "公平和正义", value: "T", dimension: "TF" },
      { text: "和谐和理解", value: "F", dimension: "TF" }
    ]
  },
  {
    id: 12,
    question: "我更喜欢的生活方式是：",
    options: [
      { text: "有规律和结构", value: "J", dimension: "JP" },
      { text: "自由和随性", value: "P", dimension: "JP" }
    ]
  },
  {
    id: 13,
    question: "在团队中，我更愿意：",
    options: [
      { text: "积极发言和表达", value: "E", dimension: "EI" },
      { text: "仔细倾听和思考", value: "I", dimension: "EI" }
    ]
  },
  {
    id: 14,
    question: "我更感兴趣的是：",
    options: [
      { text: "现实存在的事物", value: "S", dimension: "SN" },
      { text: "未来的可能性", value: "N", dimension: "SN" }
    ]
  },
  {
    id: 15,
    question: "评价一个想法时，我首先考虑：",
    options: [
      { text: "是否合理和有效", value: "T", dimension: "TF" },
      { text: "是否符合价值观", value: "F", dimension: "TF" }
    ]
  },
  {
    id: 16,
    question: "我更喜欢：",
    options: [
      { text: "提前做好准备", value: "J", dimension: "JP" },
      { text: "临时应变处理", value: "P", dimension: "JP" }
    ]
  }
]

export function MBTIPersonalityTest({ onBack, onNavigate }: MBTIPersonalityTestProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<number, string>>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [startTime] = useState(Date.now())

  const handleAnswer = (value: string) => {
    const newAnswers = { ...answers, [testQuestions[currentQuestion].id]: value }
    setAnswers(newAnswers)

    if (currentQuestion < testQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1)
      }, 300)
    } else {
      setTimeout(() => {
        setIsCompleted(true)
      }, 300)
    }
  }

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestion < testQuestions.length - 1 && answers[testQuestions[currentQuestion].id]) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const calculateMBTIType = () => {
    const scores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 }
    
    testQuestions.forEach(question => {
      const answer = answers[question.id]
      if (answer) {
        scores[answer as keyof typeof scores]++
      }
    })

    const type = 
      (scores.E > scores.I ? 'E' : 'I') +
      (scores.S > scores.N ? 'S' : 'N') +
      (scores.T > scores.F ? 'T' : 'F') +
      (scores.J > scores.P ? 'J' : 'P')

    return { type, scores }
  }

  const getMBTIDescription = (type: string) => {
    const descriptions: Record<string, any> = {
      INTJ: {
        name: "建筑师",
        description: "富有想象力和战略性的思想家，一切皆在计划之中",
        strengths: ["独立思考", "战略规划", "系统性思维", "追求完美"],
        careers: ["科学家", "工程师", "建筑师", "系统分析师", "战略规划师"],
        famous: ["牛顿", "特斯拉", "马克·扎克伯格"],
        color: "bg-purple-500"
      },
      INTP: {
        name: "思想家",
        description: "具有创新精神的发明家，对知识有着不可抑制的渴望",
        strengths: ["逻辑分析", "理论思维", "创新能力", "独立性强"],
        careers: ["研究员", "哲学家", "数学家", "程序员", "理论物理学家"],
        famous: ["爱因斯坦", "达尔文", "比尔·盖茨"],
        color: "bg-indigo-500"
      },
      ENTJ: {
        name: "指挥官",
        description: "大胆、富有想象力、意志强烈的领导者，总能找到或创造解决方法",
        strengths: ["领导能力", "战略思维", "决策果断", "目标导向"],
        careers: ["CEO", "企业家", "管理顾问", "投资银行家", "律师"],
        famous: ["史蒂夫·乔布斯", "拿破仑", "玛格丽特·撒切尔"],
        color: "bg-red-500"
      },
      ENTP: {
        name: "辩论家",
        description: "聪明好奇的思想家，不会拒绝智力上的挑战",
        strengths: ["创新思维", "适应能力", "沟通能力", "灵活性强"],
        careers: ["创业者", "营销专家", "记者", "心理学家", "发明家"],
        famous: ["托马斯·爱迪生", "本杰明·富兰克林", "马克·吐温"],
        color: "bg-orange-500"
      },
      INFJ: {
        name: "提倡者",
        description: "安静而神秘，同时鼓舞人心且不知疲倦的理想主义者",
        strengths: ["洞察力强", "理想主义", "创造性", "同理心"],
        careers: ["心理咨询师", "作家", "教师", "社会工作者", "艺术家"],
        famous: ["甘地", "马丁·路德·金", "柏拉图"],
        color: "bg-green-500"
      },
      INFP: {
        name: "调停者",
        description: "诗意、善良、利他主义，总是热切地为正当理由而努力",
        strengths: ["价值观坚定", "创造力", "适应性", "个人成长"],
        careers: ["作家", "心理学家", "艺术家", "社会工作者", "记者"],
        famous: ["莎士比亚", "J.K.罗琳", "约翰·列侬"],
        color: "bg-teal-500"
      },
      ENFJ: {
        name: "主人公",
        description: "富有魅力、鼓舞人心的领导者，有能力让听众着迷",
        strengths: ["领导魅力", "沟通能力", "同理心", "激励他人"],
        careers: ["教师", "培训师", "政治家", "人力资源", "心理咨询师"],
        famous: ["奥普拉·温弗瑞", "奥巴马", "马丁·路德·金"],
        color: "bg-blue-500"
      },
      ENFP: {
        name: "竞选者",
        description: "热情、有创造力、社交能力强，总能找到微笑的理由",
        strengths: ["热情活力", "创造力", "人际关系", "适应能力"],
        careers: ["记者", "心理学家", "演员", "营销专员", "社会工作者"],
        famous: ["罗宾·威廉姆斯", "威尔·史密斯", "艾伦·德杰尼勒斯"],
        color: "bg-pink-500"
      },
      ISTJ: {
        name: "物流师",
        description: "实用主义的逻辑学家，忠诚可靠，不会忽视细节",
        strengths: ["责任心强", "注重细节", "组织能力", "可靠性"],
        careers: ["会计师", "审计师", "银行家", "法官", "工程师"],
        famous: ["华盛顿", "沃伦·巴菲特", "安吉拉·默克尔"],
        color: "bg-gray-500"
      },
      ISFJ: {
        name: "守护者",
        description: "非常专注、温暖的守护者，时刻准备保护爱着的人们",
        strengths: ["关怀他人", "责任心", "实用性", "忠诚度"],
        careers: ["护士", "教师", "社会工作者", "图书管理员", "人力资源"],
        famous: ["特蕾莎修女", "罗莎·帕克斯", "凯特·米德尔顿"],
        color: "bg-cyan-500"
      },
      ESTJ: {
        name: "总经理",
        description: "出色的管理者，在管理事物或人员方面无与伦比",
        strengths: ["组织能力", "领导力", "实用性", "决策力"],
        careers: ["管理者", "银行家", "法官", "军官", "项目经理"],
        famous: ["亨利·福特", "约翰·洛克菲勒", "希拉里·克林顿"],
        color: "bg-amber-500"
      },
      ESFJ: {
        name: "执政官",
        description: "极有同情心、受欢迎、总是热心帮助他人",
        strengths: ["人际关系", "组织能力", "责任心", "服务精神"],
        careers: ["教师", "护士", "销售代表", "人力资源", "社会工作者"],
        famous: ["泰勒·斯威夫特", "惠特尼·休斯顿", "萨拉·佩林"],
        color: "bg-rose-500"
      },
      ISTP: {
        name: "鉴赏家",
        description: "大胆而实际的实验家，擅长使用各种工具",
        strengths: ["实践能力", "适应性", "逻辑思维", "独立性"],
        careers: ["工程师", "机械师", "飞行员", "外科医生", "运动员"],
        famous: ["迈克尔·乔丹", "克林特·伊斯特伍德", "布鲁斯·李"],
        color: "bg-slate-500"
      },
      ISFP: {
        name: "探险家",
        description: "灵活、迷人的艺术家，时刻准备探索新的可能性",
        strengths: ["艺术天赋", "适应性", "同理心", "价值观"],
        careers: ["艺术家", "音乐家", "设计师", "心理学家", "兽医"],
        famous: ["迈克尔·杰克逊", "莫扎特", "鲍勃·迪伦"],
        color: "bg-emerald-500"
      },
      ESTP: {
        name: "企业家",
        description: "聪明、精力充沛、善于感知的企业家，真正享受生活的边缘",
        strengths: ["行动力", "适应性", "实用性", "社交能力"],
        careers: ["销售员", "企业家", "运动员", "演员", "警察"],
        famous: ["唐纳德·特朗普", "欧内斯特·海明威", "温斯顿·丘吉尔"],
        color: "bg-yellow-500"
      },
      ESFP: {
        name: "娱乐家",
        description: "自发的、精力充沛、热情的表演者，生活对他们来说从不无聊",
        strengths: ["热情活力", "人际关系", "适应性", "实用性"],
        careers: ["演员", "音乐家", "销售员", "教师", "社会工作者"],
        famous: ["玛丽莲·梦露", "埃尔顿·约翰", "威尔·史密斯"],
        color: "bg-lime-500"
      }
    }

    return descriptions[type] || {
      name: "未知类型",
      description: "需要更多信息来确定您的性格类型",
      strengths: [],
      careers: [],
      famous: [],
      color: "bg-gray-500"
    }
  }

  const progress = ((currentQuestion + 1) / testQuestions.length) * 100
  const elapsedTime = Math.floor((Date.now() - startTime) / 1000 / 60)

  if (isCompleted) {
    const { type, scores } = calculateMBTIType()
    const description = getMBTIDescription(type)

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50">
        <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center space-x-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>返回测试中心</span>
                </Button>
                <div className="h-6 w-px bg-gray-300"></div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h1 className="text-xl font-bold text-gray-800">测试完成</h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 结果概览 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
              <div className="text-center mb-8">
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full text-white text-2xl font-bold mb-4 ${description.color}`}>
                  {type}
                </div>
                <h2 className="text-3xl font-bold text-gray-800 mb-2">
                  {description.name}
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  {description.description}
                </p>
              </div>

              {/* 测试统计 */}
              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {testQuestions.length}
                  </div>
                  <div className="text-sm text-gray-600">完成题目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {elapsedTime}
                  </div>
                  <div className="text-sm text-gray-600">用时(分钟)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {type}
                  </div>
                  <div className="text-sm text-gray-600">性格类型</div>
                </div>
              </div>

              {/* 维度分析 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <Eye className="w-5 h-5 text-blue-600 mr-2" />
                    <span className="font-semibold">能量来源</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-800">
                    {scores.E > scores.I ? 'E' : 'I'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {scores.E > scores.I ? '外向' : '内向'}
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <Brain className="w-5 h-5 text-green-600 mr-2" />
                    <span className="font-semibold">信息获取</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-800">
                    {scores.S > scores.N ? 'S' : 'N'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {scores.S > scores.N ? '感觉' : '直觉'}
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <Heart className="w-5 h-5 text-red-600 mr-2" />
                    <span className="font-semibold">决策方式</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-800">
                    {scores.T > scores.F ? 'T' : 'F'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {scores.T > scores.F ? '思考' : '情感'}
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    <Zap className="w-5 h-5 text-purple-600 mr-2" />
                    <span className="font-semibold">生活方式</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-800">
                    {scores.J > scores.P ? 'J' : 'P'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {scores.J > scores.P ? '判断' : '感知'}
                  </div>
                </div>
              </div>

              {/* 性格优势 */}
              <div className="mb-8">
                <h3 className="text-xl font-bold text-gray-800 mb-4">您的性格优势</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {description.strengths.map((strength, index) => (
                    <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                      <span className="text-blue-700 font-medium text-sm">{strength}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 适合职业 */}
              <div className="mb-8">
                <h3 className="text-xl font-bold text-gray-800 mb-4">适合的职业</h3>
                <div className="flex flex-wrap gap-2">
                  {description.careers.map((career, index) => (
                    <span key={index} className="px-3 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                      {career}
                    </span>
                  ))}
                </div>
              </div>

              {/* 名人代表 */}
              <div className="p-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
                <h3 className="text-lg font-bold text-gray-800 mb-4">同类型名人</h3>
                <div className="flex flex-wrap gap-2">
                  {description.famous.map((person, index) => (
                    <span key={index} className="px-3 py-2 bg-white text-gray-700 rounded-full text-sm border border-gray-200">
                      {person}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center space-x-4">
              <Button
                onClick={onBack}
                variant="outline"
                className="px-8 py-3"
              >
                返回测试中心
              </Button>
              <Button
                onClick={() => window.print()}
                className="px-8 py-3 bg-purple-600 hover:bg-purple-700"
              >
                保存报告
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回测试中心</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <User className="w-6 h-6 text-purple-600" />
                <h1 className="text-xl font-bold text-gray-800">MBTI职业性格测试完美版</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{elapsedTime} 分钟</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 进度条 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              第 {currentQuestion + 1} 题 / 共 {testQuestions.length} 题
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% 完成
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* 测试内容 */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            {/* 题目 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                {testQuestions[currentQuestion].question}
              </h2>
              
              {/* 选项 */}
              <div className="space-y-4">
                {testQuestions[currentQuestion].options.map((option, index) => (
                  <button
                    key={option.value}
                    onClick={() => handleAnswer(option.value)}
                    className={`w-full p-4 text-left border-2 rounded-lg transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 ${
                      answers[testQuestions[currentQuestion].id] === option.value 
                        ? 'border-purple-500 bg-purple-50 ring-2 ring-purple-200' 
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        answers[testQuestions[currentQuestion].id] === option.value
                          ? 'border-purple-500 bg-purple-500'
                          : 'border-gray-300'
                      }`}>
                        {answers[testQuestions[currentQuestion].id] === option.value && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className="text-gray-800 font-medium">{option.text}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="flex justify-between">
              <Button
                onClick={handlePrevious}
                disabled={currentQuestion === 0}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>上一题</span>
              </Button>
              
              <Button
                onClick={handleNext}
                disabled={currentQuestion === testQuestions.length - 1 || !answers[testQuestions[currentQuestion].id]}
                className="flex items-center space-x-2"
              >
                <span>下一题</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
