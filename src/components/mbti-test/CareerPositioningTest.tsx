import { useState } from "react"
import { ArrowLeft, Brain, ChevronLeft, ChevronRight, Clock, CheckCircle, TrendingUp, Target, Users } from "lucide-react"
import { Button } from "../ui/button"
import { Progress } from "../ui/progress"

interface CareerPositioningTestProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

// 模拟测试题目数据
const testQuestions = [
  {
    id: 1,
    question: "我在团队中通常扮演什么角色？",
    options: [
      { text: "领导者，喜欢指挥和协调", value: "leadership", dimension: "leadership" },
      { text: "执行者，专注完成任务", value: "execution", dimension: "execution" },
      { text: "创意者，提供新想法", value: "creativity", dimension: "creativity" },
      { text: "协调者，促进团队合作", value: "coordination", dimension: "social" }
    ]
  },
  {
    id: 2,
    question: "面对复杂问题时，我倾向于？",
    options: [
      { text: "系统性分析，寻找逻辑关系", value: "analytical", dimension: "analytical" },
      { text: "直觉判断，快速决策", value: "intuitive", dimension: "intuitive" },
      { text: "寻求他人意见和建议", value: "collaborative", dimension: "social" },
      { text: "创新思考，寻找新方法", value: "innovative", dimension: "creativity" }
    ]
  },
  {
    id: 3,
    question: "我最看重工作中的什么？",
    options: [
      { text: "稳定的收入和保障", value: "security", dimension: "security" },
      { text: "个人成长和发展机会", value: "growth", dimension: "growth" },
      { text: "工作的社会意义和价值", value: "meaning", dimension: "meaning" },
      { text: "自由度和灵活性", value: "freedom", dimension: "freedom" }
    ]
  },
  {
    id: 4,
    question: "我的学习方式偏向于？",
    options: [
      { text: "理论学习，深入研究", value: "theoretical", dimension: "analytical" },
      { text: "实践操作，边做边学", value: "practical", dimension: "execution" },
      { text: "讨论交流，集体学习", value: "social", dimension: "social" },
      { text: "独立探索，自主学习", value: "independent", dimension: "independence" }
    ]
  },
  {
    id: 5,
    question: "我在压力下的表现如何？",
    options: [
      { text: "保持冷静，理性分析", value: "calm", dimension: "analytical" },
      { text: "积极应对，寻找解决方案", value: "proactive", dimension: "execution" },
      { text: "寻求支持，团队协作", value: "supportive", dimension: "social" },
      { text: "创新思维，另辟蹊径", value: "creative", dimension: "creativity" }
    ]
  },
  {
    id: 6,
    question: "我理想的工作环境是？",
    options: [
      { text: "安静独立，专注思考", value: "quiet", dimension: "independence" },
      { text: "团队协作，互动频繁", value: "collaborative", dimension: "social" },
      { text: "充满挑战，变化多样", value: "dynamic", dimension: "growth" },
      { text: "稳定有序，流程清晰", value: "structured", dimension: "security" }
    ]
  },
  {
    id: 7,
    question: "我最擅长的能力是？",
    options: [
      { text: "逻辑分析和推理", value: "logic", dimension: "analytical" },
      { text: "人际沟通和协调", value: "communication", dimension: "social" },
      { text: "创新设计和构思", value: "design", dimension: "creativity" },
      { text: "执行落地和实施", value: "implementation", dimension: "execution" }
    ]
  },
  {
    id: 8,
    question: "我对未来职业发展的期望是？",
    options: [
      { text: "成为某个领域的专家", value: "expert", dimension: "analytical" },
      { text: "担任管理或领导职位", value: "manager", dimension: "leadership" },
      { text: "从事创意或创新工作", value: "creative", dimension: "creativity" },
      { text: "帮助他人成长和发展", value: "helper", dimension: "social" }
    ]
  },
  {
    id: 9,
    question: "我在做决策时主要考虑？",
    options: [
      { text: "数据和事实依据", value: "data", dimension: "analytical" },
      { text: "对他人的影响", value: "impact", dimension: "social" },
      { text: "创新和突破性", value: "innovation", dimension: "creativity" },
      { text: "可行性和效率", value: "efficiency", dimension: "execution" }
    ]
  },
  {
    id: 10,
    question: "我最喜欢的工作内容是？",
    options: [
      { text: "研究分析，深入思考", value: "research", dimension: "analytical" },
      { text: "项目管理，统筹协调", value: "management", dimension: "leadership" },
      { text: "创意设计，艺术创作", value: "creation", dimension: "creativity" },
      { text: "服务他人，解决问题", value: "service", dimension: "social" }
    ]
  }
]

export function CareerPositioningTest({ onBack, onNavigate }: CareerPositioningTestProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<number, string>>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [startTime] = useState(Date.now())

  const handleAnswer = (value: string) => {
    const newAnswers = { ...answers, [testQuestions[currentQuestion].id]: value }
    setAnswers(newAnswers)

    if (currentQuestion < testQuestions.length - 1) {
      setTimeout(() => {
        setCurrentQuestion(currentQuestion + 1)
      }, 300)
    } else {
      setTimeout(() => {
        setIsCompleted(true)
      }, 300)
    }
  }

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestion < testQuestions.length - 1 && answers[testQuestions[currentQuestion].id]) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const calculateResults = () => {
    const dimensions = {
      analytical: 0,
      creativity: 0,
      social: 0,
      leadership: 0,
      execution: 0,
      independence: 0,
      security: 0,
      growth: 0,
      meaning: 0,
      freedom: 0,
      intuitive: 0
    }

    testQuestions.forEach(question => {
      const answerId = answers[question.id]
      if (answerId) {
        const selectedOption = question.options.find(opt => opt.value === answerId)
        if (selectedOption) {
          dimensions[selectedOption.dimension as keyof typeof dimensions]++
        }
      }
    })

    return dimensions
  }

  const getCareerRecommendations = (dimensions: Record<string, number>) => {
    const sortedDimensions = Object.entries(dimensions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)

    const careerMap = {
      analytical: {
        name: "分析研究型",
        careers: ["数据分析师", "研究员", "咨询顾问", "系统分析师"],
        description: "适合需要深度思考和逻辑分析的工作"
      },
      creativity: {
        name: "创新创意型", 
        careers: ["设计师", "广告策划", "产品经理", "艺术家"],
        description: "适合需要创新思维和艺术感的工作"
      },
      social: {
        name: "人际服务型",
        careers: ["人力资源", "教师", "心理咨询师", "社工"],
        description: "适合与人打交道和帮助他人的工作"
      },
      leadership: {
        name: "管理领导型",
        careers: ["项目经理", "团队主管", "企业高管", "创业者"],
        description: "适合领导团队和管理组织的工作"
      },
      execution: {
        name: "执行实施型",
        careers: ["工程师", "运营专员", "项目执行", "技术专家"],
        description: "适合注重实际操作和结果导向的工作"
      }
    }

    return sortedDimensions.map(([dim, score]) => ({
      dimension: dim,
      score,
      ...careerMap[dim as keyof typeof careerMap] || { name: dim, careers: [], description: "" }
    }))
  }

  const progress = ((currentQuestion + 1) / testQuestions.length) * 100
  const elapsedTime = Math.floor((Date.now() - startTime) / 1000 / 60)

  if (isCompleted) {
    const results = calculateResults()
    const recommendations = getCareerRecommendations(results)

    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
        <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center space-x-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>返回测试中心</span>
                </Button>
                <div className="h-6 w-px bg-gray-300"></div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h1 className="text-xl font-bold text-gray-800">测试完成</h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 结果概览 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  职业定位测试结果
                </h2>
                <p className="text-lg text-gray-600">
                  基于您的能力、兴趣和价值观分析，为您推荐最适合的职业方向
                </p>
              </div>

              {/* 测试统计 */}
              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {testQuestions.length}
                  </div>
                  <div className="text-sm text-gray-600">完成题目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {elapsedTime}
                  </div>
                  <div className="text-sm text-gray-600">用时(分钟)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {recommendations[0].name}
                  </div>
                  <div className="text-sm text-gray-600">主要类型</div>
                </div>
              </div>

              {/* 职业类型分析 */}
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">您的职业类型匹配度</h3>
                {recommendations.map((item, index) => (
                  <div key={item.dimension} className="p-6 bg-gray-50 rounded-lg">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${
                          index === 0 ? 'bg-green-500' : index === 1 ? 'bg-blue-500' : 'bg-purple-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-800">
                            {item.name}
                          </h4>
                          <p className="text-gray-600 text-sm">{item.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-800">{item.score}</div>
                        <div className="text-sm text-gray-500">匹配度</div>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div 
                          className={`h-3 rounded-full ${
                            index === 0 ? 'bg-green-500' : index === 1 ? 'bg-blue-500' : 'bg-purple-500'
                          }`}
                          style={{ width: `${(item.score / Math.max(...recommendations.map(r => r.score))) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium text-gray-800 mb-2">推荐职业：</h5>
                      <div className="flex flex-wrap gap-2">
                        {item.careers.map(career => (
                          <span key={career} className="px-3 py-1 bg-white text-gray-700 text-sm rounded-full border border-gray-200">
                            {career}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 发展建议 */}
              <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                  职业发展建议
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                      <Target className="w-4 h-4 mr-2 text-blue-600" />
                      短期目标
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 深入了解推荐的职业领域</li>
                      <li>• 参加相关的实习或项目</li>
                      <li>• 培养核心技能和能力</li>
                    </ul>
                  </div>
                  <div className="bg-white p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                      <Users className="w-4 h-4 mr-2 text-purple-600" />
                      长期规划
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• 建立专业网络和人脉</li>
                      <li>• 持续学习和技能提升</li>
                      <li>• 寻找导师和职业指导</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center space-x-4">
              <Button
                onClick={onBack}
                variant="outline"
                className="px-8 py-3"
              >
                返回测试中心
              </Button>
              <Button
                onClick={() => window.print()}
                className="px-8 py-3 bg-green-600 hover:bg-green-700"
              >
                保存报告
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回测试中心</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <Brain className="w-6 h-6 text-green-600" />
                <h1 className="text-xl font-bold text-gray-800">职业定位测试</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{elapsedTime} 分钟</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 进度条 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              第 {currentQuestion + 1} 题 / 共 {testQuestions.length} 题
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% 完成
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* 测试内容 */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            {/* 题目 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                {testQuestions[currentQuestion].question}
              </h2>
              
              {/* 选项 */}
              <div className="space-y-3">
                {testQuestions[currentQuestion].options.map((option, index) => (
                  <button
                    key={option.value}
                    onClick={() => handleAnswer(option.value)}
                    className={`w-full p-4 text-left border-2 rounded-lg transition-all duration-200 hover:border-green-300 hover:bg-green-50 ${
                      answers[testQuestions[currentQuestion].id] === option.value 
                        ? 'border-green-500 bg-green-50 ring-2 ring-green-200' 
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        answers[testQuestions[currentQuestion].id] === option.value
                          ? 'border-green-500 bg-green-500'
                          : 'border-gray-300'
                      }`}>
                        {answers[testQuestions[currentQuestion].id] === option.value && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                      <span className="text-gray-800 font-medium">{option.text}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="flex justify-between">
              <Button
                onClick={handlePrevious}
                disabled={currentQuestion === 0}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>上一题</span>
              </Button>
              
              <Button
                onClick={handleNext}
                disabled={currentQuestion === testQuestions.length - 1 || !answers[testQuestions[currentQuestion].id]}
                className="flex items-center space-x-2"
              >
                <span>下一题</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
