import { useState } from "react"
import { ArrowLeft, User, Brain, Target, Heart, ChevronRight, Clock, Users, Star } from "lucide-react"
import { Button } from "../ui/button"

interface MBTITestPageProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

export function MBTITestPage({ onBack, onNavigate }: MBTITestPageProps) {
  const [selectedTest, setSelectedTest] = useState<string | null>(null)

  const testTypes = [
    {
      id: "holland-career-orientation",
      title: "霍兰德高考志愿倾向测试",
      description: "基于霍兰德职业兴趣理论，帮助高考生发现最适合的专业方向",
      icon: Target,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      duration: "15-20分钟",
      questions: 60,
      popularity: "95%",
      features: ["专业匹配", "兴趣分析", "职业规划"]
    },
    {
      id: "career-positioning",
      title: "职业定位测试",
      description: "全面评估个人能力、兴趣和价值观，精准定位职业发展方向",
      icon: Brain,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      duration: "10-15分钟",
      questions: 45,
      popularity: "88%",
      features: ["能力评估", "价值观分析", "发展建议"]
    },
    {
      id: "holland-interest",
      title: "霍兰德职业兴趣测试",
      description: "经典的职业兴趣测评工具，识别六大职业兴趣类型",
      icon: Heart,
      color: "from-red-500 to-red-600",
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      duration: "12-18分钟",
      questions: 54,
      popularity: "92%",
      features: ["兴趣类型", "职业匹配", "发展路径"]
    },
    {
      id: "mbti-personality",
      title: "MBTI职业性格测试完美版",
      description: "权威的性格类型测试，深度解析16种人格类型特征",
      icon: User,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      duration: "20-25分钟",
      questions: 72,
      popularity: "97%",
      features: ["性格分析", "职业适配", "团队角色"]
    }
  ]

  const handleTestClick = (testId: string) => {
    setSelectedTest(testId)
    if (onNavigate) {
      onNavigate(testId)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回首页</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <User className="w-6 h-6 text-purple-600" />
                <h1 className="text-xl font-bold text-gray-800">MBTI测试中心</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题和描述 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            专业心理测评中心
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            科学的心理测评工具，帮助您深入了解自己的性格特征、职业兴趣和发展潜力
          </p>
        </div>

        {/* 测试类型网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {testTypes.map((test) => (
            <div
              key={test.id}
              className={`${test.bgColor} ${test.borderColor} border-2 rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-105 group`}
              onClick={() => handleTestClick(test.id)}
            >
              {/* 测试头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 bg-gradient-to-r ${test.color} rounded-xl`}>
                  <test.icon className="w-8 h-8 text-white" />
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <Users className="w-4 h-4" />
                    <span>{test.popularity}</span>
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                </div>
              </div>
              
              {/* 测试标题和描述 */}
              <h3 className="text-xl font-bold text-gray-800 mb-3">
                {test.title}
              </h3>
              
              <p className="text-gray-600 mb-4 leading-relaxed">
                {test.description}
              </p>
              
              {/* 测试信息 */}
              <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{test.duration}</span>
                </div>
                <span>{test.questions} 道题目</span>
              </div>

              {/* 特色功能标签 */}
              <div className="flex flex-wrap gap-2">
                {test.features.map((feature, idx) => (
                  <span
                    key={idx}
                    className="px-3 py-1 bg-white/80 text-gray-700 text-xs font-medium rounded-full border border-gray-200"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* 测试说明 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-6 text-center">
            测试说明
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-sm font-bold">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1">选择合适的测试</h4>
                  <p className="text-gray-600 text-sm">根据您的需求选择最适合的测试类型</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 text-sm font-bold">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1">认真作答</h4>
                  <p className="text-gray-600 text-sm">请根据真实情况作答，避免过度思考</p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-600 text-sm font-bold">3</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1">获取报告</h4>
                  <p className="text-gray-600 text-sm">完成测试后即可获得详细的分析报告</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-orange-600 text-sm font-bold">4</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1">应用指导</h4>
                  <p className="text-gray-600 text-sm">结合报告进行专业选择和职业规划</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl shadow-xl p-8 text-white">
          <h3 className="text-xl font-bold mb-6 text-center">
            测试统计
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">50万+</div>
              <div className="text-sm opacity-90">累计测试人数</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">96.8%</div>
              <div className="text-sm opacity-90">准确率</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">4.9</div>
              <div className="text-sm opacity-90">用户评分</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-sm opacity-90">在线服务</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
