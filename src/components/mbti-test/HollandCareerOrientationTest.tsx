import { useState } from "react"
import { ArrowLeft, Target, ChevronLeft, ChevronRight, Clock, CheckCircle } from "lucide-react"
import { Button } from "../ui/button"
import { Progress } from "../ui/progress"

interface HollandCareerOrientationTestProps {
  onBack: () => void
  onNavigate?: (page: string) => void
}

// 模拟测试题目数据
const testQuestions = [
  {
    id: 1,
    question: "我喜欢动手制作或修理东西",
    category: "R" // Realistic 实用型
  },
  {
    id: 2,
    question: "我对科学研究和实验很感兴趣",
    category: "I" // Investigative 研究型
  },
  {
    id: 3,
    question: "我喜欢参与艺术创作活动",
    category: "A" // Artistic 艺术型
  },
  {
    id: 4,
    question: "我愿意帮助别人解决问题",
    category: "S" // Social 社会型
  },
  {
    id: 5,
    question: "我喜欢领导和管理他人",
    category: "E" // Enterprising 企业型
  },
  {
    id: 6,
    question: "我喜欢按照既定程序完成工作",
    category: "C" // Conventional 常规型
  },
  {
    id: 7,
    question: "我喜欢户外活动和体力劳动",
    category: "R"
  },
  {
    id: 8,
    question: "我对数学和逻辑推理很擅长",
    category: "I"
  },
  {
    id: 9,
    question: "我经常有创新的想法",
    category: "A"
  },
  {
    id: 10,
    question: "我喜欢与人交流和合作",
    category: "S"
  },
  {
    id: 11,
    question: "我善于说服别人接受我的观点",
    category: "E"
  },
  {
    id: 12,
    question: "我喜欢有条理地整理信息",
    category: "C"
  },
  {
    id: 13,
    question: "我喜欢使用工具和机械设备",
    category: "R"
  },
  {
    id: 14,
    question: "我对探索未知事物充满好奇",
    category: "I"
  },
  {
    id: 15,
    question: "我喜欢音乐、绘画或写作",
    category: "A"
  },
  {
    id: 16,
    question: "我愿意为他人提供服务",
    category: "S"
  },
  {
    id: 17,
    question: "我喜欢制定计划和目标",
    category: "E"
  },
  {
    id: 18,
    question: "我喜欢按部就班地完成任务",
    category: "C"
  },
  {
    id: 19,
    question: "我对技术和工程感兴趣",
    category: "R"
  },
  {
    id: 20,
    question: "我喜欢分析和解决复杂问题",
    category: "I"
  }
]

export function HollandCareerOrientationTest({ onBack, onNavigate }: HollandCareerOrientationTestProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<number, number>>({})
  const [isCompleted, setIsCompleted] = useState(false)
  const [startTime] = useState(Date.now())

  const handleAnswer = (score: number) => {
    const newAnswers = { ...answers, [testQuestions[currentQuestion].id]: score }
    setAnswers(newAnswers)

    if (currentQuestion < testQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      setIsCompleted(true)
    }
  }

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const handleNext = () => {
    if (currentQuestion < testQuestions.length - 1 && answers[testQuestions[currentQuestion].id] !== undefined) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const calculateResults = () => {
    const scores = { R: 0, I: 0, A: 0, S: 0, E: 0, C: 0 }
    
    testQuestions.forEach(question => {
      const answer = answers[question.id]
      if (answer !== undefined) {
        scores[question.category as keyof typeof scores] += answer
      }
    })

    return scores
  }

  const getResultAnalysis = (scores: Record<string, number>) => {
    const sortedTypes = Object.entries(scores)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)

    const typeDescriptions = {
      R: { name: "实用型", description: "喜欢动手操作，适合工程、技术类专业" },
      I: { name: "研究型", description: "喜欢思考分析，适合科学、医学类专业" },
      A: { name: "艺术型", description: "富有创造力，适合艺术、设计类专业" },
      S: { name: "社会型", description: "关心他人，适合教育、心理类专业" },
      E: { name: "企业型", description: "善于领导，适合管理、商务类专业" },
      C: { name: "常规型", description: "注重秩序，适合会计、行政类专业" }
    }

    return sortedTypes.map(([type, score]) => ({
      type,
      score,
      ...typeDescriptions[type as keyof typeof typeDescriptions]
    }))
  }

  const progress = ((currentQuestion + 1) / testQuestions.length) * 100
  const elapsedTime = Math.floor((Date.now() - startTime) / 1000 / 60)

  if (isCompleted) {
    const results = calculateResults()
    const analysis = getResultAnalysis(results)

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
        <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={onBack}
                  className="flex items-center space-x-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>返回测试中心</span>
                </Button>
                <div className="h-6 w-px bg-gray-300"></div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h1 className="text-xl font-bold text-gray-800">测试完成</h1>
                </div>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 结果概览 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  霍兰德职业兴趣测试结果
                </h2>
                <p className="text-lg text-gray-600">
                  基于您的回答，我们为您分析了职业兴趣倾向
                </p>
              </div>

              {/* 测试统计 */}
              <div className="grid grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {testQuestions.length}
                  </div>
                  <div className="text-sm text-gray-600">完成题目</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {elapsedTime}
                  </div>
                  <div className="text-sm text-gray-600">用时(分钟)</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {analysis[0].name}
                  </div>
                  <div className="text-sm text-gray-600">主要类型</div>
                </div>
              </div>

              {/* 兴趣类型分析 */}
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">您的兴趣类型排序</h3>
                {analysis.map((item, index) => (
                  <div key={item.type} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${
                        index === 0 ? 'bg-blue-500' : index === 1 ? 'bg-green-500' : 'bg-orange-500'
                      }`}>
                        {index + 1}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-lg font-semibold text-gray-800">
                          {item.name} ({item.type})
                        </h4>
                        <span className="text-sm font-medium text-gray-600">
                          {item.score} 分
                        </span>
                      </div>
                      <p className="text-gray-600">{item.description}</p>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              index === 0 ? 'bg-blue-500' : index === 1 ? 'bg-green-500' : 'bg-orange-500'
                            }`}
                            style={{ width: `${(item.score / Math.max(...Object.values(results))) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 专业推荐 */}
              <div className="mt-8 p-6 bg-blue-50 rounded-lg">
                <h3 className="text-lg font-bold text-gray-800 mb-4">推荐专业方向</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {analysis.slice(0, 2).map((item, index) => (
                    <div key={item.type} className="bg-white p-4 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-gray-800 mb-2">{item.name}相关专业</h4>
                      <div className="flex flex-wrap gap-2">
                        {item.type === 'R' && ['机械工程', '土木工程', '电气工程', '计算机科学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">{major}</span>
                        ))}
                        {item.type === 'I' && ['生物科学', '化学', '物理学', '医学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">{major}</span>
                        ))}
                        {item.type === 'A' && ['艺术设计', '音乐学', '广告学', '建筑学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">{major}</span>
                        ))}
                        {item.type === 'S' && ['教育学', '心理学', '社会工作', '护理学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">{major}</span>
                        ))}
                        {item.type === 'E' && ['工商管理', '市场营销', '国际贸易', '法学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">{major}</span>
                        ))}
                        {item.type === 'C' && ['会计学', '统计学', '图书馆学', '档案学'].map(major => (
                          <span key={major} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full">{major}</span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center space-x-4">
              <Button
                onClick={onBack}
                variant="outline"
                className="px-8 py-3"
              >
                返回测试中心
              </Button>
              <Button
                onClick={() => window.print()}
                className="px-8 py-3 bg-blue-600 hover:bg-blue-700"
              >
                保存报告
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回测试中心</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <Target className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-800">霍兰德高考志愿倾向测试</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{elapsedTime} 分钟</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 进度条 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              第 {currentQuestion + 1} 题 / 共 {testQuestions.length} 题
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% 完成
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* 测试内容 */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
            {/* 题目 */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                {testQuestions[currentQuestion].question}
              </h2>
              
              {/* 选项 */}
              <div className="space-y-3">
                {[
                  { value: 5, label: "非常同意", color: "bg-green-500 hover:bg-green-600" },
                  { value: 4, label: "比较同意", color: "bg-blue-500 hover:bg-blue-600" },
                  { value: 3, label: "一般", color: "bg-gray-500 hover:bg-gray-600" },
                  { value: 2, label: "比较不同意", color: "bg-orange-500 hover:bg-orange-600" },
                  { value: 1, label: "非常不同意", color: "bg-red-500 hover:bg-red-600" }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => handleAnswer(option.value)}
                    className={`w-full p-4 text-white font-medium rounded-lg transition-all duration-200 ${option.color} ${
                      answers[testQuestions[currentQuestion].id] === option.value 
                        ? 'ring-4 ring-blue-300 scale-105' 
                        : 'hover:scale-102'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 导航按钮 */}
            <div className="flex justify-between">
              <Button
                onClick={handlePrevious}
                disabled={currentQuestion === 0}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>上一题</span>
              </Button>
              
              <Button
                onClick={handleNext}
                disabled={currentQuestion === testQuestions.length - 1 || answers[testQuestions[currentQuestion].id] === undefined}
                className="flex items-center space-x-2"
              >
                <span>下一题</span>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
