import type {LucideIcon} from "lucide-react"
import { cn } from "../lib/utils"
import { AnimatedShinyText } from "./magicui/animated-shiny-text"

interface FeatureCardProps {
  title: string
  icon: LucideIcon
  color: string
  onClick?: () => void
  className?: string
  size?: 'small' | 'medium' | 'large' | 'wide' | 'tall'
}

export function FeatureCard({
  title,
  icon: Icon,
  color,
  onClick,
  className,
  size = 'medium'
}: FeatureCardProps) {

  // 根据尺寸定义不同的样式
  const sizeClasses = {
    small: "min-h-[100px] text-sm",
    medium: "min-h-[120px] text-base",
    large: "min-h-[140px] text-lg",
    wide: "min-h-[120px] text-base",
    tall: "min-h-[140px] text-lg"
  }

  const iconSizes = {
    small: "w-6 h-6",
    medium: "w-8 h-8",
    large: "w-10 h-10",
    wide: "w-9 h-9",
    tall: "w-10 h-10"
  }

  const paddingClasses = {
    small: "p-4",
    medium: "p-6",
    large: "p-8",
    wide: "p-6",
    tall: "p-8"
  }
  return (
    <div
      onClick={onClick}
      className={cn(
        "group relative overflow-hidden rounded-3xl cursor-pointer transition-all duration-500",
        "hover:scale-105 hover:shadow-2xl active:scale-95",
        "flex flex-col items-center justify-center text-white font-medium",
        "before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/20 before:to-transparent before:opacity-0 before:transition-opacity before:duration-500",
        "after:absolute after:inset-0 after:bg-gradient-to-t after:from-black/10 after:to-transparent after:opacity-0 after:transition-opacity after:duration-500",
        "hover:before:opacity-100 hover:after:opacity-100",
        "border border-white/20 backdrop-blur-sm",
        sizeClasses[size],
        paddingClasses[size],
        color,
        className
      )}
    >
      {/* 动态背景粒子效果 */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-white rounded-full animate-pulse delay-300"></div>
        <div className="absolute bottom-1/4 left-3/4 w-1.5 h-1.5 bg-white rounded-full animate-pulse delay-700"></div>
      </div>

      {/* 背景装饰图标 */}
      <div className="absolute top-3 right-3 opacity-10 group-hover:opacity-20 transition-all duration-500 group-hover:rotate-12">
        <Icon className={cn("opacity-50", iconSizes[size])} />
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 flex flex-col items-center">
        <div className="relative mb-3">
          <Icon className={cn("relative z-10 group-hover:scale-110 transition-transform duration-500 drop-shadow-lg", iconSizes[size])} />
          <div className="absolute inset-0 bg-white/20 rounded-full blur-xl scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        </div>
        <AnimatedShinyText className="text-center leading-tight relative z-10 group-hover:font-semibold transition-all duration-500 text-white">
          {title}
        </AnimatedShinyText>
      </div>

      {/* 底部装饰线 */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/40 via-white/60 to-white/40 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center" />

      {/* 边框光效 */}
      <div className="absolute inset-0 rounded-3xl border border-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
    </div>
  )
}
