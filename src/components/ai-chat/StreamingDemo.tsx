import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Sparkles, Play, Pause, RotateCcw } from 'lucide-react'

interface StreamingDemoProps {
  onClose: () => void
}

export function StreamingDemo({ onClose }: StreamingDemoProps) {
  const [displayText, setDisplayText] = useState('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)

  // 模拟的流式响应文本
  const demoText = `选择适合自己的专业是一个重要的决定，需要综合考虑多个因素：

🎯 **兴趣与爱好**
首先要了解自己的兴趣所在。选择自己感兴趣的专业，学习过程会更有动力，未来工作也会更有热情。

📊 **个人能力与特长**
评估自己的学科优势，比如数学、语言、逻辑思维等方面的能力，选择能发挥自己优势的专业。

💼 **就业前景**
了解专业的就业率、薪资水平、行业发展趋势等，选择有良好发展前景的专业。

🏫 **院校实力**
同一专业在不同院校的教学质量、师资力量、就业资源都有差异，要综合考虑。

🌟 **建议步骤**：
1. 做职业兴趣测试，了解自己的性格特点
2. 咨询老师、家长和已就业的学长学姐
3. 参加高校开放日，实地了解专业情况
4. 关注国家政策导向和行业发展趋势

记住，没有绝对的"好专业"，只有适合自己的专业。选择时要理性分析，不要盲目跟风。`

  useEffect(() => {
    let timer: NodeJS.Timeout

    if (isPlaying && currentIndex < demoText.length) {
      timer = setTimeout(() => {
        setDisplayText(demoText.substring(0, currentIndex + 1))
        setCurrentIndex(prev => prev + 1)
      }, 50) // 调整速度
    } else if (currentIndex >= demoText.length) {
      setIsPlaying(false)
    }

    return () => clearTimeout(timer)
  }, [isPlaying, currentIndex, demoText])

  const handlePlay = () => {
    if (currentIndex >= demoText.length) {
      // 重新开始
      setCurrentIndex(0)
      setDisplayText('')
    }
    setIsPlaying(true)
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const handleReset = () => {
    setIsPlaying(false)
    setCurrentIndex(0)
    setDisplayText('')
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Sparkles className="w-6 h-6" />
              <div>
                <h2 className="text-xl font-bold">流式响应演示</h2>
                <p className="text-indigo-100 text-sm">体验AI实时生成回答的过程</p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={onClose}
              className="text-white border-white/30 hover:bg-white/10"
            >
              关闭
            </Button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {/* 控制按钮 */}
          <div className="flex items-center space-x-3 mb-6">
            <Button
              onClick={handlePlay}
              disabled={isPlaying}
              className="bg-green-500 hover:bg-green-600 text-white"
            >
              <Play className="w-4 h-4 mr-2" />
              {currentIndex >= demoText.length ? '重新播放' : '开始'}
            </Button>
            
            <Button
              onClick={handlePause}
              disabled={!isPlaying}
              variant="outline"
            >
              <Pause className="w-4 h-4 mr-2" />
              暂停
            </Button>
            
            <Button
              onClick={handleReset}
              variant="outline"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              重置
            </Button>

            <div className="flex-1" />
            
            <div className="text-sm text-gray-500">
              进度: {currentIndex}/{demoText.length} 字符
            </div>
          </div>

          {/* 模拟聊天界面 */}
          <div className="bg-gray-50 rounded-xl p-4 min-h-[400px] max-h-[400px] overflow-y-auto">
            {/* 用户消息 */}
            <div className="flex justify-end mb-4">
              <div className="bg-blue-500 text-white rounded-2xl px-4 py-2 max-w-[70%]">
                <p className="text-sm">如何选择适合自己的专业？</p>
              </div>
            </div>

            {/* AI回复 */}
            <div className="flex justify-start">
              <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 max-w-[80%] shadow-sm">
                <div className="text-sm leading-relaxed whitespace-pre-wrap">
                  {displayText}
                  {/* 流式光标 */}
                  {isPlaying && (
                    <span className="inline-block w-2 h-4 bg-indigo-500 ml-1 animate-pulse" />
                  )}
                </div>
                {isPlaying && (
                  <div className="flex items-center mt-2 text-xs text-indigo-500">
                    <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                    正在生成回答...
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 说明文字 */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">💡 流式响应的优势</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>实时反馈</strong>：用户可以立即看到AI开始生成回答</li>
              <li>• <strong>更好体验</strong>：避免长时间等待，减少用户焦虑</li>
              <li>• <strong>可中断性</strong>：用户可以在生成过程中停止或重新提问</li>
              <li>• <strong>透明度</strong>：用户能看到AI的"思考"过程</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
