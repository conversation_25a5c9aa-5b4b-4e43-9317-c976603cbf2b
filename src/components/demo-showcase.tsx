import { useState } from "react"
import { <PERSON><PERSON> } from "./ui/button"
import { Globe } from "./magicui/globe"
import { SimpleGlobe } from "./magicui/simple-globe"
import { Marquee } from "./magicui/marquee"
import { Shi<PERSON>Button } from "./magicui/shimmer-button"
import { AnimatedShinyText } from "./magicui/animated-shiny-text"
import { DotPattern } from "./magicui/dot-pattern"
import { NumberTicker } from "./magicui/number-ticker"
import { ErrorBoundary } from "./error-boundary"
import {
  Rocket,
  Zap,
  Palette,
  Globe2,
  Sparkles,
  Code2,
  Layers3,
  Waves,
  RotateCcw,
  Plus,
  Minus,
  Trash2,
  Ghost,
  PartyPopper,
  FileCode,
  RefreshCw
} from "lucide-react"

interface DemoShowcaseProps {
  count: number
  onIncrement: () => void
  onDecrement: () => void
  onReset: () => void
}

export function DemoShowcase({ count, onIncrement, onDecrement, onReset }: DemoShowcaseProps) {
  const [useSimpleGlobe, setUseSimpleGlobe] = useState(true)
  return (
    <div className="relative min-h-svh bg-background text-foreground overflow-hidden">
      <DotPattern
        className="absolute inset-0 opacity-30"
        width={20}
        height={20}
        cx={1}
        cy={1}
        cr={1}
      />
      
      <div className="relative container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-svh gap-16">
          
          {/* Header Section */}
          <div className="text-center space-y-8">
            <div className="flex items-center justify-center gap-4">
              <Rocket className="w-16 h-16 text-blue-500 animate-pulse" />
              <AnimatedShinyText className="text-6xl font-bold tracking-tight">
                Modern Web Stack
              </AnimatedShinyText>
              <Sparkles className="w-16 h-16 text-yellow-500 animate-bounce" />
            </div>
            <p className="text-muted-foreground text-xl max-w-3xl">
              Vite + React + TypeScript + shadcn/ui + Magic UI 完美集成演示
            </p>
            
            {/* Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
              <div className="text-center p-6 rounded-lg bg-card border hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-4">
                  <Plus className="w-8 h-8 text-primary" />
                </div>
                <NumberTicker value={count} className="text-5xl font-bold text-primary" />
                <p className="text-muted-foreground mt-2">当前计数</p>
              </div>
              <div className="text-center p-6 rounded-lg bg-card border hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-4">
                  <Layers3 className="w-8 h-8 text-green-500" />
                </div>
                <NumberTicker value={50} className="text-5xl font-bold text-green-500" />
                <p className="text-muted-foreground mt-2">Magic UI 组件</p>
              </div>
              <div className="text-center p-6 rounded-lg bg-card border hover:shadow-lg transition-shadow">
                <div className="flex items-center justify-center mb-4">
                  <Code2 className="w-8 h-8 text-blue-500" />
                </div>
                <NumberTicker value={2024} className="text-5xl font-bold text-blue-500" />
                <p className="text-muted-foreground mt-2">现代化年份</p>
              </div>
            </div>
          </div>

          {/* Interactive Globe */}
          <div className="relative w-full max-w-lg h-96">
            <ErrorBoundary
              fallback={({ resetError }) => (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-muted-foreground mb-4">地球组件加载失败</p>
                  <Button onClick={resetError} variant="outline" size="sm">
                    重试
                  </Button>
                </div>
              )}
            >
              {useSimpleGlobe ? (
                <SimpleGlobe className="top-0" />
              ) : (
                <Globe className="top-0" />
              )}
            </ErrorBoundary>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
              <div className="flex flex-col items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setUseSimpleGlobe(!useSimpleGlobe)}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  切换到 {useSimpleGlobe ? 'COBE' : '简化版'} 地球
                </Button>
                <p className="text-xs text-muted-foreground text-center flex items-center gap-1">
                  <Globe2 className="w-4 h-4" />
                  {useSimpleGlobe ? '简化版地球动画' : '拖拽地球进行交互'}
                </p>
              </div>
            </div>
          </div>

          {/* Buttons Showcase */}
          <div className="w-full max-w-4xl space-y-12">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-8">按钮组件展示</h2>
              
              {/* shadcn/ui Buttons */}
              <div className="mb-8">
                <h3 className="text-xl font-semibold mb-4 text-muted-foreground flex items-center justify-center gap-2">
                  <Layers3 className="w-5 h-5" />
                  shadcn/ui 按钮
                </h3>
                <div className="flex gap-4 flex-wrap justify-center">
                  <Button onClick={onIncrement} size="lg" className="flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    增加 ({count})
                  </Button>
                  <Button variant="outline" onClick={onReset} size="lg" className="flex items-center gap-2">
                    <RotateCcw className="w-4 h-4" />
                    重置
                  </Button>
                  <Button variant="secondary" onClick={onDecrement} size="lg" className="flex items-center gap-2">
                    <Minus className="w-4 h-4" />
                    减少
                  </Button>
                  <Button variant="destructive" size="lg" className="flex items-center gap-2">
                    <Trash2 className="w-4 h-4" />
                    危险操作
                  </Button>
                  <Button variant="ghost" size="lg" className="flex items-center gap-2">
                    <Ghost className="w-4 h-4" />
                    幽灵按钮
                  </Button>
                </div>
              </div>
              
              {/* Magic UI Buttons */}
              <div>
                <h3 className="text-xl font-semibold mb-4 text-muted-foreground flex items-center justify-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Magic UI 动画按钮
                </h3>
                <div className="flex gap-4 flex-wrap justify-center">
                  <ShimmerButton className="shadow-2xl">
                    <span className="whitespace-pre-wrap text-center text-sm font-medium leading-none tracking-tight text-white lg:text-lg flex items-center gap-2">
                      <Sparkles className="w-4 h-4" />
                      Shimmer Effect
                    </span>
                  </ShimmerButton>
                </div>
              </div>
            </div>
          </div>

          {/* Marquee Section */}
          <div className="w-full">
            <h2 className="text-3xl font-bold text-center mb-8 flex items-center justify-center gap-3">
              <Layers3 className="w-8 h-8 text-primary" />
              技术栈展示
            </h2>
            <Marquee pauseOnHover className="[--duration:30s]">
              {[
                { name: "React", icon: <Rocket className="w-8 h-8" />, color: "from-blue-500 to-cyan-500" },
                { name: "Vite", icon: <Zap className="w-8 h-8" />, color: "from-purple-500 to-pink-500" },
                { name: "TypeScript", icon: <Code2 className="w-8 h-8" />, color: "from-blue-600 to-blue-400" },
                { name: "Tailwind CSS", icon: <Palette className="w-8 h-8" />, color: "from-teal-500 to-green-500" },
                { name: "shadcn/ui", icon: <Layers3 className="w-8 h-8" />, color: "from-gray-700 to-gray-500" },
                { name: "Magic UI", icon: <Sparkles className="w-8 h-8" />, color: "from-yellow-500 to-orange-500" },
                { name: "Motion", icon: <Waves className="w-8 h-8" />, color: "from-indigo-500 to-purple-500" },
                { name: "Lucide", icon: <Globe2 className="w-8 h-8" />, color: "from-emerald-500 to-teal-500" }
              ].map((tech, idx) => (
                <div
                  key={idx}
                  className={`flex flex-col items-center justify-center w-48 h-32 bg-gradient-to-br ${tech.color} rounded-xl text-white font-semibold shadow-lg hover:scale-105 transition-transform cursor-pointer`}
                >
                  <div className="mb-2 text-white">{tech.icon}</div>
                  <div className="text-lg">{tech.name}</div>
                </div>
              ))}
            </Marquee>
          </div>

          {/* Footer */}
          <div className="text-center max-w-2xl">
            <p className="text-muted-foreground text-lg flex items-center justify-center gap-2">
              <PartyPopper className="w-6 h-6 text-yellow-500" />
              恭喜！你已经成功集成了现代化的 Web 开发技术栈
            </p>
            <p className="text-sm text-muted-foreground mt-4 flex items-center justify-center gap-2">
              <FileCode className="w-4 h-4" />
              编辑 <code className="bg-muted px-2 py-1 rounded">src/App.tsx</code> 开始你的项目开发之旅
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
