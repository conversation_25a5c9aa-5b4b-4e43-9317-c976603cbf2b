import { useState } from 'react'
import { Card } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { QSUniversityCard } from './QSUniversityCard'
import type { ProcessedQSUniversity } from '../../types/qs-ranking'
import { cn } from '../../lib/utils'
import { 
  ChevronLeft, 
  ChevronRight, 
  List, 
  Grid,
  SortAsc,
  SortDesc
} from 'lucide-react'

interface QSRankingListProps {
  universities: ProcessedQSUniversity[]
  currentPage: number
  pageSize: number
  totalCount: number
  onPageChange: (page: number) => void
}

type SortField = 'rank' | 'name' | 'country' | 'academicReputation' | 'employerReputation'
type SortOrder = 'asc' | 'desc'
type ViewMode = 'grid' | 'list'

export function QSRankingList({ 
  universities, 
  currentPage, 
  pageSize, 
  totalCount, 
  onPageChange 
}: QSRankingListProps) {
  const [sortField, setSortField] = useState<SortField>('rank')
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')

  // Sort universities first
  const sortedUniversities = [...universities].sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortField) {
      case 'rank':
        aValue = a.rank
        bValue = b.rank
        break
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'country':
        aValue = a.country.toLowerCase()
        bValue = b.country.toLowerCase()
        break
      case 'academicReputation':
        aValue = a.scores.academicReputation.score
        bValue = b.scores.academicReputation.score
        break
      case 'employerReputation':
        aValue = a.scores.employerReputation.score
        bValue = b.scores.employerReputation.score
        break
      default:
        aValue = a.rank
        bValue = b.rank
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
    }
  })

  // Calculate pagination based on totalCount (server-side pagination)
  const totalPages = Math.ceil(totalCount / pageSize)
  const startIndex = (currentPage - 1) * pageSize + 1
  const endIndex = Math.min(startIndex + universities.length - 1, totalCount)

  // Use sorted universities directly (no client-side pagination)
  const displayUniversities = sortedUniversities

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortOrder('asc')
    }
  }

  const renderSortButton = (field: SortField, label: string) => (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => handleSort(field)}
      className={cn(
        "flex items-center gap-1 text-sm",
        sortField === field ? "text-blue-600 bg-blue-50" : "text-gray-600"
      )}
    >
      {label}
      {sortField === field && (
        sortOrder === 'asc' ? 
          <SortAsc className="w-4 h-4" /> : 
          <SortDesc className="w-4 h-4" />
      )}
    </Button>
  )

  return (
    <div className="space-y-4">
      {/* Header with controls */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-900">
              大学排名列表
            </h2>
            <span className="text-sm text-gray-500">
              显示 {startIndex}-{endIndex} 项，共 {totalCount} 项
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode('grid')}
                className={cn(
                  "px-3 py-1",
                  viewMode === 'grid' ? "bg-white shadow-sm" : ""
                )}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode('list')}
                className={cn(
                  "px-3 py-1",
                  viewMode === 'list' ? "bg-white shadow-sm" : ""
                )}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Sort Controls */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600 self-center">排序：</span>
          {renderSortButton('rank', '排名')}
          {renderSortButton('name', '大学名称')}
          {renderSortButton('country', '国家')}
          {renderSortButton('academicReputation', '学术声誉')}
          {renderSortButton('employerReputation', '雇主声誉')}
        </div>
      </Card>

      {/* University List */}
      {displayUniversities.length > 0 ? (
        <div className={cn(
          viewMode === 'grid'
            ? "grid grid-cols-1 lg:grid-cols-2 gap-4"
            : "space-y-4"
        )}>
          {displayUniversities.map((university) => (
            <QSUniversityCard
              key={university.id}
              university={university}
              viewMode={viewMode}
            />
          ))}
        </div>
      ) : (
        <Card className="p-8 text-center">
          <div className="text-gray-500">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <List className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-lg font-medium mb-2">暂无数据</p>
            <p className="text-sm">请尝试调整筛选条件</p>
          </div>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              第 {currentPage} 页，共 {totalPages} 页
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="w-4 h-4" />
                上一页
              </Button>
              
              {/* Page Numbers */}
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum
                  if (totalPages <= 5) {
                    pageNum = i + 1
                  } else if (currentPage <= 3) {
                    pageNum = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i
                  } else {
                    pageNum = currentPage - 2 + i
                  }
                  
                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(pageNum)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                下一页
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
