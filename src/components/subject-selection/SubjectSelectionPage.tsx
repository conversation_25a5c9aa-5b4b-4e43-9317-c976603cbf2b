import { useState } from 'react'
import { <PERSON><PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { SubjectSelector } from './SubjectSelector'
import { CombinationAnalysis } from './CombinationAnalysis'
import { MajorRecommendation } from './MajorRecommendation'
import { SelectionGuide } from './SelectionGuide'
import { cn } from '../../lib/utils'
import type { UserSelection } from './types'
import { 
  ArrowLeft, 
  BookOpenCheck, 
  Target,
  TrendingUp,
  BookOpen,
  Lightbulb,
  CheckCircle,
  RotateCcw,
  Share2,
  Download
} from 'lucide-react'

interface SubjectSelectionPageProps {
  onBack?: () => void
  className?: string
}

export function SubjectSelectionPage({ onBack, className }: SubjectSelectionPageProps) {
  const [userSelection, setUserSelection] = useState<UserSelection>({
    firstChoice: null,
    secondChoices: [],
    isComplete: false
  })

  const [activeTab, setActiveTab] = useState<'selector' | 'analysis' | 'majors' | 'guide'>('selector')

  // 重置选择
  const handleReset = () => {
    setUserSelection({
      firstChoice: null,
      secondChoices: [],
      isComplete: false
    })
    setActiveTab('selector')
  }

  // 分享选科结果
  const handleShare = () => {
    if (userSelection.isComplete) {
      const text = `我的高考选科：${userSelection.firstChoice} + ${userSelection.secondChoices.join(' + ')}`
      if (navigator.share) {
        navigator.share({
          title: '我的高考选科',
          text: text,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(text)
        alert('选科结果已复制到剪贴板')
      }
    }
  }

  // 导出选科报告
  const handleExport = () => {
    if (userSelection.isComplete) {
      // 这里可以实现导出PDF功能
      alert('导出功能开发中...')
    }
  }

  const tabs = [
    {
      id: 'selector',
      name: '选择科目',
      icon: BookOpenCheck,
      description: '选择首选和再选科目'
    },
    {
      id: 'analysis',
      name: '组合分析',
      icon: Target,
      description: '分析选科组合优劣'
    },
    {
      id: 'majors',
      name: '专业推荐',
      icon: BookOpen,
      description: '查看可报考专业'
    },
    {
      id: 'guide',
      name: '选科指导',
      icon: Lightbulb,
      description: '获取专业选科建议'
    }
  ]

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50", className)}>
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                返回首页
              </Button>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <BookOpenCheck className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="font-semibold text-gray-900">高考选科</h1>
                  <p className="text-xs text-gray-500">新高考3+1+2模式</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {userSelection.isComplete && (
                <>
                  <Button variant="outline" size="sm" onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    分享
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleExport}>
                    <Download className="w-4 h-4 mr-2" />
                    导出
                  </Button>
                </>
              )}
              <Button variant="outline" size="sm" onClick={handleReset}>
                <RotateCcw className="w-4 h-4 mr-2" />
                重新选择
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 选科状态概览 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-orange-500" />
              选科状态
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-3 h-3 rounded-full",
                    userSelection.firstChoice ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm font-medium">首选科目</span>
                  {userSelection.firstChoice && (
                    <Badge className="bg-orange-500 text-white">
                      {userSelection.firstChoice}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-3 h-3 rounded-full",
                    userSelection.secondChoices.length === 2 ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm font-medium">再选科目</span>
                  <span className="text-xs text-gray-500">
                    ({userSelection.secondChoices.length}/2)
                  </span>
                  {userSelection.secondChoices.map(subject => (
                    <Badge key={subject} variant="outline" className="bg-blue-50 text-blue-700">
                      {subject}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {userSelection.isComplete && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">选科完成</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id
            const isDisabled = tab.id !== 'selector' && tab.id !== 'guide' && !userSelection.isComplete
            
            return (
              <button
                key={tab.id}
                onClick={() => !isDisabled && setActiveTab(tab.id as any)}
                disabled={isDisabled}
                className={cn(
                  "flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md text-sm font-medium transition-all duration-200",
                  isActive
                    ? "bg-white text-orange-600 shadow-sm"
                    : isDisabled
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-gray-600 hover:text-gray-900 hover:bg-white/50"
                )}
              >
                <Icon className="w-4 h-4" />
                <div className="text-left">
                  <div>{tab.name}</div>
                  <div className="text-xs opacity-75">{tab.description}</div>
                </div>
              </button>
            )
          })}
        </div>

        {/* 内容区域 */}
        <div className="space-y-8">
          {activeTab === 'selector' && (
            <SubjectSelector
              selection={userSelection}
              onSelectionChange={setUserSelection}
            />
          )}
          
          {activeTab === 'analysis' && (
            <CombinationAnalysis selection={userSelection} />
          )}
          
          {activeTab === 'majors' && (
            <MajorRecommendation selection={userSelection} />
          )}

          {activeTab === 'guide' && (
            <SelectionGuide />
          )}
        </div>

        {/* 底部提示 */}
        {userSelection.isComplete && (
          <Card className="mt-8 bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Lightbulb className="w-6 h-6 text-orange-500 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="font-semibold text-orange-900 mb-2">选科建议</h3>
                  <div className="space-y-2 text-sm text-orange-800">
                    <p>• 您已完成选科：{userSelection.firstChoice} + {userSelection.secondChoices.join(' + ')}</p>
                    <p>• 建议查看"组合分析"了解这个组合的优势和劣势</p>
                    <p>• 在"专业推荐"中查看可报考的专业和院校</p>
                    <p>• 选科确定后建议尽早开始相关科目的深入学习</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 帮助信息 */}
        <Card className="mt-8 bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <TrendingUp className="w-6 h-6 text-blue-500 flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">新高考选科说明</h3>
                <div className="space-y-2 text-sm text-blue-800">
                  <p>• <strong>3+1+2模式：</strong>语文、数学、外语为必考科目</p>
                  <p>• <strong>首选科目：</strong>物理、历史中选择1科</p>
                  <p>• <strong>再选科目：</strong>化学、生物、政治、地理中选择2科</p>
                  <p>• <strong>总分构成：</strong>3门必考科目 + 1门首选科目 + 2门再选科目，满分750分</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
