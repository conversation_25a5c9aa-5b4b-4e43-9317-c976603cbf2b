import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import type { SelectionAdvice } from './types'
import { 
  Lightbulb, 
  Heart,
  TrendingUp,
  Users,
  AlertTriangle,
  CheckCircle,
  Star,
  Target,
  Brain,
  Briefcase
} from 'lucide-react'

interface SelectionGuideProps {
  className?: string
}

export function SelectionGuide({ className }: SelectionGuideProps) {
  // 选科建议数据
  const selectionAdvice: SelectionAdvice[] = [
    {
      type: 'interest',
      title: '兴趣导向选科',
      description: '根据个人兴趣和爱好选择科目，学习动力更强',
      recommendedCombinations: ['physics-chemistry-biology', 'history-politics-geography'],
      reasons: [
        '兴趣是最好的老师，能激发学习热情',
        '有助于培养专业特长和核心竞争力',
        '提高学习效率和成绩表现'
      ],
      priority: 1
    },
    {
      type: 'strength',
      title: '优势科目选科',
      description: '选择自己擅长的科目，确保高考分数优势',
      recommendedCombinations: ['physics-chemistry-geography', 'history-biology-geography'],
      reasons: [
        '发挥学科优势，提高总分竞争力',
        '减少学习压力，增强自信心',
        '为冲击名校奠定基础'
      ],
      priority: 2
    },
    {
      type: 'career',
      title: '职业规划选科',
      description: '根据未来职业规划和专业要求选择科目',
      recommendedCombinations: ['physics-chemistry-biology', 'physics-biology-geography'],
      reasons: [
        '确保专业选择的广度和深度',
        '提前为专业学习做准备',
        '增强就业竞争优势'
      ],
      priority: 3
    },
    {
      type: 'difficulty',
      title: '难度平衡选科',
      description: '合理搭配科目难度，避免学习压力过大',
      recommendedCombinations: ['physics-biology-geography', 'history-biology-geography'],
      reasons: [
        '避免全选高难度科目造成压力',
        '保证各科目均衡发展',
        '提高整体学习效果'
      ],
      priority: 4
    }
  ]

  // 选科误区
  const commonMistakes = [
    {
      title: '盲目跟风选择',
      description: '看别人选什么就选什么，不考虑自身情况',
      icon: Users,
      color: 'text-red-500'
    },
    {
      title: '只看科目难度',
      description: '只选择简单科目，忽视专业要求和兴趣',
      icon: AlertTriangle,
      color: 'text-orange-500'
    },
    {
      title: '过分追求热门',
      description: '只选择所谓的"热门组合"，不考虑个人特点',
      icon: TrendingUp,
      color: 'text-yellow-500'
    },
    {
      title: '忽视专业要求',
      description: '不了解目标专业的科目要求，盲目选择',
      icon: Target,
      color: 'text-blue-500'
    }
  ]

  // 选科步骤
  const selectionSteps = [
    {
      step: 1,
      title: '自我评估',
      description: '了解自己的兴趣、优势科目和学习能力',
      icon: Brain,
      color: 'bg-blue-500'
    },
    {
      step: 2,
      title: '专业了解',
      description: '研究目标专业的科目要求和就业前景',
      icon: Briefcase,
      color: 'bg-green-500'
    },
    {
      step: 3,
      title: '组合分析',
      description: '分析不同选科组合的优劣势和竞争情况',
      icon: Target,
      color: 'bg-purple-500'
    },
    {
      step: 4,
      title: '最终决策',
      description: '综合考虑各种因素，做出最适合的选择',
      icon: CheckCircle,
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className={cn("space-y-6", className)}>
      {/* 选科建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-500" />
            选科建议
          </CardTitle>
          <p className="text-sm text-gray-600">
            根据不同维度为您提供选科指导
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectionAdvice.map((advice, index) => {
              const getIcon = () => {
                switch (advice.type) {
                  case 'interest': return Heart
                  case 'strength': return Star
                  case 'career': return Briefcase
                  case 'difficulty': return Target
                  default: return Lightbulb
                }
              }
              
              const Icon = getIcon()
              
              return (
                <div 
                  key={advice.type}
                  className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                >
                  <div className="flex items-start gap-3 mb-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Icon className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{advice.title}</h3>
                      <p className="text-sm text-gray-600">{advice.description}</p>
                    </div>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700">
                      优先级 {advice.priority}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    {advice.reasons.map((reason, idx) => (
                      <div key={idx} className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{reason}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 选科步骤 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-green-500" />
            选科步骤
          </CardTitle>
          <p className="text-sm text-gray-600">
            科学的选科流程，帮您做出最佳决策
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {selectionSteps.map((step, index) => {
              const Icon = step.icon
              
              return (
                <div key={step.step} className="relative">
                  <div className="text-center">
                    <div className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3",
                      step.color
                    )}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      步骤{step.step}: {step.title}
                    </h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                  
                  {index < selectionSteps.length - 1 && (
                    <div className="hidden md:block absolute top-6 left-full w-full">
                      <div className="w-full h-0.5 bg-gray-300 relative">
                        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gray-300 rounded-full" />
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 常见误区 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            选科误区
          </CardTitle>
          <p className="text-sm text-gray-600">
            避免这些常见的选科误区
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {commonMistakes.map((mistake, index) => {
              const Icon = mistake.icon
              
              return (
                <div 
                  key={index}
                  className="p-4 border border-red-200 rounded-lg bg-red-50"
                >
                  <div className="flex items-start gap-3">
                    <Icon className={cn("w-5 h-5 flex-shrink-0 mt-0.5", mistake.color)} />
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{mistake.title}</h3>
                      <p className="text-sm text-gray-600">{mistake.description}</p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 专家建议 */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-blue-500" />
            专家建议
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900 mb-1">充分了解自己</h4>
                <p className="text-sm text-gray-600">
                  选科前要充分了解自己的兴趣、特长、学习能力和职业规划，这是选科的基础。
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900 mb-1">研究专业要求</h4>
                <p className="text-sm text-gray-600">
                  提前了解目标专业和院校的选科要求，确保选择的科目组合能够满足报考条件。
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900 mb-1">平衡难度与兴趣</h4>
                <p className="text-sm text-gray-600">
                  在兴趣和优势科目之间找到平衡，既要考虑学习的可持续性，也要确保成绩竞争力。
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900 mb-1">咨询专业意见</h4>
                <p className="text-sm text-gray-600">
                  可以咨询老师、学长学姐或专业的升学指导老师，获得更专业的建议。
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
