import type { SubjectInfo, SubjectCombination, MajorRequirement } from './types'

// 科目信息数据
export const subjectData: SubjectInfo[] = [
  // 必考科目
  {
    id: '语文',
    name: '语文',
    category: 'required',
    description: '必考科目，培养语言文字运用能力',
    difficulty: 'medium',
    characteristics: ['文字理解', '表达能力', '文学素养'],
    suitableFor: ['文科倾向', '语言天赋', '表达能力强'],
    relatedMajors: ['中文', '新闻', '教育', '法学']
  },
  {
    id: '数学',
    name: '数学',
    category: 'required',
    description: '必考科目，培养逻辑思维和计算能力',
    difficulty: 'hard',
    characteristics: ['逻辑思维', '计算能力', '抽象思维'],
    suitableFor: ['理科倾向', '逻辑思维强', '计算能力好'],
    relatedMajors: ['数学', '计算机', '工程', '经济']
  },
  {
    id: '外语',
    name: '外语',
    category: 'required',
    description: '必考科目，培养外语交流能力',
    difficulty: 'medium',
    characteristics: ['语言能力', '记忆力', '交流能力'],
    suitableFor: ['语言天赋', '记忆力好', '国际视野'],
    relatedMajors: ['外语', '国际贸易', '翻译', '外交']
  },
  
  // 首选科目
  {
    id: '物理',
    name: '物理',
    category: 'first-choice',
    description: '研究物质运动规律，培养科学思维',
    difficulty: 'hard',
    characteristics: ['科学思维', '实验能力', '数理逻辑'],
    suitableFor: ['理科思维', '动手能力强', '好奇心强'],
    relatedMajors: ['物理', '工程', '计算机', '医学', '航空航天']
  },
  {
    id: '历史',
    name: '历史',
    category: 'first-choice',
    description: '了解人类发展历程，培养人文素养',
    difficulty: 'medium',
    characteristics: ['记忆能力', '分析能力', '人文素养'],
    suitableFor: ['文科思维', '记忆力好', '人文关怀'],
    relatedMajors: ['历史', '考古', '教育', '法学', '管理']
  },
  
  // 再选科目
  {
    id: '化学',
    name: '化学',
    category: 'second-choice',
    description: '研究物质组成和变化，培养实验思维',
    difficulty: 'hard',
    characteristics: ['实验能力', '记忆理解', '细致观察'],
    suitableFor: ['实验兴趣', '记忆力好', '细心认真'],
    relatedMajors: ['化学', '材料', '生物', '医学', '环境']
  },
  {
    id: '生物',
    name: '生物',
    category: 'second-choice',
    description: '研究生命现象，培养生命科学素养',
    difficulty: 'medium',
    characteristics: ['观察能力', '记忆理解', '生命关怀'],
    suitableFor: ['生命兴趣', '观察力强', '记忆力好'],
    relatedMajors: ['生物', '医学', '农学', '环境', '食品']
  },
  {
    id: '政治',
    name: '思想政治',
    category: 'second-choice',
    description: '培养政治素养和思辨能力',
    difficulty: 'medium',
    characteristics: ['思辨能力', '记忆理解', '社会关怀'],
    suitableFor: ['社会关注', '思辨能力', '表达能力'],
    relatedMajors: ['政治', '法学', '管理', '教育', '新闻']
  },
  {
    id: '地理',
    name: '地理',
    category: 'second-choice',
    description: '研究地球表面现象，培养空间思维',
    difficulty: 'medium',
    characteristics: ['空间思维', '综合分析', '环境意识'],
    suitableFor: ['空间想象', '综合思维', '环境关注'],
    relatedMajors: ['地理', '环境', '城规', '旅游', '测绘']
  }
]

// 热门选科组合
export const popularCombinations: SubjectCombination[] = [
  {
    id: 'physics-chemistry-biology',
    name: '物理+化学+生物',
    subjects: ['物理', '化学', '生物'],
    firstChoice: '物理',
    secondChoices: ['化学', '生物'],
    description: '传统理科组合，专业覆盖面最广',
    advantages: ['专业选择最多', '理科思维培养', '就业面广'],
    disadvantages: ['竞争激烈', '学习难度大', '压力较大'],
    difficulty: 5,
    majorCoverage: 96.4,
    popularityRank: 1,
    genderRatio: { male: 65, female: 35 }
  },
  {
    id: 'physics-chemistry-geography',
    name: '物理+化学+地理',
    subjects: ['物理', '化学', '地理'],
    firstChoice: '物理',
    secondChoices: ['化学', '地理'],
    description: '理科为主，地理调节难度',
    advantages: ['专业覆盖广', '地理相对简单', '文理兼顾'],
    disadvantages: ['思维跨度大', '学习方法不同'],
    difficulty: 4,
    majorCoverage: 95.8,
    popularityRank: 2,
    genderRatio: { male: 60, female: 40 }
  },
  {
    id: 'physics-biology-geography',
    name: '物理+生物+地理',
    subjects: ['物理', '生物', '地理'],
    firstChoice: '物理',
    secondChoices: ['生物', '地理'],
    description: '避开化学，降低学习难度',
    advantages: ['避开化学难点', '专业覆盖较广', '学习压力适中'],
    disadvantages: ['部分工科专业受限', '思维方式差异'],
    difficulty: 3,
    majorCoverage: 87.3,
    popularityRank: 3,
    genderRatio: { male: 55, female: 45 }
  },
  {
    id: 'history-politics-geography',
    name: '历史+政治+地理',
    subjects: ['历史', '政治', '地理'],
    firstChoice: '历史',
    secondChoices: ['政治', '地理'],
    description: '传统文科组合，文科专业全覆盖',
    advantages: ['文科专业全覆盖', '学习方法相近', '记忆为主'],
    disadvantages: ['专业选择受限', '就业竞争激烈', '理科专业无法报考'],
    difficulty: 2,
    majorCoverage: 52.9,
    popularityRank: 4,
    genderRatio: { male: 25, female: 75 }
  },
  {
    id: 'history-biology-geography',
    name: '历史+生物+地理',
    subjects: ['历史', '生物', '地理'],
    firstChoice: '历史',
    secondChoices: ['生物', '地理'],
    description: '文理兼顾，增加专业选择',
    advantages: ['可报考医学专业', '学习难度适中', '文理兼顾'],
    disadvantages: ['思维跨度较大', '部分专业受限'],
    difficulty: 3,
    majorCoverage: 67.8,
    popularityRank: 5,
    genderRatio: { male: 35, female: 65 }
  },
  {
    id: 'physics-politics-geography',
    name: '物理+政治+地理',
    subjects: ['物理', '政治', '地理'],
    firstChoice: '物理',
    secondChoices: ['政治', '地理'],
    description: '理科基础，文科补充',
    advantages: ['理工专业可选', '文理兼顾', '竞争相对较小'],
    disadvantages: ['思维跨度最大', '学习方法差异大'],
    difficulty: 4,
    majorCoverage: 82.4,
    popularityRank: 6,
    genderRatio: { male: 50, female: 50 }
  }
]

// 专业要求示例数据
export const majorRequirements: MajorRequirement[] = [
  {
    majorId: 'computer-science',
    majorName: '计算机科学与技术',
    category: '工学',
    requiredSubjects: ['物理'],
    preferredSubjects: ['化学', '生物'],
    description: '培养计算机系统设计、开发和应用的高级技术人才',
    employmentRate: 95.2,
    averageSalary: 12000,
    difficulty: 'hard'
  },
  {
    majorId: 'clinical-medicine',
    majorName: '临床医学',
    category: '医学',
    requiredSubjects: ['物理', '化学', '生物'],
    preferredSubjects: [],
    description: '培养具备基础医学、临床医学基本理论和技能的医师',
    employmentRate: 89.7,
    averageSalary: 8500,
    difficulty: 'hard'
  },
  {
    majorId: 'law',
    majorName: '法学',
    category: '法学',
    requiredSubjects: [],
    preferredSubjects: ['历史', '政治'],
    description: '培养具有法学基本理论和实务技能的法律人才',
    employmentRate: 78.3,
    averageSalary: 7800,
    difficulty: 'medium'
  }
]
