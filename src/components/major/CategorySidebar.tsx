import { useState } from 'react'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import { disciplineCategories, categoryStats, subCategories } from '../../data/majors'

// 临时类型定义 - 支持所有教育层次的分类
type DisciplineCategory =
  // 本科普通教育的学科门类
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'
  // 本科职业教育和专科高职的专业大类
  | '交通运输'
  | '公共管理与服务'
  | '公安与司法'
  | '农林牧渔'
  | '医药卫生'
  | '土木建筑'
  | '教育与体育'
  | '文化艺术'
  | '新闻传播'
  | '旅游'
  | '水利'
  | '生物与化工'
  | '电子与信息'
  | '能源动力与材料'
  | '装备制造'
  | '财经商贸'
  | '资源环境与安全'
  | '轻工纺织'
  | '食品药品与粮食'
import {
  Wrench,
  Stethoscope,
  BookOpen,
  Building2,
  Atom,
  TrendingUp,
  Scale,
  Palette,
  GraduationCap,
  Wheat,
  Clock,
  Brain,
  ChevronDown,
  ChevronRight,
  Truck,
  Shield,
  Briefcase,
  Sprout,
  Heart,
  Home,
  Dumbbell,
  Music,
  Newspaper,
  MapPin,
  Droplets,
  FlaskConical,
  Zap,
  Settings,
  ShoppingCart,
  Mountain,
  Shirt,
  Apple
} from 'lucide-react'

interface CategorySidebarProps {
  selectedCategory?: DisciplineCategory
  selectedSubCategory?: string
  onCategorySelect: (category: DisciplineCategory | undefined) => void
  onSubCategorySelect?: (subCategory: string | undefined) => void
  className?: string
  // 动态数据支持
  categories?: DisciplineCategory[]
  categoryStats?: Array<{category: DisciplineCategory, count: number}>
  subCategories?: Record<DisciplineCategory, string[]>
}

// 学科门类图标映射
const categoryIcons: Record<DisciplineCategory, React.ReactNode> = {
  // 本科普通教育的学科门类
  '工学': <Wrench className="w-5 h-5" />,
  '医学': <Stethoscope className="w-5 h-5" />,
  '文学': <BookOpen className="w-5 h-5" />,
  '管理学': <Building2 className="w-5 h-5" />,
  '理学': <Atom className="w-5 h-5" />,
  '经济学': <TrendingUp className="w-5 h-5" />,
  '法学': <Scale className="w-5 h-5" />,
  '艺术学': <Palette className="w-5 h-5" />,
  '教育学': <GraduationCap className="w-5 h-5" />,
  '农学': <Wheat className="w-5 h-5" />,
  '历史学': <Clock className="w-5 h-5" />,
  '哲学': <Brain className="w-5 h-5" />,
  // 本科职业教育和专科高职的专业大类
  '交通运输': <Truck className="w-5 h-5" />,
  '公共管理与服务': <Building2 className="w-5 h-5" />,
  '公安与司法': <Shield className="w-5 h-5" />,
  '农林牧渔': <Sprout className="w-5 h-5" />,
  '医药卫生': <Heart className="w-5 h-5" />,
  '土木建筑': <Home className="w-5 h-5" />,
  '教育与体育': <Dumbbell className="w-5 h-5" />,
  '文化艺术': <Music className="w-5 h-5" />,
  '新闻传播': <Newspaper className="w-5 h-5" />,
  '旅游': <MapPin className="w-5 h-5" />,
  '水利': <Droplets className="w-5 h-5" />,
  '生物与化工': <FlaskConical className="w-5 h-5" />,
  '电子与信息': <Zap className="w-5 h-5" />,
  '能源动力与材料': <Zap className="w-5 h-5" />,
  '装备制造': <Settings className="w-5 h-5" />,
  '财经商贸': <ShoppingCart className="w-5 h-5" />,
  '资源环境与安全': <Mountain className="w-5 h-5" />,
  '轻工纺织': <Shirt className="w-5 h-5" />,
  '食品药品与粮食': <Apple className="w-5 h-5" />
}

// 学科门类颜色映射
const categoryColors: Record<DisciplineCategory, string> = {
  // 本科普通教育的学科门类
  '工学': 'text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100',
  '医学': 'text-red-600 bg-red-50 border-red-200 hover:bg-red-100',
  '文学': 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100',
  '管理学': 'text-purple-600 bg-purple-50 border-purple-200 hover:bg-purple-100',
  '理学': 'text-indigo-600 bg-indigo-50 border-indigo-200 hover:bg-indigo-100',
  '经济学': 'text-yellow-600 bg-yellow-50 border-yellow-200 hover:bg-yellow-100',
  '法学': 'text-gray-600 bg-gray-50 border-gray-200 hover:bg-gray-100',
  '艺术学': 'text-pink-600 bg-pink-50 border-pink-200 hover:bg-pink-100',
  '教育学': 'text-teal-600 bg-teal-50 border-teal-200 hover:bg-teal-100',
  '农学': 'text-lime-600 bg-lime-50 border-lime-200 hover:bg-lime-100',
  '历史学': 'text-amber-600 bg-amber-50 border-amber-200 hover:bg-amber-100',
  '哲学': 'text-violet-600 bg-violet-50 border-violet-200 hover:bg-violet-100',
  // 本科职业教育和专科高职的专业大类
  '交通运输': 'text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100',
  '公共管理与服务': 'text-purple-600 bg-purple-50 border-purple-200 hover:bg-purple-100',
  '公安与司法': 'text-slate-600 bg-slate-50 border-slate-200 hover:bg-slate-100',
  '农林牧渔': 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100',
  '医药卫生': 'text-red-600 bg-red-50 border-red-200 hover:bg-red-100',
  '土木建筑': 'text-orange-600 bg-orange-50 border-orange-200 hover:bg-orange-100',
  '教育与体育': 'text-teal-600 bg-teal-50 border-teal-200 hover:bg-teal-100',
  '文化艺术': 'text-pink-600 bg-pink-50 border-pink-200 hover:bg-pink-100',
  '新闻传播': 'text-cyan-600 bg-cyan-50 border-cyan-200 hover:bg-cyan-100',
  '旅游': 'text-emerald-600 bg-emerald-50 border-emerald-200 hover:bg-emerald-100',
  '水利': 'text-sky-600 bg-sky-50 border-sky-200 hover:bg-sky-100',
  '生物与化工': 'text-lime-600 bg-lime-50 border-lime-200 hover:bg-lime-100',
  '电子与信息': 'text-indigo-600 bg-indigo-50 border-indigo-200 hover:bg-indigo-100',
  '能源动力与材料': 'text-yellow-600 bg-yellow-50 border-yellow-200 hover:bg-yellow-100',
  '装备制造': 'text-gray-600 bg-gray-50 border-gray-200 hover:bg-gray-100',
  '财经商贸': 'text-amber-600 bg-amber-50 border-amber-200 hover:bg-amber-100',
  '资源环境与安全': 'text-stone-600 bg-stone-50 border-stone-200 hover:bg-stone-100',
  '轻工纺织': 'text-rose-600 bg-rose-50 border-rose-200 hover:bg-rose-100',
  '食品药品与粮食': 'text-green-600 bg-green-50 border-green-200 hover:bg-green-100'
}

export function CategorySidebar({
  selectedCategory,
  selectedSubCategory,
  onCategorySelect,
  onSubCategorySelect,
  className,
  categories: propCategories,
  categoryStats: propCategoryStats,
  subCategories: propSubCategories
}: CategorySidebarProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<DisciplineCategory>>(new Set())

  // 使用传入的动态数据，如果没有则使用默认数据
  const currentCategories = propCategories || disciplineCategories
  const currentCategoryStats = propCategoryStats || categoryStats
  const currentSubCategories = propSubCategories || subCategories

  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `${(salary / 10000).toFixed(1)}万`
    }
    return `${salary.toLocaleString()}`
  }

  const toggleCategory = (category: DisciplineCategory) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 全部专业选项 */}
      <Card className="p-4">
        <h3 className="text-lg font-bold text-gray-900 mb-4">专业分类</h3>
        
        <button
          onClick={() => onCategorySelect(undefined)}
          className={cn(
            "w-full flex items-center justify-between p-3 rounded-lg border transition-all duration-200 text-left",
            !selectedCategory 
              ? "bg-blue-600 text-white border-blue-600" 
              : "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100"
          )}
        >
          <div className="flex items-center gap-3">
            <div className={cn(
              "w-8 h-8 rounded-lg flex items-center justify-center",
              !selectedCategory ? "bg-white/20" : "bg-gray-200"
            )}>
              <BookOpen className={cn("w-4 h-4", !selectedCategory ? "text-white" : "text-gray-600")} />
            </div>
            <span className="font-medium">全部专业</span>
          </div>
          <span className={cn(
            "text-sm px-2 py-1 rounded",
            !selectedCategory ? "bg-white/20 text-white" : "bg-gray-200 text-gray-600"
          )}>
            {currentCategoryStats.reduce((sum, stat) => sum + stat.count, 0)}
          </span>
        </button>
      </Card>

      {/* 学科分类列表 */}
      <Card className="p-4">
        <div className="space-y-2">
          {currentCategories.map((category) => {
            const stat = currentCategoryStats.find(s => s.category === category)
            const isSelected = selectedCategory === category
            const isExpanded = expandedCategories.has(category)
            const colorClass = categoryColors[category] || 'text-gray-600 bg-gray-50 border-gray-200 hover:bg-gray-100'
            const categorySubCategories = currentSubCategories[category] || []

            return (
              <div key={category} className="space-y-1">
                {/* 主分类按钮 */}
                <div className="flex">
                  <button
                    onClick={() => onCategorySelect(category)}
                    className={cn(
                      "flex-1 flex items-center justify-between p-3 rounded-lg border transition-all duration-200 text-left",
                      isSelected
                        ? "bg-blue-600 text-white border-blue-600"
                        : `border-transparent ${colorClass}`
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-8 h-8 rounded-lg flex items-center justify-center",
                        isSelected ? "bg-white/20" : ""
                      )}>
                        <div className={isSelected ? "text-white" : ""}>
                          {categoryIcons[category]}
                        </div>
                      </div>
                      <span className="font-medium">{category}</span>
                    </div>
                    <span className={cn(
                      "text-sm px-2 py-1 rounded",
                      isSelected ? "bg-white/20 text-white" : "bg-white/50"
                    )}>
                      {stat?.count || 0}
                    </span>
                  </button>

                  {/* 展开/折叠按钮 */}
                  {categorySubCategories.length > 0 && (
                    <button
                      onClick={() => toggleCategory(category)}
                      className={cn(
                        "ml-1 p-3 rounded-lg border transition-all duration-200",
                        isSelected
                          ? "bg-blue-600 text-white border-blue-600"
                          : `border-transparent ${colorClass}`
                      )}
                    >
                      {isExpanded ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>
                  )}
                </div>

                {/* 子分类列表 */}
                {isExpanded && categorySubCategories.length > 0 && (
                  <div className="ml-6 space-y-1">
                    {categorySubCategories.map((subCategory) => (
                      <button
                        key={subCategory}
                        onClick={() => onSubCategorySelect?.(subCategory)}
                        className={cn(
                          "w-full text-left p-2 rounded text-sm transition-all duration-200",
                          selectedSubCategory === subCategory
                            ? "bg-blue-100 text-blue-800 font-medium"
                            : "text-gray-600 hover:bg-gray-100 hover:text-gray-800"
                        )}
                      >
                        {subCategory}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </Card>

      {/* 统计信息 */}
      {selectedCategory && (
        <Card className="p-4">
          <h4 className="font-bold text-gray-900 mb-3">{selectedCategory}统计</h4>
          {(() => {
            const stat = currentCategoryStats.find(s => s.category === selectedCategory)
            if (!stat) return null
            
            return (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">专业数量</span>
                  <span className="font-medium">{stat.count}个</span>
                </div>
                
                {stat.avgSalary && stat.avgSalary > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">平均薪资</span>
                    <span className="font-medium text-green-600">
                      ¥{formatSalary(stat.avgSalary)}
                    </span>
                  </div>
                )}
                
                {stat.avgEmploymentRate && stat.avgEmploymentRate > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">平均就业率</span>
                    <span className="font-medium text-blue-600">
                      {stat.avgEmploymentRate.toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>
            )
          })()}
        </Card>
      )}
    </div>
  )
}
