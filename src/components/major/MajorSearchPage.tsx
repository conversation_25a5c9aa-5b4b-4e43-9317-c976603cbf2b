import { useState, useEffect, useMemo } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { cn } from '../../lib/utils'
import { MajorCard } from './MajorCard'
import { CategorySidebar } from './CategorySidebar'
import { RecommendedUniversities } from './RecommendedUniversities'
import { MajorFilters } from './MajorFilters'
import { EducationLevelTabs } from './EducationLevelTabs'
import { mockMajors } from '../../data/majors'
import {
  getMajorTree,
  searchMajors,
  getAllMajors,
  flattenMajorTree,
  transformApiDataToMajor,
  extractCategoriesFromApiData,
  type EducationLevel as ApiEducationLevel
} from '../../services/majorTreeApi'

// 临时类型定义 - 支持所有教育层次的分类
type DisciplineCategory =
  // 本科普通教育的学科门类
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'
  // 本科职业教育和专科高职的专业大类
  | '交通运输'
  | '公共管理与服务'
  | '公安与司法'
  | '农林牧渔'
  | '医药卫生'
  | '土木建筑'
  | '教育与体育'
  | '文化艺术'
  | '新闻传播'
  | '旅游'
  | '水利'
  | '生物与化工'
  | '电子与信息'
  | '能源动力与材料'
  | '装备制造'
  | '财经商贸'
  | '资源环境与安全'
  | '轻工纺织'
  | '食品药品与粮食'

type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: DisciplineCategory
  subCategory?: string
  degree: string
  duration: number
  educationLevel: EducationLevel
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
}

interface MajorSearchFilters {
  keyword?: string
  sortBy?: 'name' | 'salary' | 'employment' | 'popularity'
  sortOrder?: 'asc' | 'desc'
}
import { 
  ArrowLeft,
  GraduationCap,
  TrendingUp,
  Users,
  MapPin,
  BarChart3,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface MajorSearchPageProps {
  onBack?: () => void
  onMajorClick?: (major: Major) => void
}

export function MajorSearchPage({ onBack, onMajorClick }: MajorSearchPageProps) {
  const [selectedCategory, setSelectedCategory] = useState<DisciplineCategory | undefined>()
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | undefined>()
  const [selectedEducationLevel, setSelectedEducationLevel] = useState<EducationLevel>('本科(普通)')
  const [filters, setFilters] = useState<MajorSearchFilters>({
    sortBy: 'name',
    sortOrder: 'asc'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const pageSize = 10

  // API数据状态
  const [apiMajors, setApiMajors] = useState<Major[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [useApiData, setUseApiData] = useState(true) // 控制是否使用API数据

  // 动态分类数据
  const [dynamicCategories, setDynamicCategories] = useState<{
    categories: DisciplineCategory[]
    categoryStats: Array<{category: DisciplineCategory, count: number}>
    subCategories: Record<DisciplineCategory, string[]>
  }>({
    categories: [],
    categoryStats: [],
    subCategories: {}
  })

  // 根据专业数据生成动态分类
  const generateDynamicCategories = (majors: Major[], apiData?: any) => {
    // 首先从API数据中提取所有学科门类信息
    if (apiData) {
      const { categories: allCategories, categoryStats: allCategoryStats } = extractCategoriesFromApiData(apiData)

      // 对于有专业数据的分类，使用实际统计
      const categoryMap = new Map<DisciplineCategory, number>()
      const subCategoryMap = new Map<DisciplineCategory, Set<string>>()

      majors.forEach(major => {
        const category = major.category as DisciplineCategory
        const subCategory = major.subCategory

        // 统计分类数量
        categoryMap.set(category, (categoryMap.get(category) || 0) + 1)

        // 收集子分类
        if (subCategory) {
          if (!subCategoryMap.has(category)) {
            subCategoryMap.set(category, new Set())
          }
          subCategoryMap.get(category)!.add(subCategory)
        }
      })

      // 合并所有分类信息
      const categories = allCategories as DisciplineCategory[]
      const categoryStats = allCategoryStats.map(stat => ({
        category: stat.category as DisciplineCategory,
        count: categoryMap.get(stat.category as DisciplineCategory) || stat.count
      }))

      const subCategories: Record<DisciplineCategory, string[]> = {}
      subCategoryMap.forEach((subCats, category) => {
        subCategories[category] = Array.from(subCats).sort()
      })

      setDynamicCategories({
        categories,
        categoryStats,
        subCategories
      })
    } else {
      // 回退到原有逻辑
      const categoryMap = new Map<DisciplineCategory, number>()
      const subCategoryMap = new Map<DisciplineCategory, Set<string>>()

      majors.forEach(major => {
        const category = major.category as DisciplineCategory
        const subCategory = major.subCategory

        // 统计分类数量
        categoryMap.set(category, (categoryMap.get(category) || 0) + 1)

        // 收集子分类
        if (subCategory) {
          if (!subCategoryMap.has(category)) {
            subCategoryMap.set(category, new Set())
          }
          subCategoryMap.get(category)!.add(subCategory)
        }
      })

      const categories = Array.from(categoryMap.keys()).sort()
      const categoryStats = categories.map(category => ({
        category,
        count: categoryMap.get(category) || 0
      }))

      const subCategories: Record<DisciplineCategory, string[]> = {}
      subCategoryMap.forEach((subCats, category) => {
        subCategories[category] = Array.from(subCats).sort()
      })

      setDynamicCategories({
        categories,
        categoryStats,
        subCategories
      })
    }
  }

  // 加载专业数据
  const loadMajorData = async (keyword?: string, categoryId?: string) => {
    if (!useApiData) return

    setLoading(true)
    setError(null)

    try {
      console.log('🔍 加载专业数据，教育层次:', selectedEducationLevel, '关键词:', keyword, '分类ID:', categoryId)

      let response
      if (keyword && keyword.trim()) {
        // 如果有搜索关键词，使用搜索API
        response = await searchMajors(keyword.trim(), selectedEducationLevel as ApiEducationLevel)
      } else if (categoryId) {
        // 如果有分类ID，使用getMajorTree API获取该分类的数据
        response = await getMajorTree('', categoryId, selectedEducationLevel)
      } else {
        // 按教育层次获取专业数据
        response = await getAllMajors(selectedEducationLevel as ApiEducationLevel)
      }

      if (response.data && typeof response.data === 'object') {
        // 扁平化树形结构并转换为组件所需格式
        const flatMajors = flattenMajorTree(response.data)

        // 如果没有传教育层次参数，需要在客户端进行筛选
        let filteredMajors = flatMajors
        if ((!keyword || !keyword.trim()) && !categoryId) {
          // 按教育层次筛选（只在没有分类ID时进行筛选）
          const levelMapping: Record<string, string[]> = {
            '本科(普通)': ['本科（普通教育）', '本科(普通教育)', '本科（普通）', '本科(普通)'],
            '本科(职业)': ['本科（职业教育）', '本科(职业教育)', '本科（职业）', '本科(职业)'],
            '专科(高职)': ['高职（专科）', '高职(专科)', '专科（高职）', '专科(高职)']
          }

          const allowedLevels = levelMapping[selectedEducationLevel] || [selectedEducationLevel]
          filteredMajors = flatMajors.filter(major =>
            allowedLevels.some(level => major.educationLevel === level)
          )
        }

        const transformedMajors = filteredMajors.map(transformApiDataToMajor)

        setApiMajors(transformedMajors)

        // 生成动态分类数据，传递原始API数据以获取所有学科门类
        generateDynamicCategories(transformedMajors, response.data)

        console.log('✅ 专业数据加载成功:', transformedMajors.length, '个专业')
        console.log('📊 API数据结构:', response.data)
      } else {
        console.warn('⚠️ API返回数据为空，使用模拟数据')
        setUseApiData(false)
      }
    } catch (err) {
      console.error('❌ 加载专业数据失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
      setUseApiData(false) // 失败时回退到模拟数据
    } finally {
      setLoading(false)
    }
  }

  // 搜索防抖处理
  useEffect(() => {
    if (!useApiData) return

    const timeoutId = setTimeout(() => {
      loadMajorData(filters.keyword)
    }, 500) // 500ms防抖

    return () => clearTimeout(timeoutId)
  }, [filters.keyword, selectedEducationLevel, useApiData])

  // 分类选择处理
  const handleCategorySelect = (category: DisciplineCategory | undefined) => {
    setSelectedCategory(category)
    setSelectedSubCategory(undefined) // 清除子分类选择

    if (useApiData) {
      // 调用API获取该分类的数据
      loadMajorData(undefined, category)
    }
  }

  // 页面初始加载时获取默认数据
  useEffect(() => {
    console.log('🚀 页面初始化，开始加载本科(普通)专业数据')
    loadMajorData()
  }, []) // 空依赖数组，只在组件挂载时执行一次

  // 当教育层次改变时重新加载数据（不包含关键词搜索）
  useEffect(() => {
    if (!filters.keyword) {
      loadMajorData()
    }
  }, [selectedEducationLevel, useApiData])

  // 筛选和排序逻辑
  const filteredMajors = useMemo(() => {
    const sourceMajors = useApiData ? apiMajors : mockMajors
    let result = [...sourceMajors]

    // 按教育层次筛选（API数据已经在loadMajorData中筛选过了，这里只对模拟数据筛选）
    if (!useApiData) {
      result = result.filter(major => major.educationLevel === selectedEducationLevel)
    }

    // 按分类筛选
    if (selectedCategory) {
      result = result.filter(major => major.category === selectedCategory)
    }

    // 按子分类筛选
    if (selectedSubCategory) {
      result = result.filter(major => major.subCategory === selectedSubCategory)
    }

    // 关键词搜索
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      result = result.filter(major => 
        major.name.toLowerCase().includes(keyword) ||
        major.englishName?.toLowerCase().includes(keyword) ||
        major.category.toLowerCase().includes(keyword) ||
        major.subCategory?.toLowerCase().includes(keyword)
      )
    }



    // 排序
    result.sort((a, b) => {
      let aValue: any, bValue: any
      
      switch (filters.sortBy) {
        case 'name':
          aValue = a.name
          bValue = b.name
          break
        case 'salary':
          aValue = a.employment?.avgSalary || 0
          bValue = b.employment?.avgSalary || 0
          break
        case 'employment':
          aValue = a.employment?.rate || 0
          bValue = b.employment?.rate || 0
          break
        case 'popularity':
          // 简单的热门度计算：就业率 + 薪资权重
          aValue = (a.employment?.rate || 0) + (a.employment?.avgSalary || 0) / 1000
          bValue = (b.employment?.rate || 0) + (b.employment?.avgSalary || 0) / 1000
          break
        default:
          aValue = a.name
          bValue = b.name
      }

      if (typeof aValue === 'string') {
        const comparison = aValue.localeCompare(bValue)
        return filters.sortOrder === 'asc' ? comparison : -comparison
      } else {
        const comparison = aValue - bValue
        return filters.sortOrder === 'asc' ? comparison : -comparison
      }
    })

    return result
  }, [selectedCategory, selectedSubCategory, selectedEducationLevel, filters, apiMajors, useApiData])

  // 分页逻辑
  const totalPages = Math.ceil(filteredMajors.length / pageSize)
  const paginatedMajors = filteredMajors.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  // 重置页码当筛选条件改变时
  useEffect(() => {
    setCurrentPage(1)
  }, [selectedCategory, selectedSubCategory, selectedEducationLevel, filters])

  // 统计信息
  const stats = useMemo(() => {
    const totalMajors = filteredMajors.length
    const avgSalary = filteredMajors.length > 0
      ? filteredMajors.reduce((sum, major) => sum + (major.employment?.avgSalary || 0), 0) / filteredMajors.length
      : 0
    const avgEmploymentRate = filteredMajors.length > 0
      ? filteredMajors.reduce((sum, major) => sum + (major.employment?.rate || 0), 0) / filteredMajors.length
      : 0
    const categoriesCount = new Set(filteredMajors.map(major => major.category)).size

    return {
      totalMajors,
      avgSalary: Math.round(avgSalary),
      avgEmploymentRate: Math.round(avgEmploymentRate * 100) / 100,
      categoriesCount
    }
  }, [filteredMajors])

  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `${(salary / 10000).toFixed(1)}万`
    }
    return `${salary.toLocaleString()}`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <GraduationCap className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">查专业</h1>
                {/* 数据源指示器 */}
                <div className="flex items-center gap-2 ml-4">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    useApiData ? "bg-green-500" : "bg-yellow-500"
                  )}></div>
                  <span className="text-xs text-gray-500">
                    {useApiData ? "在线数据" : "离线数据"}
                  </span>
                </div>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="hidden md:flex items-center gap-6 text-sm">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4 text-blue-600" />
                <span className="text-gray-600">找到专业</span>
                <span className="font-bold text-blue-600">{stats.totalMajors}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-600" />
                <span className="text-gray-600">平均薪资</span>
                <span className="font-bold text-green-600">¥{formatSalary(stats.avgSalary)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-purple-600" />
                <span className="text-gray-600">平均就业率</span>
                <span className="font-bold text-purple-600">{stats.avgEmploymentRate}%</span>
              </div>
              
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-orange-600" />
                <span className="text-gray-600">涵盖学科</span>
                <span className="font-bold text-orange-600">{stats.categoriesCount}</span>
              </div>
            </div>

            {/* 视图切换 */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 教育层次标签 */}
        <div className="mb-6">
          <EducationLevelTabs
            selectedLevel={selectedEducationLevel}
            onLevelChange={setSelectedEducationLevel}
            className="max-w-md"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧：学科分类 */}
          <div className="lg:col-span-1">
            <CategorySidebar
              selectedCategory={selectedCategory}
              selectedSubCategory={selectedSubCategory}
              onCategorySelect={handleCategorySelect}
              onSubCategorySelect={setSelectedSubCategory}
              className="sticky top-24"
              // 传入动态数据
              categories={useApiData ? dynamicCategories.categories : undefined}
              categoryStats={useApiData ? dynamicCategories.categoryStats : undefined}
              subCategories={useApiData ? dynamicCategories.subCategories : undefined}
            />
          </div>

          {/* 中间：专业列表 */}
          <div className="lg:col-span-2">
            {/* 筛选器 */}
            <div className="mb-6">
              <MajorFilters
                filters={filters}
                onFiltersChange={setFilters}
              />
            </div>

            {/* 加载状态 */}
            {loading && (
              <Card className="p-12 text-center">
                <div className="animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-gray-600">正在加载专业数据...</p>
              </Card>
            )}

            {/* 错误状态 */}
            {error && !loading && (
              <Card className="p-12 text-center border-red-200 bg-red-50">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="w-8 h-8 text-red-600" />
                </div>
                <h3 className="text-lg font-medium text-red-900 mb-2">数据加载失败</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <div className="flex items-center justify-center gap-3">
                  <Button onClick={loadMajorData} variant="outline">
                    重新加载
                  </Button>
                  <Button onClick={() => setUseApiData(false)}>
                    使用离线数据
                  </Button>
                </div>
              </Card>
            )}

            {/* 专业列表 */}
            {!loading && !error && paginatedMajors.length > 0 && (
              <div className={cn(
                "space-y-4",
                viewMode === 'grid' && "grid grid-cols-1 gap-4 space-y-0"
              )}>
                {paginatedMajors.map((major) => (
                  <MajorCard
                    key={major.id}
                    major={major}
                    onClick={() => onMajorClick?.(major)}
                  />
                ))}
              </div>
            )}

            {/* 空状态 */}
            {!loading && !error && paginatedMajors.length === 0 && (
              <Card className="p-12 text-center">
                <GraduationCap className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的专业</h3>
                <p className="text-gray-600 mb-4">请尝试调整筛选条件或搜索关键词</p>
                <div className="flex items-center justify-center gap-3">
                  <Button onClick={() => {
                    setFilters({ sortBy: 'name', sortOrder: 'asc' })
                    setSelectedCategory(undefined)
                    setSelectedSubCategory(undefined)
                  }}>
                    清除所有筛选
                  </Button>
                  {useApiData && (
                    <Button onClick={() => setUseApiData(false)} variant="outline">
                      使用离线数据
                    </Button>
                  )}
                </div>
              </Card>
            )}

            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-8">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1
                    return (
                      <Button
                        key={page}
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    )
                  })}
                  
                  {totalPages > 5 && (
                    <>
                      <span className="px-2 text-gray-500">...</span>
                      <Button
                        variant={currentPage === totalPages ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>

          {/* 右侧：推荐院校 */}
          <div className="lg:col-span-1">
            <RecommendedUniversities className="sticky top-24" />
          </div>
        </div>
      </div>
    </div>
  )
}
