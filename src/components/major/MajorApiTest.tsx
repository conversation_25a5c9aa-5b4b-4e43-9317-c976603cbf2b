import { useState } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import {
  getMajorTree,
  searchMajors,
  getAllMajors,
  flattenMajorTree,
  transformApiDataToMajor,
  type EducationLevel
} from '../../services/majorTreeApi'

interface TestResult {
  type: string
  data: any
  message: string
}

export function MajorApiTest() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<TestResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTest, setActiveTest] = useState('')

  const testGetAllMajors = async (educationLevel: EducationLevel) => {
    setLoading(true)
    setError(null)
    setResult(null)
    setActiveTest(`获取所有专业-${educationLevel}`)

    try {
      console.log('🧪 测试获取所有专业API...', educationLevel)
      const response = await getAllMajors(educationLevel)

      const flatMajors = flattenMajorTree(response.data)
      const transformedMajors = flatMajors.map(transformApiDataToMajor)
      
      setResult({
        type: 'majors',
        data: {
          raw: response,
          processed: transformedMajors.slice(0, 5) // 只显示前5个
        },
        message: `成功获取 ${transformedMajors.length} 个专业`
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取专业数据失败'
      setError(errorMessage)
      console.error('❌ 获取专业数据失败:', err)
    } finally {
      setLoading(false)
      setActiveTest('')
    }
  }

  const testSearchMajors = async (keyword: string, educationLevel: EducationLevel) => {
    setLoading(true)
    setError(null)
    setResult(null)
    setActiveTest(`搜索专业-${keyword}`)

    try {
      console.log('🧪 测试搜索专业API...', keyword, educationLevel)
      const response = await searchMajors(keyword, educationLevel)

      const flatMajors = flattenMajorTree(response.data)
      const transformedMajors = flatMajors.map(transformApiDataToMajor)
      
      setResult({
        type: 'search',
        data: {
          raw: response,
          processed: transformedMajors.slice(0, 5)
        },
        message: `搜索"${keyword}"找到 ${transformedMajors.length} 个专业`
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '搜索专业失败'
      setError(errorMessage)
      console.error('❌ 搜索专业失败:', err)
    } finally {
      setLoading(false)
      setActiveTest('')
    }
  }

  return (
    <div className="container mx-auto p-6">
      <Card className="p-6">
        <h1 className="text-2xl font-bold mb-6">专业API测试中心</h1>
        
        {/* 测试按钮 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <Button
            onClick={() => testGetAllMajors('本科(普通)')}
            disabled={loading}
            variant={activeTest.includes('本科(普通)') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('本科(普通)') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            本科(普通)专业
          </Button>
          
          <Button
            onClick={() => testGetAllMajors('本科(职业)')}
            disabled={loading}
            variant={activeTest.includes('本科(职业)') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('本科(职业)') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            本科(职业)专业
          </Button>
          
          <Button
            onClick={() => testGetAllMajors('专科(高职)')}
            disabled={loading}
            variant={activeTest.includes('专科(高职)') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('专科(高职)') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            专科(高职)专业
          </Button>
          
          <Button
            onClick={() => testSearchMajors('计算机', '本科(普通)')}
            disabled={loading}
            variant={activeTest.includes('计算机') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('计算机') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            搜索"计算机"
          </Button>
          
          <Button
            onClick={() => testSearchMajors('医学', '本科(普通)')}
            disabled={loading}
            variant={activeTest.includes('医学') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('医学') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            搜索"医学"
          </Button>
          
          <Button
            onClick={() => testSearchMajors('护理', '专科(高职)')}
            disabled={loading}
            variant={activeTest.includes('护理') ? 'default' : 'outline'}
          >
            {loading && activeTest.includes('护理') && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            搜索"护理"
          </Button>
        </div>

        {/* 错误显示 */}
        {error && (
          <Card className="p-4 mb-6 border-red-200 bg-red-50">
            <h3 className="text-lg font-semibold text-red-800 mb-2">❌ 测试失败</h3>
            <p className="text-red-600">{error}</p>
          </Card>
        )}

        {/* 结果显示 */}
        {result && (
          <Card className="p-4 border-green-200 bg-green-50">
            <h3 className="text-lg font-semibold text-green-800 mb-2">✅ {result.message}</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">原始API响应:</h4>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(result.data.raw, null, 2)}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-800 mb-2">处理后的专业数据 (前5个):</h4>
                <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-60">
                  {JSON.stringify(result.data.processed, null, 2)}
                </pre>
              </div>
            </div>
          </Card>
        )}

        {/* 使用说明 */}
        <Card className="p-4 mt-6 bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">📋 测试说明</h3>
          <ul className="text-blue-700 space-y-1 text-sm">
            <li>• 点击按钮测试不同教育层次的专业数据获取</li>
            <li>• 测试搜索功能，验证关键词搜索是否正常</li>
            <li>• 查看原始API响应和处理后的数据格式</li>
            <li>• 如果测试失败，请检查网络连接和API接口状态</li>
          </ul>
        </Card>
      </Card>
    </div>
  )
}
