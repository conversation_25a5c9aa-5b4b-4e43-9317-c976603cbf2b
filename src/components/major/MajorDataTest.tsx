import { useState } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { flattenMajorTree, transformApiDataToMajor, extractCategoriesFromApiData, getMajorTree } from '../../services/majorTreeApi'

// 模拟API返回的数据结构（基于data/major.json）
const mockApiData = {
  code: 0,
  data: {
    currentCategory: "农学",
    tree: {
      categories: [
        {
          id: "农学",
          name: "农学",
          children: [
            {
              id: "动物医学类",
              name: "动物医学类",
              children: [
                {
                  id: 523,
                  name: "动物医学",
                  code: "090401",
                  educationLevel: "本科（普通教育）",
                  isRecommended: false,
                  careerDirection: null,
                  majorIntroduction: "本专业培养德、智、体、美全面发展，具备小动物疾病防治、牧场兽医、兽医公共卫生、兽医医学等方面的基本理论、基本知识和基本技能...",
                  graduateScale: "10000-12000人",
                  maleFemaleRatio: "38:62",
                  recommendSchools: [
                    "四川农业大学",
                    "西南民族大学",
                    "河南农业大学",
                    "甘肃农业大学",
                    "黑龙江八一农垦大学"
                  ],
                  courses: [
                    {
                      courseName: "兽医影像学",
                      courseDifficulty: "4"
                    },
                    {
                      courseName: "兽医传染病学",
                      courseDifficulty: "4"
                    },
                    {
                      courseName: "兽医外科手术学",
                      courseDifficulty: "3"
                    }
                  ],
                  disciplinaryCategory: "农学",
                  disciplinarySubCategory: "动物医学类",
                  createTime: 1746628343000
                }
              ]
            }
          ]
        }
      ]
    }
  }
}

export function MajorDataTest() {
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const testDataTransformation = async () => {
    try {
      console.log('🧪 测试数据转换...')

      // 首先尝试从实际的major.json文件加载数据
      let apiData = mockApiData.data
      try {
        const response = await fetch('/data/major.json')
        const jsonData = await response.json()
        if (jsonData.data) {
          apiData = jsonData.data
          console.log('✅ 成功加载实际的major.json数据')
        }
      } catch (fetchError) {
        console.log('⚠️ 无法加载major.json，使用模拟数据')
      }

      console.log('原始API数据:', apiData)

      // 1. 提取所有学科门类信息
      const { categories, categoryStats } = extractCategoriesFromApiData(apiData)
      console.log('所有学科门类:', categories)
      console.log('学科门类统计:', categoryStats)

      // 2. 扁平化数据（只包含已加载的专业）
      const flatMajors = flattenMajorTree(apiData)
      console.log('扁平化后的数据:', flatMajors)

      // 3. 转换为组件格式
      const transformedMajors = flatMajors.map(transformApiDataToMajor)
      console.log('转换后的数据:', transformedMajors)

      setResult({
        original: apiData,
        categories,
        categoryStats,
        flattened: flatMajors,
        transformed: transformedMajors
      })
      setError(null)
    } catch (err) {
      console.error('❌ 数据转换失败:', err)
      setError(err instanceof Error ? err.message : '转换失败')
      setResult(null)
    }
  }

  const testCategoryApi = async (categoryId: string) => {
    try {
      console.log(`🧪 测试${categoryId}分类API调用...`)
      setError(null)

      const response = await getMajorTree('', categoryId, '本科(普通)')
      console.log(`✅ ${categoryId}分类API调用成功:`, response)

      // 处理返回的数据
      const flatMajors = flattenMajorTree(response.data)
      const transformedMajors = flatMajors.map(transformApiDataToMajor)

      setResult({
        original: response.data,
        categories: [categoryId],
        categoryStats: [{category: categoryId, count: transformedMajors.length}],
        flattened: flatMajors,
        transformed: transformedMajors
      })

      console.log(`📊 ${categoryId}分类专业数量:`, transformedMajors.length)
    } catch (err) {
      console.error(`❌ ${categoryId}分类API调用失败:`, err)
      setError(`${categoryId}分类API调用失败: ${err instanceof Error ? err.message : '未知错误'}`)
      setResult(null)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">专业数据转换测试</h1>
          <div className="space-x-4">
            <Button onClick={testDataTransformation}>
              测试数据转换
            </Button>
            <Button onClick={() => testCategoryApi('医学')} variant="outline">
              测试医学分类API
            </Button>
            <Button onClick={() => testCategoryApi('工学')} variant="outline">
              测试工学分类API
            </Button>
          </div>
        </div>

        {error && (
          <Card className="p-4 mb-6 bg-red-50 border-red-200">
            <div className="text-red-800">
              <strong>错误:</strong> {error}
            </div>
          </Card>
        )}

        {result && (
          <div className="space-y-6">
            <Card className="p-6">
              <h2 className="text-xl font-bold mb-4">学科门类信息</h2>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h3 className="font-semibold mb-2">所有学科门类 ({result.categories?.length || 0}个)</h3>
                  <div className="bg-blue-50 p-3 rounded">
                    {result.categories?.map((category: string, index: number) => (
                      <div key={index} className="text-sm text-blue-800">
                        {index + 1}. {category}
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">专业数量统计</h3>
                  <div className="bg-green-50 p-3 rounded">
                    {result.categoryStats?.map((stat: any, index: number) => (
                      <div key={index} className="text-sm text-green-800 flex justify-between">
                        <span>{stat.category}</span>
                        <span className="font-bold">{stat.count}个</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h2 className="text-xl font-bold mb-4">扁平化数据 ({result.flattened?.length || 0}个专业)</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-60">
                {JSON.stringify(result.flattened, null, 2)}
              </pre>
            </Card>

            <Card className="p-6">
              <h2 className="text-xl font-bold mb-4">转换后数据</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-60">
                {JSON.stringify(result.transformed, null, 2)}
              </pre>
            </Card>

            {result.transformed && result.transformed.length > 0 && (
              <Card className="p-6">
                <h2 className="text-xl font-bold mb-4">转换结果验证</h2>
                <div className="space-y-2">
                  <div><strong>专业名称:</strong> {result.transformed[0].name}</div>
                  <div><strong>专业代码:</strong> {result.transformed[0].code}</div>
                  <div><strong>学科门类:</strong> {result.transformed[0].category}</div>
                  <div><strong>专业类别:</strong> {result.transformed[0].subCategory}</div>
                  <div><strong>毕业生规模:</strong> {result.transformed[0].graduateScale}</div>
                  <div><strong>推荐院校数量:</strong> {result.transformed[0].recommendSchools?.length || 0}</div>
                  <div><strong>课程数量:</strong> {(result.transformed[0].courses?.core?.length || 0) + (result.transformed[0].courses?.elective?.length || 0)}</div>
                  <div><strong>就业率:</strong> {result.transformed[0].employment?.rate}%</div>
                  <div><strong>平均薪资:</strong> ¥{result.transformed[0].employment?.avgSalary?.toLocaleString()}</div>
                </div>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
