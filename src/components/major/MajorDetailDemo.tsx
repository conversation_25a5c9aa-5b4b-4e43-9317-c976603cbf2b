import { useState } from 'react'
import { MajorDetailPageNew } from './MajorDetailPageNew'
import { mockMajors } from '../../data/majors'
import { Button } from '../ui/button'
import { Card } from '../ui/card'

export function MajorDetailDemo() {
  const [selectedMajorId, setSelectedMajorId] = useState<string>('2') // 默认选择计算机科学与技术
  
  const selectedMajor = mockMajors.find(major => major.id === selectedMajorId)
  
  if (!selectedMajor) {
    return <div>专业未找到</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 专业选择器 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="container mx-auto">
          <h2 className="text-lg font-bold text-gray-900 mb-4">选择专业查看详情</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
            {mockMajors.slice(0, 12).map((major) => (
              <Button
                key={major.id}
                variant={selectedMajorId === major.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedMajorId(major.id)}
                className="text-xs"
              >
                {major.name}
              </Button>
            ))}
          </div>
        </div>
      </div>
      
      {/* 专业详情页面 */}
      <MajorDetailPageNew 
        major={selectedMajor}
        onBack={() => console.log('返回上一页')}
      />
    </div>
  )
}
