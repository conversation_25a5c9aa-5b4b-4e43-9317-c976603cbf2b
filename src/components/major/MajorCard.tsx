import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import {
  GraduationCap,
  Clock,
  TrendingUp,
  Users,
  MapPin,
  ChevronRight,
  Briefcase,
  DollarSign
} from 'lucide-react'

// 临时类型定义
interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: string
  subCategory?: string
  degree: string
  duration: number
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
  // 新增字段
  graduateScale?: string
  recommendSchools?: string[]
  careerDirection?: string
  isRecommended?: boolean
}

interface MajorCardProps {
  major: Major
  onClick?: () => void
  className?: string
}

export function MajorCard({ major, onClick, className }: MajorCardProps) {
  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `¥${(salary / 10000).toFixed(1)}万`
    }
    return `¥${salary.toLocaleString()}`
  }

  const getGenderRatioDisplay = () => {
    if (!major.genderRatio) return null
    const { male, female } = major.genderRatio
    return `男${male.toFixed(0)}% : 女${female.toFixed(0)}%`
  }

  const getTrendIcon = () => {
    if (!major.prospects) return null
    switch (major.prospects.trend) {
      case 'rising':
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'stable':
        return <div className="w-4 h-4 bg-blue-600 rounded-full" />
      case 'declining':
        return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />
      default:
        return null
    }
  }

  return (
    <Card 
      className={cn(
        "p-6 hover:shadow-lg transition-all duration-200 cursor-pointer border border-gray-200 hover:border-blue-300",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-lg font-bold text-gray-900">{major.name}</h3>
            <span className="text-sm text-gray-500">({major.code})</span>
            {getTrendIcon()}
          </div>
          
          {major.englishName && (
            <p className="text-sm text-gray-600 mb-2">{major.englishName}</p>
          )}
          
          <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{major.duration}年</span>
            </div>
            <div className="flex items-center gap-1">
              <GraduationCap className="w-4 h-4" />
              <span>{major.degree}学位</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{major.category}</span>
            </div>
          </div>
        </div>
        
        <Button variant="ghost" size="sm" className="flex-shrink-0">
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>

      {/* 就业信息 */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        {major.employment?.rate && (
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Briefcase className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">就业率</span>
            </div>
            <div className="text-lg font-bold text-green-900">
              {major.employment.rate.toFixed(1)}%
            </div>
          </div>
        )}

        {major.employment?.avgSalary && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <DollarSign className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">平均薪资</span>
            </div>
            <div className="text-lg font-bold text-blue-900">
              {formatSalary(major.employment.avgSalary)}
            </div>
          </div>
        )}

        {major.graduateScale && (
          <div className="bg-orange-50 p-3 rounded-lg col-span-2">
            <div className="flex items-center gap-2 mb-1">
              <Users className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-800">毕业生规模</span>
            </div>
            <div className="text-lg font-bold text-orange-900">
              {major.graduateScale}
            </div>
          </div>
        )}
      </div>

      {/* 性别比例 */}
      {major.genderRatio && (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">性别比例</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2 overflow-hidden">
              <div 
                className="h-full bg-blue-500 transition-all duration-300"
                style={{ width: `${major.genderRatio.male}%` }}
              />
            </div>
            <span className="text-sm text-gray-600 min-w-fit">
              {getGenderRatioDisplay()}
            </span>
          </div>
        </div>
      )}

      {/* 专业特色 */}
      {major.features && major.features.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {major.features.slice(0, 3).map((feature, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-orange-50 text-orange-700 rounded text-xs font-medium"
              >
                {feature}
              </span>
            ))}
            {major.features.length > 3 && (
              <span className="px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs">
                +{major.features.length - 3}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 主要就业行业 */}
      {major.employment?.topIndustries && major.employment.topIndustries.length > 0 && (
        <div>
          <div className="text-sm font-medium text-gray-700 mb-2">主要就业行业</div>
          <div className="flex flex-wrap gap-1">
            {major.employment.topIndustries.slice(0, 3).map((industry, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-purple-50 text-purple-700 rounded text-xs"
              >
                {industry}
              </span>
            ))}
            {major.employment.topIndustries.length > 3 && (
              <span className="px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs">
                +{major.employment.topIndustries.length - 3}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 专业描述 */}
      {major.description && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <p className="text-sm text-gray-600 line-clamp-2">
            {major.description}
          </p>
        </div>
      )}
    </Card>
  )
}
