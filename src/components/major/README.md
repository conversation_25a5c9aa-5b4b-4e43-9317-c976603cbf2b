# 专业查询功能

## 功能概述

专业查询功能是一个完整的专业信息查询和筛选系统，帮助用户快速找到合适的专业信息。

## 主要功能

### 1. 专业搜索
- **关键词搜索**: 支持专业名称、英文名称搜索
- **实时搜索**: 输入即时显示搜索结果
- **智能匹配**: 支持模糊搜索和部分匹配

### 2. 学科分类
- **12个学科门类**: 工学、医学、文学、管理学、理学、经济学、法学、艺术学、教育学、农学、历史学、哲学
- **分类统计**: 显示每个学科的专业数量、平均薪资、就业率
- **快速筛选**: 点击分类快速筛选相关专业

### 3. 高级筛选
- **学位类型**: 学士、硕士、博士
- **学制筛选**: 3-8年学制选择
- **薪资范围**: 自定义薪资区间筛选
- **就业率**: 设置最低就业率要求
- **性别比例**: 男性较多、女性较多、比例均衡

### 4. 排序功能
- **多种排序方式**: 专业名称、平均薪资、就业率、热门程度
- **升序/降序**: 支持正序和倒序排列
- **智能排序**: 热门程度综合考虑就业率和薪资

### 5. 专业信息展示
- **基本信息**: 专业名称、代码、学制、学位、学科分类
- **就业数据**: 就业率、平均薪资、薪资范围
- **性别比例**: 可视化显示男女比例
- **就业方向**: 主要就业行业和岗位
- **专业特色**: 专业亮点和特色标签

### 6. 推荐功能
- **推荐院校**: 显示开设该专业的优质院校
- **院校排名**: 显示院校全国排名和录取分数
- **热门专业**: 展示当前最受关注的专业

## 组件结构

```
src/components/major/
├── MajorSearchPage.tsx      # 主页面组件
├── MajorCard.tsx           # 专业卡片组件
├── MajorDetailPage.tsx     # 专业详情页面
├── CategorySidebar.tsx     # 学科分类侧边栏
├── MajorFilters.tsx        # 筛选组件
├── RecommendedUniversities.tsx # 推荐院校组件
├── test-functionality.md   # 功能测试清单
└── README.md              # 说明文档
```

## 数据结构

### Major 接口
```typescript
interface Major {
  id: string
  code: string              // 专业代码
  name: string             // 专业名称
  englishName?: string     // 英文名称
  category: DisciplineCategory // 学科门类
  subCategory?: string     // 专业类别
  degree: DegreeType       // 学位类型
  duration: number         // 学制
  description?: string     // 专业描述
  employment?: {           // 就业信息
    rate?: number
    avgSalary?: number
    salaryRange?: { min: number, max: number }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {          // 性别比例
    male: number
    female: number
  }
  courses?: {              // 课程信息
    core?: string[]
    elective?: string[]
  }
  prospects?: {            // 发展前景
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
}
```

## 使用方法

### 1. 基本使用
```tsx
import { MajorSearchPage } from './components/major/MajorSearchPage'

function App() {
  return (
    <MajorSearchPage onBack={() => console.log('返回')} />
  )
}
```

### 2. 专业卡片
```tsx
import { MajorCard } from './components/major/MajorCard'

function MajorList({ majors }) {
  return (
    <div>
      {majors.map(major => (
        <MajorCard 
          key={major.id} 
          major={major} 
          onClick={() => console.log('查看详情')} 
        />
      ))}
    </div>
  )
}
```

## 样式定制

专业查询功能使用了自定义CSS类，可以通过修改样式来定制外观：

```css
/* 专业卡片样式 */
.major-card {
  transition: all 0.2s ease-in-out;
  border: 1px solid #e5e7eb;
}

.major-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 学科分类按钮样式 */
.category-button {
  transition: all 0.2s ease-in-out;
}

.category-button:hover {
  transform: translateX(4px);
}
```

## 响应式设计

- **桌面端**: 三栏布局（分类 + 内容 + 推荐）
- **平板端**: 两栏布局（内容 + 侧边栏）
- **移动端**: 单栏布局，侧边栏折叠

## 性能优化

- **分页加载**: 每页显示10个专业，避免一次性加载过多数据
- **搜索防抖**: 搜索输入有延迟，避免频繁请求
- **虚拟滚动**: 大量数据时使用虚拟滚动提升性能
- **懒加载**: 图片和非关键内容懒加载

## 扩展功能

### 计划中的功能
1. **专业对比**: 支持多个专业对比分析
2. **个性化推荐**: 基于用户兴趣推荐专业
3. **数据导出**: 支持导出专业信息到Excel
4. **收藏功能**: 用户可以收藏感兴趣的专业
5. **历年数据**: 显示专业的历年就业和薪资趋势

### 数据来源
- 教育部专业目录
- 各高校招生信息
- 就业统计数据
- 薪资调研报告

## 技术栈

- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库
- **Vite**: 构建工具
