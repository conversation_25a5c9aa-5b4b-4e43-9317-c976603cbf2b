import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import { Major } from '../../types/major'
import { 
  ArrowLeft,
  GraduationCap,
  Clock,
  TrendingUp,
  Users,
  MapPin,
  Briefcase,
  DollarSign,
  BookOpen,
  Award,
  Building,
  Star,
  ChevronRight,
  ExternalLink,
  BarChart3
} from 'lucide-react'

interface MajorDetailPageProps {
  major: Major
  onBack?: () => void
  className?: string
}

export function MajorDetailPage({ major, onBack, className }: MajorDetailPageProps) {
  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `¥${(salary / 10000).toFixed(1)}万`
    }
    return `¥${salary.toLocaleString()}`
  }

  const getGenderRatioDisplay = () => {
    if (!major.genderRatio) return null
    const { male, female } = major.genderRatio
    return `男${male.toFixed(0)}% : 女${female.toFixed(0)}%`
  }

  const getTrendIcon = () => {
    if (!major.prospects) return null
    switch (major.prospects.trend) {
      case 'rising':
        return <TrendingUp className="w-5 h-5 text-green-600" />
      case 'stable':
        return <div className="w-5 h-5 bg-blue-600 rounded-full" />
      case 'declining':
        return <TrendingUp className="w-5 h-5 text-red-600 rotate-180" />
      default:
        return null
    }
  }

  const getTrendText = () => {
    if (!major.prospects) return null
    switch (major.prospects.trend) {
      case 'rising':
        return '上升趋势'
      case 'stable':
        return '稳定发展'
      case 'declining':
        return '下降趋势'
      default:
        return null
    }
  }

  const getDemandLevelText = (level: string) => {
    switch (level) {
      case 'high':
        return '需求旺盛'
      case 'medium':
        return '需求一般'
      case 'low':
        return '需求较少'
      default:
        return level
    }
  }

  const getCompetitionLevelText = (level: string) => {
    switch (level) {
      case 'high':
        return '竞争激烈'
      case 'medium':
        return '竞争适中'
      case 'low':
        return '竞争较小'
      default:
        return level
    }
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* 顶部导航 */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  返回
                </Button>
              )}
              <div className="flex items-center gap-2">
                <GraduationCap className="w-6 h-6 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">{major.name}</h1>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* 专业头部信息 */}
        <Card className="p-8 mb-6">
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold text-gray-900">{major.name}</h1>
                <span className="text-lg text-gray-500">({major.code})</span>
                {getTrendIcon()}
              </div>
              
              {major.englishName && (
                <p className="text-lg text-gray-600 mb-4">{major.englishName}</p>
              )}
              
              <div className="flex items-center gap-6 text-sm text-gray-600 mb-6">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>学制 {major.duration} 年</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="w-4 h-4" />
                  <span>{major.degree}学位</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{major.category} - {major.subCategory}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 核心指标 */}
          {major.employment && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <Briefcase className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">就业率</span>
                </div>
                <div className="text-2xl font-bold text-green-900">
                  {major.employment.rate?.toFixed(1)}%
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-800">平均薪资</span>
                </div>
                <div className="text-2xl font-bold text-blue-900">
                  {major.employment.avgSalary ? formatSalary(major.employment.avgSalary) : 'N/A'}
                </div>
              </div>

              {major.genderRatio && (
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    <span className="font-medium text-purple-800">性别比例</span>
                  </div>
                  <div className="text-lg font-bold text-purple-900">
                    {getGenderRatioDisplay()}
                  </div>
                </div>
              )}

              {major.prospects && (
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="w-5 h-5 text-orange-600" />
                    <span className="font-medium text-orange-800">发展趋势</span>
                  </div>
                  <div className="text-lg font-bold text-orange-900">
                    {getTrendText()}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 专业特色 */}
          {major.features && major.features.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">专业特色</h3>
              <div className="flex flex-wrap gap-2">
                {major.features.map((feature, index) => (
                  <span 
                    key={index}
                    className="px-3 py-1 bg-orange-50 text-orange-700 rounded-full text-sm font-medium border border-orange-200"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          )}
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 专业介绍 */}
            {major.description && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  专业介绍
                </h2>
                <p className="text-gray-700 leading-relaxed">{major.description}</p>
              </Card>
            )}

            {/* 课程设置 */}
            {major.courses && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  课程设置
                </h2>
                
                {major.courses.core && major.courses.core.length > 0 && (
                  <div className="mb-4">
                    <h3 className="font-medium text-gray-900 mb-2">核心课程</h3>
                    <div className="flex flex-wrap gap-2">
                      {major.courses.core.map((course, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-blue-50 text-blue-700 rounded text-sm border border-blue-200"
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {major.courses.elective && major.courses.elective.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">选修课程</h3>
                    <div className="flex flex-wrap gap-2">
                      {major.courses.elective.map((course, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-gray-50 text-gray-700 rounded text-sm border border-gray-200"
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            )}

            {/* 就业前景 */}
            {major.employment && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  就业前景
                </h2>
                
                {major.employment.salaryRange && (
                  <div className="mb-4">
                    <h3 className="font-medium text-gray-900 mb-2">薪资范围</h3>
                    <p className="text-gray-700">
                      {formatSalary(major.employment.salaryRange.min)} - {formatSalary(major.employment.salaryRange.max)}
                    </p>
                  </div>
                )}

                {major.employment.topIndustries && major.employment.topIndustries.length > 0 && (
                  <div className="mb-4">
                    <h3 className="font-medium text-gray-900 mb-2">主要就业行业</h3>
                    <div className="flex flex-wrap gap-2">
                      {major.employment.topIndustries.map((industry, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-purple-50 text-purple-700 rounded text-sm border border-purple-200"
                        >
                          {industry}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {major.employment.topPositions && major.employment.topPositions.length > 0 && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">主要就业岗位</h3>
                    <div className="flex flex-wrap gap-2">
                      {major.employment.topPositions.map((position, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-green-50 text-green-700 rounded text-sm border border-green-200"
                        >
                          {position}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            )}
          </div>

          {/* 右侧信息 */}
          <div className="space-y-6">
            {/* 发展前景 */}
            {major.prospects && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">发展前景</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">发展趋势</span>
                    <span className="font-medium">{getTrendText()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">市场需求</span>
                    <span className="font-medium">{getDemandLevelText(major.prospects.demandLevel)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">竞争程度</span>
                    <span className="font-medium">{getCompetitionLevelText(major.prospects.competitionLevel)}</span>
                  </div>
                </div>
              </Card>
            )}

            {/* 相关院校 */}
            {major.universities && major.universities.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">开设院校</h3>
                <div className="space-y-3">
                  {major.universities.slice(0, 5).map((university, index) => (
                    <div 
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                    >
                      <div>
                        <div className="font-medium text-gray-900">{university.name}</div>
                        {university.ranking && (
                          <div className="text-sm text-gray-600">排名: #{university.ranking}</div>
                        )}
                      </div>
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  ))}
                  
                  {major.universities.length > 5 && (
                    <Button variant="outline" className="w-full" size="sm">
                      查看更多院校
                    </Button>
                  )}
                </div>
              </Card>
            )}

            {/* 相关链接 */}
            <Card className="p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">相关链接</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  专业详细介绍
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Building className="w-4 h-4 mr-2" />
                  开设院校查询
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Star className="w-4 h-4 mr-2" />
                  专业排名
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
