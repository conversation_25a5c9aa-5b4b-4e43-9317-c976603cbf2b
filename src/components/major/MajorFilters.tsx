import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'

interface MajorSearchFilters {
  keyword?: string
  sortBy?: 'name' | 'salary' | 'employment' | 'popularity'
  sortOrder?: 'asc' | 'desc'
}
import {
  Search,
  SortAsc,
  SortDesc
} from 'lucide-react'

interface MajorFiltersProps {
  filters: MajorSearchFilters
  onFiltersChange: (filters: MajorSearchFilters) => void
  className?: string
}

export function MajorFilters({ filters, onFiltersChange, className }: MajorFiltersProps) {
  const updateFilter = <K extends keyof MajorSearchFilters>(
    key: K,
    value: MajorSearchFilters[K]
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const sortOptions = [
    { value: 'name', label: '专业名称' },
    { value: 'salary', label: '平均薪资' },
    { value: 'employment', label: '就业率' },
    { value: 'popularity', label: '热门程度' }
  ] as const

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索框 */}
      <Card className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="搜索专业名称..."
            value={filters.keyword || ''}
            onChange={(e) => updateFilter('keyword', e.target.value || undefined)}
            className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          />
        </div>
      </Card>



      {/* 排序选项 */}
      <Card className="p-4">
        <h4 className="font-bold text-gray-900 mb-3">排序方式</h4>
        <div className="flex items-center gap-2">
          <select
            value={filters.sortBy || 'name'}
            onChange={(e) => updateFilter('sortBy', e.target.value as any)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {filters.sortOrder === 'asc' ? (
              <SortAsc className="w-4 h-4" />
            ) : (
              <SortDesc className="w-4 h-4" />
            )}
          </Button>
        </div>
      </Card>


    </div>
  )
}
