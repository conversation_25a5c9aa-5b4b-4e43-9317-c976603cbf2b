import { Card } from '../ui/card'
import { Button } from '../ui/button'
import { cn } from '../../lib/utils'
import { 
  ArrowLeft,
  GraduationCap,
  Clock,
  TrendingUp,
  Users,
  MapPin,
  Briefcase,
  DollarSign,
  BookOpen,
  Award,
  Building,
  Star,
  ChevronRight,
  ExternalLink,
  BarChart3,
  Home,
  Target,
  Calendar,
  TrendingDown,
  Minus
} from 'lucide-react'

// 类型定义
type DisciplineCategory =
  | '哲学'
  | '经济学'
  | '法学'
  | '教育学'
  | '文学'
  | '历史学'
  | '理学'
  | '工学'
  | '农学'
  | '医学'
  | '管理学'
  | '艺术学'

type EducationLevel = '本科(普通)' | '本科(职业)' | '专科(高职)'

interface Major {
  id: string
  code: string
  name: string
  englishName?: string
  category: DisciplineCategory
  subCategory?: string
  degree: string
  duration: number
  educationLevel: EducationLevel
  description?: string
  employment?: {
    rate?: number
    avgSalary?: number
    salaryRange?: {
      min: number
      max: number
    }
    topIndustries?: string[]
    topPositions?: string[]
  }
  genderRatio?: {
    male: number
    female: number
  }
  courses?: {
    core?: string[]
    elective?: string[]
  }
  features?: string[]
  prospects?: {
    trend: 'rising' | 'stable' | 'declining'
    demandLevel: 'high' | 'medium' | 'low'
    competitionLevel: 'high' | 'medium' | 'low'
  }
  // 新增字段
  graduateScale?: string
  recommendSchools?: string[]
  careerDirection?: string
  isRecommended?: boolean
}

interface MajorDetailPageNewProps {
  major: Major
  onBack?: () => void
  className?: string
}

export function MajorDetailPageNew({ major, onBack, className }: MajorDetailPageNewProps) {
  const formatSalary = (salary: number) => {
    if (salary >= 10000) {
      return `${(salary / 10000).toFixed(1)}万`
    }
    return `${salary.toLocaleString()}`
  }

  const getGenderRatioDisplay = () => {
    if (!major.genderRatio) return null
    const { male, female } = major.genderRatio
    return { male: male.toFixed(1), female: female.toFixed(1) }
  }

  const getTrendIcon = () => {
    if (!major.prospects) return <Minus className="w-4 h-4 text-gray-400" />
    switch (major.prospects.trend) {
      case 'rising':
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'stable':
        return <Minus className="w-4 h-4 text-blue-600" />
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-600" />
      default:
        return <Minus className="w-4 h-4 text-gray-400" />
    }
  }

  const getDemandLevelText = (level: string) => {
    switch (level) {
      case 'high': return '需求旺盛'
      case 'medium': return '需求一般'
      case 'low': return '需求较少'
      default: return '未知'
    }
  }

  const getCompetitionLevelText = (level: string) => {
    switch (level) {
      case 'high': return '竞争激烈'
      case 'medium': return '竞争适中'
      case 'low': return '竞争较小'
      default: return '未知'
    }
  }

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center h-14 text-sm text-gray-600">
            <Home className="w-4 h-4 mr-1" />
            <span>首页</span>
            <ChevronRight className="w-4 h-4 mx-2" />
            <span>专业查询</span>
            <ChevronRight className="w-4 h-4 mx-2" />
            <span className="text-gray-900">{major.name}</span>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧主内容 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 专业头部信息 */}
            <Card className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-2xl font-bold text-gray-900">{major.name}</h1>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {major.code}
                    </span>
                    {getTrendIcon()}
                  </div>
                  
                  {major.englishName && (
                    <p className="text-gray-600 mb-3">{major.englishName}</p>
                  )}
                  
                  <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <GraduationCap className="w-4 h-4" />
                      <span>{major.category} &gt; {major.subCategory}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Award className="w-4 h-4" />
                      <span>{major.degree}学位</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>学制{major.duration}年</span>
                    </div>
                  </div>
                </div>
                
                {onBack && (
                  <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
                    <ArrowLeft className="w-4 h-4" />
                    返回
                  </Button>
                )}
              </div>
              
              {/* 关键数据展示 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
                {major.employment?.rate && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {major.employment.rate.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600">就业率</div>
                  </div>
                )}
                
                {major.employment?.avgSalary && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatSalary(major.employment.avgSalary)}
                    </div>
                    <div className="text-sm text-gray-600">平均薪资</div>
                  </div>
                )}
                
                {major.genderRatio && (
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">
                      {getGenderRatioDisplay()?.male}% : {getGenderRatioDisplay()?.female}%
                    </div>
                    <div className="text-sm text-gray-600">男女比例</div>
                  </div>
                )}
                
                {major.graduateScale ? (
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">
                      {major.graduateScale}
                    </div>
                    <div className="text-sm text-gray-600">毕业生规模</div>
                  </div>
                ) : major.prospects ? (
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">
                      {getDemandLevelText(major.prospects.demandLevel)}
                    </div>
                    <div className="text-sm text-gray-600">市场需求</div>
                  </div>
                ) : null}
              </div>
            </Card>

            {/* 专业介绍 */}
            <Card className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-blue-600" />
                专业介绍
              </h2>
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {major.description || '该专业致力于培养具有扎实理论基础和实践能力的专业人才，适应社会发展需要，具备创新精神和实践能力。'}
                </p>
              </div>
            </Card>

            {/* 就业前景 */}
            <Card className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Briefcase className="w-5 h-5 text-green-600" />
                就业前景
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 就业行业 */}
                {major.employment?.topIndustries && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">主要就业行业</h3>
                    <div className="space-y-2">
                      {major.employment.topIndustries.map((industry, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-gray-700">{industry}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* 就业岗位 */}
                {major.employment?.topPositions && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-3">主要就业岗位</h3>
                    <div className="space-y-2">
                      {major.employment.topPositions.map((position, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                          <span className="text-gray-700">{position}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {/* 薪资范围 */}
              {major.employment?.salaryRange && (
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-semibold text-gray-900 mb-2">薪资范围</h3>
                  <div className="flex items-center gap-4">
                    <span className="text-gray-700">
                      {formatSalary(major.employment.salaryRange.min)} - {formatSalary(major.employment.salaryRange.max)}
                    </span>
                    <span className="text-sm text-gray-500">
                      (平均: {major.employment.avgSalary ? formatSalary(major.employment.avgSalary) : '未知'})
                    </span>
                  </div>
                </div>
              )}
            </Card>

            {/* 主要课程 */}
            {major.courses && (
              <Card className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-purple-600" />
                  主要课程
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {major.courses.core && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">核心课程</h3>
                      <div className="flex flex-wrap gap-2">
                        {major.courses.core.map((course, index) => (
                          <span 
                            key={index}
                            className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                          >
                            {course}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {major.courses.elective && (
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-3">选修课程</h3>
                      <div className="flex flex-wrap gap-2">
                        {major.courses.elective.map((course, index) => (
                          <span 
                            key={index}
                            className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                          >
                            {course}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            )}
          </div>

          {/* 右侧侧边栏 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 专业特色 */}
            {major.features && (
              <Card className="p-4">
                <h3 className="font-bold text-gray-900 mb-3">专业特色</h3>
                <div className="space-y-2">
                  {major.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* 发展前景 */}
            {major.prospects && (
              <Card className="p-4">
                <h3 className="font-bold text-gray-900 mb-3">发展前景</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">发展趋势</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon()}
                      <span className="text-sm font-medium">
                        {major.prospects.trend === 'rising' ? '上升' : 
                         major.prospects.trend === 'stable' ? '稳定' : '下降'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">市场需求</span>
                    <span className="text-sm font-medium">
                      {getDemandLevelText(major.prospects.demandLevel)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">竞争程度</span>
                    <span className="text-sm font-medium">
                      {getCompetitionLevelText(major.prospects.competitionLevel)}
                    </span>
                  </div>
                </div>
              </Card>
            )}

            {/* 快速导航 */}
            <Card className="p-4">
              <h3 className="font-bold text-gray-900 mb-3">快速导航</h3>
              <div className="space-y-2">
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Building className="w-4 h-4 mr-2" />
                  开设院校
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <Users className="w-4 h-4 mr-2" />
                  相关专业
                </Button>
                <Button variant="ghost" size="sm" className="w-full justify-start">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  数据分析
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* 底部推荐内容 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
          {/* 推荐院校 */}
          <Card className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <Building className="w-5 h-5 text-blue-600" />
              开设该专业的推荐院校
            </h2>
            <div className="space-y-4">
              {major.recommendSchools && major.recommendSchools.length > 0 ? (
                major.recommendSchools.slice(0, 8).map((schoolName: string, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{schoolName}</div>
                        <div className="text-sm text-gray-600">
                          <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-xs mr-2">
                            推荐院校
                          </span>
                          开设{major.name}专业
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      查看详情
                    </Button>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Building className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>暂无推荐院校信息</p>
                </div>
              )}
            </div>
          </Card>

          {/* 相关专业推荐 */}
          <Card className="p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-green-600" />
              相关专业推荐
            </h2>
            <div className="space-y-4">
              {[
                { name: '软件工程', similarity: 95, category: '工学' },
                { name: '网络工程', similarity: 88, category: '工学' },
                { name: '信息安全', similarity: 85, category: '工学' },
                { name: '数据科学与大数据技术', similarity: 82, category: '工学' },
                { name: '人工智能', similarity: 80, category: '工学' }
              ].map((relatedMajor, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {relatedMajor.similarity}%
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{relatedMajor.name}</div>
                      <div className="text-sm text-gray-600">
                        <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-xs">
                          {relatedMajor.category}
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    查看详情
                  </Button>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
