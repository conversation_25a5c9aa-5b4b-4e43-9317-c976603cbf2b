import { useState } from 'react'
import { CategorySidebar } from './CategorySidebar'

// 模拟不同教育层次的分类数据
const mockCategories = {
  '本科(普通)': [
    '工学', '医学', '文学', '管理学', '理学', '经济学', 
    '法学', '艺术学', '教育学', '农学', '历史学', '哲学'
  ],
  '本科(职业)': [
    '交通运输', '公共管理与服务', '公安与司法', '农林牧渔', 
    '医药卫生', '土木建筑', '教育与体育', '文化艺术', 
    '新闻传播', '旅游', '水利', '生物与化工', '电子与信息', 
    '能源动力与材料', '装备制造', '财经商贸', '资源环境与安全', 
    '轻工纺织', '食品药品与粮食'
  ],
  '专科(高职)': [
    '交通运输', '公共管理与服务', '公安与司法', '农林牧渔', 
    '医药卫生', '土木建筑', '教育与体育', '文化艺术', 
    '新闻传播', '旅游', '水利', '生物与化工', '电子与信息', 
    '能源动力与材料', '装备制造', '财经商贸', '资源环境与安全', 
    '轻工纺织', '食品药品与粮食'
  ]
}

const mockCategoryStats = {
  '本科(普通)': [
    { category: '工学', count: 120 },
    { category: '医学', count: 45 },
    { category: '文学', count: 30 },
    { category: '管理学', count: 85 },
    { category: '理学', count: 65 },
    { category: '经济学', count: 25 },
    { category: '法学', count: 15 },
    { category: '艺术学', count: 35 },
    { category: '教育学', count: 20 },
    { category: '农学', count: 18 },
    { category: '历史学', count: 8 },
    { category: '哲学', count: 5 }
  ],
  '本科(职业)': [
    { category: '交通运输', count: 21 },
    { category: '公共管理与服务', count: 9 },
    { category: '公安与司法', count: 10 },
    { category: '农林牧渔', count: 13 },
    { category: '医药卫生', count: 20 },
    { category: '土木建筑', count: 18 },
    { category: '教育与体育', count: 13 },
    { category: '文化艺术', count: 17 },
    { category: '新闻传播', count: 7 },
    { category: '旅游', count: 4 },
    { category: '水利', count: 8 },
    { category: '生物与化工', count: 7 },
    { category: '电子与信息', count: 18 },
    { category: '能源动力与材料', count: 11 },
    { category: '装备制造', count: 28 },
    { category: '财经商贸', count: 16 },
    { category: '资源环境与安全', count: 13 },
    { category: '轻工纺织', count: 6 },
    { category: '食品药品与粮食', count: 8 }
  ],
  '专科(高职)': [
    { category: '交通运输', count: 63 },
    { category: '公共管理与服务', count: 24 },
    { category: '公安与司法', count: 28 },
    { category: '农林牧渔', count: 48 },
    { category: '医药卫生', count: 47 },
    { category: '土木建筑', count: 34 },
    { category: '教育与体育', count: 48 },
    { category: '文化艺术', count: 60 },
    { category: '新闻传播', count: 22 },
    { category: '旅游', count: 18 },
    { category: '水利', count: 16 },
    { category: '生物与化工', count: 20 },
    { category: '电子与信息', count: 37 },
    { category: '能源动力与材料', count: 49 },
    { category: '装备制造', count: 68 },
    { category: '财经商贸', count: 44 },
    { category: '资源环境与安全', count: 63 },
    { category: '轻工纺织', count: 29 },
    { category: '食品药品与粮食', count: 26 }
  ]
}

export function CategoryTest() {
  const [selectedLevel, setSelectedLevel] = useState<'本科(普通)' | '本科(职业)' | '专科(高职)'>('本科(普通)')
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>()

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">分类图标和颜色测试</h1>
        
        {/* 教育层次切换 */}
        <div className="mb-6">
          <div className="flex gap-2">
            {(['本科(普通)', '本科(职业)', '专科(高职)'] as const).map((level) => (
              <button
                key={level}
                onClick={() => {
                  setSelectedLevel(level)
                  setSelectedCategory(undefined)
                }}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedLevel === level
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {level}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 分类侧边栏 */}
          <div className="lg:col-span-1">
            <CategorySidebar
              selectedCategory={selectedCategory as any}
              onCategorySelect={(category) => setSelectedCategory(category)}
              categories={mockCategories[selectedLevel] as any}
              categoryStats={mockCategoryStats[selectedLevel] as any}
              subCategories={{} as any}
            />
          </div>

          {/* 信息展示 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg p-6">
              <h2 className="text-lg font-bold text-gray-900 mb-4">当前选择</h2>
              <div className="space-y-2">
                <p><span className="font-medium">教育层次:</span> {selectedLevel}</p>
                <p><span className="font-medium">选中分类:</span> {selectedCategory || '无'}</p>
                <p><span className="font-medium">分类总数:</span> {mockCategories[selectedLevel].length}</p>
              </div>

              <div className="mt-6">
                <h3 className="text-md font-bold text-gray-900 mb-3">所有分类预览</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {mockCategories[selectedLevel].map((category) => (
                    <div key={category} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <span className="font-medium">{category}</span>
                      <span className="text-gray-500">
                        ({mockCategoryStats[selectedLevel].find(s => s.category === category)?.count || 0})
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
