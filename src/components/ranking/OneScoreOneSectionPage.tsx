import { useState, useEffect } from 'react'
import { Button } from '../ui/button'
import { Card } from '../ui/card'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import {
  getOneScoreOneSectionData,
  findRankingByScore,
  getAvailableYears,
  getAvailableProvinces,
  getAvailableSubjectSelections,
  getAvailableBatchNames
} from '../../services/oneScoreOneSectionApi'
import type {
  OneScoreOneSectionFilters,
  OneScoreOneSectionItem
} from '../../types/oneScoreOneSection'

import { cn } from '../../lib/utils'
import {
  ArrowLeft,
  TrendingUp,
  BarChart3,
  Users,
  Target,
  Info,
  Search,
  ChevronLeft,
  ChevronRight,
  Award,
  Calculator,
  Activity,
  Hash,
  UserCheck,
  GraduationCap
} from 'lucide-react'

interface OneScoreOneSectionPageProps {
  onBack?: () => void
}

// 分数分布图表组件
interface ScoreDistributionChartProps {
  data: OneScoreOneSectionItem[]
  targetScore: number
  className?: string
}

function ScoreDistributionChart({ data, targetScore, className }: ScoreDistributionChartProps) {
  if (!data || data.length === 0) {
    return <div className={className}>暂无数据</div>
  }

  // 处理数据，提取分数和人数
  const chartData = data.map(item => {
    let score = 0
    const scoreStr = item.examinationScore

    if (scoreStr.includes('-')) {
      // 处理分数范围，取中位数
      const [minStr, maxStr] = scoreStr.split('-')
      score = (parseInt(minStr) + parseInt(maxStr)) / 2
    } else {
      score = parseInt(scoreStr)
    }

    return {
      score: score,
      count: item.candidateCount,
      totalCandidates: item.totalCandidates,
      batch: item.admissionBatchName
    }
  }).filter(item => !isNaN(item.score)).sort((a, b) => b.score - a.score)

  if (chartData.length === 0) {
    return <div className={className}>数据格式错误</div>
  }

  // 计算图表范围
  const minScore = Math.min(...chartData.map(d => d.score))
  const maxScore = Math.max(...chartData.map(d => d.score))
  const maxCount = Math.max(...chartData.map(d => d.count))

  const width = 800
  const height = 300
  const padding = 60

  // 生成路径
  const generatePath = () => {
    if (chartData.length < 2) return ''

    const points = chartData.map((item, index) => {
      const x = padding + ((maxScore - item.score) / (maxScore - minScore)) * (width - 2 * padding)
      const y = height - padding - (item.count / maxCount) * (height - 2 * padding)
      return `${x},${y}`
    })

    return `M ${points.join(' L ')}`
  }

  // 找到目标分数的位置
  const targetX = padding + ((maxScore - targetScore) / (maxScore - minScore)) * (width - 2 * padding)

  return (
    <svg viewBox={`0 0 ${width} ${height}`} className={className}>
      {/* 背景网格 */}
      <defs>
        <pattern id="scoreGrid" width="40" height="30" patternUnits="userSpaceOnUse">
          <path d="M 40 0 L 0 0 0 30" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
        </pattern>
      </defs>
      <rect width={width} height={height} fill="url(#scoreGrid)" />

      {/* X轴标签（分数） */}
      <g className="text-xs fill-gray-600">
        {[maxScore, Math.floor((maxScore + minScore) / 2), minScore].map((score, index) => {
          const x = padding + ((maxScore - score) / (maxScore - minScore)) * (width - 2 * padding)
          return (
            <text key={index} x={x} y={height - 10} textAnchor="middle">
              {Math.round(score)}
            </text>
          )
        })}
      </g>

      {/* Y轴标签（人数） */}
      <g className="text-xs fill-gray-600">
        {[maxCount, Math.floor(maxCount * 0.75), Math.floor(maxCount * 0.5), Math.floor(maxCount * 0.25), 0].map((count, index) => {
          const y = height - padding - (count / maxCount) * (height - 2 * padding)
          return (
            <text key={index} x={padding - 10} y={y + 4} textAnchor="end">
              {count}
            </text>
          )
        })}
        <text x={20} y={30} textAnchor="middle" className="text-xs fill-gray-500">人数</text>
      </g>

      {/* 分数轴标签 */}
      <text x={width - 30} y={height - 20} textAnchor="middle" className="text-xs fill-gray-500">分数</text>

      {/* 主折线 */}
      <path
        d={generatePath()}
        fill="none"
        stroke="#ef4444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />

      {/* 目标分数线 */}
      <line
        x1={targetX}
        y1={padding}
        x2={targetX}
        y2={height - padding}
        stroke="#3b82f6"
        strokeWidth="2"
        strokeDasharray="5,5"
      />

      {/* 目标分数标签 */}
      <rect
        x={targetX - 20}
        y={padding - 25}
        width="40"
        height="20"
        fill="#3b82f6"
        rx="4"
      />
      <text
        x={targetX}
        y={padding - 10}
        textAnchor="middle"
        className="text-xs fill-white font-medium"
      >
        {targetScore}
      </text>

      {/* 数据点 */}
      {chartData.filter((_, index) => index % 5 === 0).map((item, index) => {
        const x = padding + ((maxScore - item.score) / (maxScore - minScore)) * (width - 2 * padding)
        const y = height - padding - (item.count / maxCount) * (height - 2 * padding)

        return (
          <circle
            key={index}
            cx={x}
            cy={y}
            r="3"
            fill="#ef4444"
            stroke="white"
            strokeWidth="1"
          />
        )
      })}
    </svg>
  )
}

export function OneScoreOneSectionPage({ onBack }: OneScoreOneSectionPageProps) {
  // 查询表单状态
  const [filters, setFilters] = useState<OneScoreOneSectionFilters>(() => {
    // 动态获取初始科目选择
    const initialSubjects = getAvailableSubjectSelections('安徽', '2025')
    return {
      year: '2025',
      provincename: '安徽',
      subjectselection: initialSubjects[0] || '物理类', // 使用第一个可用的科目类型
      minscore: '',
      maxscore: '',
      batchname: ''
    }
  })

  // 分数查询状态
  const [targetScore, setTargetScore] = useState<string>('467')

  // 数据状态
  const [data, setData] = useState<OneScoreOneSectionItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 位次查询结果
  const [rankingResult, setRankingResult] = useState<any>(null)

  // 完整一分一段数据（用于绘制折线图）
  const [fullScoreData, setFullScoreData] = useState<OneScoreOneSectionItem[]>([])

  // 统计数据状态
  const [statistics, setStatistics] = useState({
    rankingRange: '181797 ~ 183207',
    sameScoreCount: 1411,
    totalCandidates: 183207,
    batch: '高'
  })

  // 趋势数据
  const [trendData, setTrendData] = useState([
    { year: 2020, ranking: 180000, score: 467 },
    { year: 2021, ranking: 175000, score: 467 },
    { year: 2022, ranking: 182000, score: 467 },
    { year: 2023, ranking: 181000, score: 467 },
    { year: 2024, ranking: 183207, score: 467 }
  ])

  // 历年数据
  const [historicalData, setHistoricalData] = useState([
    { year: 2024, entrants: 176157, distribution1: 168636, distribution2: 470 },
    { year: 2021, entrants: 167156, distribution1: 160940, distribution2: 412 },
    { year: 2022, entrants: 164411, distribution1: 157421, distribution2: 440 }
  ])

  // 可用选项
  const availableYears = getAvailableYears()
  const availableProvinces = getAvailableProvinces()
  const availableSubjects = getAvailableSubjectSelections(filters.provincename, filters.year)
  const availableBatches = getAvailableBatchNames()

  // 监听省份和年份变化，自动更新科目选择
  useEffect(() => {
    const currentSubjects = getAvailableSubjectSelections(filters.provincename, filters.year)

    // 如果当前选择的科目不在新的可用科目列表中，自动选择第一个可用的科目
    if (currentSubjects.length > 0 && !currentSubjects.includes(filters.subjectselection)) {
      console.log(`🔄 省份/年份变化，自动更新科目选择: ${filters.subjectselection} → ${currentSubjects[0]}`)
      setFilters(prev => ({
        ...prev,
        subjectselection: currentSubjects[0]
      }))
    }
  }, [filters.provincename, filters.year, filters.subjectselection])

  // 获取完整一分一段数据（用于绘制折线图）
  const fetchFullScoreData = async () => {
    try {
      console.log('📊 获取完整一分一段数据用于绘制折线图...')
      const response = await getOneScoreOneSectionData({
        year: filters.year,
        provincename: filters.provincename,
        subjectselection: filters.subjectselection
        // 不设置分数范围，获取所有数据
      })

      if (response.data && response.data.length > 0) {
        setFullScoreData(response.data)
        console.log('✅ 获取完整数据成功，共', response.data.length, '条记录')
      }
    } catch (err) {
      console.error('❌ 获取完整数据失败:', err)
    }
  }

  // 查询一分一段数据
  const handleSearch = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await getOneScoreOneSectionData(filters)
      setData(response.data || [])
      console.log('✅ 查询成功，获取到', response.data?.length || 0, '条数据')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '查询失败'
      setError(errorMessage)
      console.error('❌ 查询失败:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // 根据分数查询位次
  const handleScoreSearch = async () => {
    if (!targetScore || isNaN(Number(targetScore))) {
      setError('请输入有效的分数')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log('🔍 开始查询位次:', {
        year: filters.year,
        province: filters.provincename,
        subject: filters.subjectselection,
        targetScore: Number(targetScore)
      })

      const result = await findRankingByScore(
        filters.year,
        filters.provincename,
        filters.subjectselection,
        Number(targetScore)
      )
      setRankingResult(result)

      // 更新统计数据
      if (result) {
        console.log('📋 查询结果详情:', {
          exactMatch: result.exactMatch,
          admissionBatchName: result.exactMatch?.admissionBatchName,
          candidateCount: result.exactMatch?.candidateCount,
          totalCandidates: result.totalCandidates
        })

        // 计算位次范围
        let rankingRange = result.ranking
        if (result.exactMatch?.rankingRange) {
          rankingRange = result.exactMatch.rankingRange
        } else if (result.exactMatch?.candidateCount) {
          // 如果有同分人数，计算位次范围
          const startRank = Math.max(1, result.totalCandidates - result.exactMatch.candidateCount + 1)
          const endRank = result.totalCandidates
          rankingRange = `${startRank} ~ ${endRank}`
        }

        setStatistics({
          rankingRange: rankingRange,
          sameScoreCount: result.exactMatch?.candidateCount || 1411,
          totalCandidates: result.totalCandidates,
          batch: result.exactMatch?.admissionBatchName || '未知批次'
        })

        // 解析历年数据
        let parsedHistoricalData = []
        let parsedTrendData = []

        if (result.exactMatch?.historicalScores) {
          try {
            const historicalScores = JSON.parse(result.exactMatch.historicalScores)
            console.log('📊 解析历年数据:', historicalScores)

            // 转换为表格数据格式
            parsedHistoricalData = historicalScores.map((item: any) => ({
              year: item.AcademicYear,
              entrants: 0, // 接口没有提供，使用默认值
              distribution1: item.ExaminationScore,
              distribution2: item.RankingRange
            }))

            // 转换为趋势图数据格式
            parsedTrendData = historicalScores.map((item: any) => {
              // 从位次范围中提取中位数作为趋势数据
              const rankingRange = item.RankingRange
              const matches = rankingRange.match(/(\d+)-(\d+)/)
              let ranking = result.totalCandidates
              if (matches) {
                const start = parseInt(matches[1])
                const end = parseInt(matches[2])
                ranking = Math.floor((start + end) / 2)
              }

              return {
                year: item.AcademicYear,
                ranking: ranking,
                score: parseInt(item.ExaminationScore)
              }
            })

            // 添加当前年份数据
            parsedTrendData.push({
              year: parseInt(filters.year),
              ranking: result.totalCandidates,
              score: Number(targetScore)
            })

            // 按年份排序
            parsedTrendData.sort((a, b) => a.year - b.year)

          } catch (error) {
            console.error('❌ 解析历年数据失败:', error)
            // 使用默认数据
            parsedHistoricalData = [
              { year: 2024, entrants: 176157, distribution1: targetScore, distribution2: result.totalCandidates }
            ]
            parsedTrendData = [
              { year: parseInt(filters.year), ranking: result.totalCandidates, score: Number(targetScore) }
            ]
          }
        } else {
          // 如果没有历年数据，使用当前数据
          parsedHistoricalData = [
            { year: parseInt(filters.year), entrants: 0, distribution1: targetScore, distribution2: result.totalCandidates }
          ]
          parsedTrendData = [
            { year: parseInt(filters.year), ranking: result.totalCandidates, score: Number(targetScore) }
          ]
        }

        setHistoricalData(parsedHistoricalData)
        setTrendData(parsedTrendData)

        // 获取完整一分一段数据用于绘制折线图
        await fetchFullScoreData()
      }

      console.log('✅ 位次查询成功:', result)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '位次查询失败'
      setError(errorMessage)
      console.error('❌ 位次查询失败:', err)
    } finally {
      setIsLoading(false)
    }
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      {/* 头部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-orange-100 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={onBack}
                className="flex items-center gap-2 hover:bg-orange-50"
              >
                <ArrowLeft className="w-4 h-4" />
                返回
              </Button>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">查位次</h1>
                  <p className="text-sm text-gray-600">高考一分一段数据查询</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>覆盖全国31个省份</span>
              </div>
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                <span>近6年数据</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* 查询表单区域 */}
        <div className="mb-8">
          <Card className="p-6 shadow-lg border-orange-100">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <Search className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">查询条件</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              {/* 年份选择 */}
              <div>
                <Label htmlFor="year" className="text-sm font-medium text-gray-700">年份</Label>
                <select
                  id="year"
                  value={filters.year}
                  onChange={(e) => setFilters(prev => ({ ...prev, year: e.target.value }))}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                >
                  {availableYears.map(year => (
                    <option key={year} value={year}>{year}年</option>
                  ))}
                </select>
              </div>

              {/* 省份选择 */}
              <div>
                <Label htmlFor="province" className="text-sm font-medium text-gray-700">省份</Label>
                <select
                  id="province"
                  value={filters.provincename}
                  onChange={(e) => setFilters(prev => ({ ...prev, provincename: e.target.value }))}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                >
                  {availableProvinces.map(province => (
                    <option key={province} value={province}>{province}</option>
                  ))}
                </select>
              </div>

              {/* 科目选择 */}
              <div>
                <Label htmlFor="subject" className="text-sm font-medium text-gray-700">科目类型</Label>
                <select
                  id="subject"
                  value={filters.subjectselection}
                  onChange={(e) => setFilters(prev => ({ ...prev, subjectselection: e.target.value }))}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200"
                >
                  {availableSubjects.map(subject => (
                    <option key={subject} value={subject}>{subject}</option>
                  ))}
                </select>
              </div>

              {/* 分数输入 */}
              <div>
                <Label htmlFor="score" className="text-sm font-medium text-gray-700">高考分数</Label>
                <Input
                  id="score"
                  type="number"
                  value={targetScore}
                  onChange={(e) => setTargetScore(e.target.value)}
                  placeholder="请输入分数"
                  className="mt-1"
                />
              </div>

              {/* 查询按钮 */}
              <div className="flex items-end">
                <Button
                  onClick={handleScoreSearch}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      查询中...
                    </>
                  ) : (
                    <>
                      <Search className="w-4 h-4 mr-2" />
                      查询位次
                    </>
                  )}
                </Button>
              </div>
            </div>
          </Card>
        </div>

        {/* 统计卡片区域 */}
        {rankingResult && (
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* 位次范围 */}
              <Card className="p-6 shadow-lg border-orange-100 bg-gradient-to-br from-orange-50 to-red-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600 mb-1">位次范围</div>
                    <div className="text-2xl font-bold text-orange-600">{statistics.rankingRange}</div>
                    <div className="text-xs text-gray-500 mt-1">位次区间</div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                    <Activity className="w-6 h-6 text-white" />
                  </div>
                </div>
              </Card>

              {/* 同分人数 */}
              <Card className="p-6 shadow-lg border-blue-100 bg-gradient-to-br from-blue-50 to-indigo-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600 mb-1">同分人数</div>
                    <div className="text-2xl font-bold text-blue-600">{statistics.sameScoreCount.toLocaleString()}</div>
                    <div className="text-xs text-gray-500 mt-1">相同分数考生</div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <Hash className="w-6 h-6 text-white" />
                  </div>
                </div>
              </Card>

              {/* 累计人数 */}
              <Card className="p-6 shadow-lg border-green-100 bg-gradient-to-br from-green-50 to-emerald-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600 mb-1">累计人数</div>
                    <div className="text-2xl font-bold text-green-600">{statistics.totalCandidates.toLocaleString()}</div>
                    <div className="text-xs text-gray-500 mt-1">累计考生数</div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <UserCheck className="w-6 h-6 text-white" />
                  </div>
                </div>
              </Card>

              {/* 批次信息 */}
              <Card className="p-6 shadow-lg border-purple-100 bg-gradient-to-br from-purple-50 to-violet-50">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600 mb-1">录取批次</div>
                    <div className="text-2xl font-bold text-purple-600">{statistics.batch}</div>
                    <div className="text-xs text-gray-500 mt-1">批次等级</div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                    <GraduationCap className="w-6 h-6 text-white" />
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* 一分一段分布图表区域 */}
        {rankingResult && fullScoreData.length > 0 && (
          <div className="mb-8">
            <Card className="p-6 shadow-lg border-orange-100">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">一分一段分布图</h3>
              </div>

              <div className="h-80 bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-6">
                <ScoreDistributionChart
                  data={fullScoreData}
                  targetScore={Number(targetScore)}
                  className="w-full h-full"
                />
              </div>

              <div className="mt-4 text-sm text-gray-600">
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span>分数分布曲线</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>查询分数位置</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span>批次分界线</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* 历年数据表格区域 */}
        {rankingResult && (
          <div className="mb-8">
            <Card className="shadow-lg border-orange-100">
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">历年等级分布</h3>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">年份</th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">对应分数</th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">位次范围</th>
                      <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">数据来源</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {historicalData.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{item.year}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-600">
                            {typeof item.distribution1 === 'number' ? item.distribution1 : item.distribution1}分
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-600">{item.distribution2}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-xs text-gray-500">
                            {item.year === parseInt(filters.year) ? '当前查询' : '历史数据'}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="p-4 bg-gray-50 text-sm text-gray-600">
                <p>历年数据说明：显示该位次在历年对应的分数和位次范围，数据来源于官方统计，仅供参考使用。</p>
              </div>
            </Card>
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="mb-8">
            <Card className="p-4 border-red-200 bg-red-50">
              <div className="text-red-800 text-sm">{error}</div>
            </Card>
          </div>
        )}

        {/* 空状态提示 */}
        {!rankingResult && !isLoading && !error && (
          <div className="mb-8">
            <Card className="p-12 text-center shadow-lg border-orange-100">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">开始查询位次信息</h3>
              <p className="text-gray-600 mb-6">
                请在上方输入查询条件，点击查询按钮获取位次数据
              </p>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="font-medium text-orange-900">精准数据</div>
                  <div className="text-orange-700">官方一分一段表</div>
                </div>
                <div className="p-3 bg-red-50 rounded-lg">
                  <div className="font-medium text-red-900">位次查询</div>
                  <div className="text-red-700">分数对应位次</div>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="font-medium text-yellow-900">累计统计</div>
                  <div className="text-yellow-700">考生人数统计</div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
