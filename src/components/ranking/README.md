# 查位次功能说明

## 📋 功能概述

查位次功能允许用户根据高考分数查询对应的位次信息，包括位次范围、同分人数、历年趋势等详细数据。

## 🎯 主要特性

### 1. 位次查询
- **精准查询**: 根据分数、省份、科类查询准确位次
- **范围显示**: 显示位次范围而非单一数值
- **同分统计**: 显示相同分数的考生人数

### 2. 数据展示
- **核心指标**: 位次范围、同分人数、最高位次
- **省控线**: 显示对应批次线信息
- **趋势分析**: 历年位次变化趋势图表

### 3. 交互体验
- **响应式设计**: 适配各种屏幕尺寸
- **实时查询**: 快速响应用户查询
- **可视化图表**: 直观的趋势分析图表

## 🏗️ 组件结构

```
src/components/ranking/
├── RankingSearchPage.tsx    # 主页面组件
├── RankingForm.tsx         # 查询表单组件
├── RankingResult.tsx       # 结果展示组件
├── RankingChart.tsx        # 趋势图表组件
└── README.md              # 说明文档
```

## 📊 数据结构

### 查询参数 (RankingQuery)
```typescript
interface RankingQuery {
  year: number                    // 年份
  province: string               // 省份
  score: number                  // 分数
  category: '文科' | '理科' | '综合'  // 科类
}
```

### 查询结果 (RankingResult)
```typescript
interface RankingResult {
  year: number
  province: string
  score: number
  category: '文科' | '理科' | '综合'
  rankingRange: {
    min: number                  // 最小位次
    max: number                  // 最大位次
  }
  sameScoreCount: number         // 同分人数
  maxRanking: number             // 最高位次
  controlLine: {
    type: '一本' | '二本' | '专科' | '高'
    score: number
  }
}
```

## 🎨 界面设计

### 1. 色彩方案
- **主色调**: 橙色到红色渐变 (orange-500 to red-500)
- **辅助色**: 蓝色、绿色、黄色用于不同数据类型
- **背景**: 浅橙色渐变背景

### 2. 布局结构
- **左侧**: 查询表单区域
- **右侧**: 结果展示区域
- **图表**: 历年趋势可视化

### 3. 交互元素
- **下拉选择器**: 年份、省份、科类选择
- **数字输入**: 分数输入框
- **查询按钮**: 带加载状态的查询按钮

## 🔧 使用方法

### 1. 基本查询
1. 选择查询年份（默认2025年）
2. 选择省份（默认安徽）
3. 选择科类（文科/理科/综合）
4. 输入高考分数
5. 点击"查询位次"按钮

### 2. 结果解读
- **位次范围**: 显示该分数对应的位次区间
- **同分人数**: 相同分数的考生总数
- **批次信息**: 分数对应的录取批次
- **趋势图表**: 历年该分数段的位次变化

### 3. 数据说明
- 位次数据基于模拟数据，仅供参考
- 实际使用时需要接入真实的位次数据API
- 支持近5年的历史数据查询

## 🚀 技术实现

### 1. 核心技术
- **React + TypeScript**: 组件开发
- **Tailwind CSS**: 样式设计
- **Lucide React**: 图标库
- **SVG**: 自定义图表绘制

### 2. 数据处理
- **模拟数据生成**: 基于算法生成合理的位次数据
- **趋势计算**: 历年数据对比分析
- **批次判断**: 根据省控线判断录取批次

### 3. 性能优化
- **懒加载**: 图表组件按需渲染
- **防抖处理**: 避免频繁查询
- **缓存机制**: 查询结果本地缓存

## 📱 响应式设计

- **桌面端**: 左右分栏布局
- **平板端**: 上下堆叠布局
- **移动端**: 单列垂直布局

## 🔮 未来扩展

1. **更多省份数据**: 扩展到全国所有省份
2. **专业位次**: 按专业查询位次信息
3. **院校位次**: 特定院校的录取位次
4. **预测功能**: 基于历史数据预测位次变化
5. **对比功能**: 多年份、多省份位次对比

## 🐛 已知问题

1. 当前使用模拟数据，需要接入真实数据源
2. 图表在小屏幕设备上可能显示不够清晰
3. 部分省份的数据可能不够完整

## 📞 技术支持

如有问题或建议，请联系开发团队。
