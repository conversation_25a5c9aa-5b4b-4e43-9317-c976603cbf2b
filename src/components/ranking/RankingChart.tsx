import { Card } from '../ui/card'
import { cn } from '../../lib/utils'

// 本地类型定义
interface RankingTrend {
  year: number
  ranking: number
  score: number
  sameScoreCount: number
}
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Calendar,
  Target
} from 'lucide-react'

interface RankingChartProps {
  data: RankingTrend[]
  title: string
}

export function RankingChart({ data, title }: RankingChartProps) {
  if (!data || data.length === 0) {
    return null
  }

  // 计算图表数据
  const maxRanking = Math.max(...data.map(d => d.ranking))
  const minRanking = Math.min(...data.map(d => d.ranking))
  const rankingRange = maxRanking - minRanking
  
  // 计算趋势
  const firstRanking = data[0]?.ranking || 0
  const lastRanking = data[data.length - 1]?.ranking || 0
  const trend = lastRanking - firstRanking
  const trendPercentage = firstRanking > 0 ? Math.abs((trend / firstRanking) * 100) : 0

  // 生成SVG路径
  const generatePath = () => {
    if (data.length < 2) return ''
    
    const width = 400
    const height = 200
    const padding = 20
    
    const points = data.map((item, index) => {
      const x = padding + (index / (data.length - 1)) * (width - 2 * padding)
      const y = height - padding - ((item.ranking - minRanking) / rankingRange) * (height - 2 * padding)
      return `${x},${y}`
    })
    
    return `M ${points.join(' L ')}`
  }

  const generateAreaPath = () => {
    if (data.length < 2) return ''
    
    const width = 400
    const height = 200
    const padding = 20
    
    const points = data.map((item, index) => {
      const x = padding + (index / (data.length - 1)) * (width - 2 * padding)
      const y = height - padding - ((item.ranking - minRanking) / rankingRange) * (height - 2 * padding)
      return `${x},${y}`
    })
    
    const firstPoint = points[0]
    const lastPoint = points[points.length - 1]
    const [lastX] = lastPoint.split(',')
    const [firstX] = firstPoint.split(',')
    
    return `M ${firstX},${height - padding} L ${points.join(' L ')} L ${lastX},${height - padding} Z`
  }

  return (
    <Card className="p-6 shadow-lg border-orange-100">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">位次趋势</h3>
            <p className="text-sm text-gray-600">{title}</p>
          </div>
        </div>
        
        {/* 趋势指示器 */}
        <div className={cn(
          "flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium",
          trend > 0 
            ? "bg-red-50 text-red-700 border border-red-200"
            : trend < 0
            ? "bg-green-50 text-green-700 border border-green-200"
            : "bg-gray-50 text-gray-700 border border-gray-200"
        )}>
          {trend > 0 ? (
            <TrendingDown className="w-4 h-4" />
          ) : trend < 0 ? (
            <TrendingUp className="w-4 h-4" />
          ) : (
            <Target className="w-4 h-4" />
          )}
          <span>
            {trend === 0 ? '持平' : `${trend > 0 ? '下降' : '上升'} ${trendPercentage.toFixed(1)}%`}
          </span>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 mb-6">
        <div className="relative">
          <svg
            width="100%"
            height="200"
            viewBox="0 0 400 200"
            className="overflow-visible"
          >
            {/* 背景网格 */}
            <defs>
              <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            {/* 面积填充 */}
            <path
              d={generateAreaPath()}
              fill="url(#gradient)"
              opacity="0.3"
            />
            
            {/* 渐变定义 */}
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#f97316" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#f97316" stopOpacity="0.1"/>
              </linearGradient>
            </defs>
            
            {/* 主线条 */}
            <path
              d={generatePath()}
              fill="none"
              stroke="#f97316"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            
            {/* 数据点 */}
            {data.map((item, index) => {
              const x = 20 + (index / (data.length - 1)) * 360
              const y = 180 - ((item.ranking - minRanking) / rankingRange) * 160
              
              return (
                <g key={index}>
                  <circle
                    cx={x}
                    cy={y}
                    r="6"
                    fill="#f97316"
                    stroke="white"
                    strokeWidth="2"
                    className="hover:r-8 transition-all cursor-pointer"
                  />
                  <text
                    x={x}
                    y={y - 15}
                    textAnchor="middle"
                    className="text-xs font-medium fill-gray-700"
                  >
                    {item.ranking.toLocaleString()}
                  </text>
                </g>
              )
            })}
          </svg>
        </div>
        
        {/* X轴标签 */}
        <div className="flex justify-between mt-4 px-5">
          {data.map((item, index) => (
            <div key={index} className="text-center">
              <div className="text-sm font-medium text-gray-900">{item.year}</div>
              <div className="text-xs text-gray-600">{item.score}分</div>
            </div>
          ))}
        </div>
      </div>

      {/* 数据表格 */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 flex items-center gap-2">
          <Calendar className="w-4 h-4" />
          历年数据详情
        </h4>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-2 px-3 font-medium text-gray-900">年份</th>
                <th className="text-left py-2 px-3 font-medium text-gray-900">分数</th>
                <th className="text-left py-2 px-3 font-medium text-gray-900">位次</th>
                <th className="text-left py-2 px-3 font-medium text-gray-900">同分人数</th>
                <th className="text-left py-2 px-3 font-medium text-gray-900">变化</th>
              </tr>
            </thead>
            <tbody>
              {data.map((item, index) => {
                const prevItem = index > 0 ? data[index - 1] : null
                const change = prevItem ? item.ranking - prevItem.ranking : 0
                
                return (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-2 px-3 font-medium text-gray-900">{item.year}</td>
                    <td className="py-2 px-3 text-gray-700">{item.score}分</td>
                    <td className="py-2 px-3 text-gray-700">{item.ranking.toLocaleString()}</td>
                    <td className="py-2 px-3 text-gray-700">{item.sameScoreCount}人</td>
                    <td className="py-2 px-3">
                      {index === 0 ? (
                        <span className="text-gray-500">-</span>
                      ) : (
                        <span className={cn(
                          "flex items-center gap-1 text-xs font-medium",
                          change > 0 ? "text-red-600" : change < 0 ? "text-green-600" : "text-gray-600"
                        )}>
                          {change > 0 ? (
                            <TrendingDown className="w-3 h-3" />
                          ) : change < 0 ? (
                            <TrendingUp className="w-3 h-3" />
                          ) : (
                            <Target className="w-3 h-3" />
                          )}
                          {change === 0 ? '持平' : `${Math.abs(change).toLocaleString()}`}
                        </span>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </Card>
  )
}
