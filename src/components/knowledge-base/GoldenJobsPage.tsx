import { useState } from "react"
import { ArrowLeft, Trophy, Search, Shield, TrendingUp, DollarSign, Users, ChevronRight, Filter, Star } from "lucide-react"
import { Button } from "../ui/button"

interface GoldenJobsPageProps {
  onBack: () => void
}

interface GoldenJob {
  id: string
  name: string
  category: string
  description: string
  averageSalary: number
  jobSecurity: "极高" | "很高" | "较高"
  growthPotential: number
  entryBarrier: "高" | "中" | "低"
  workLifeBalance: number
  benefits: string[]
  requirements: string[]
  careerPath: string[]
  topEmployers: string[]
  relatedMajors: string[]
  stabilityScore: number
}

export function GoldenJobsPage({ onBack }: GoldenJobsPageProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedSecurity, setSelectedSecurity] = useState("all")

  const categories = [
    { id: "all", name: "全部职业", count: 67 },
    { id: "government", name: "公务员", count: 15 },
    { id: "finance", name: "金融", count: 12 },
    { id: "tech", name: "科技", count: 18 },
    { id: "healthcare", name: "医疗", count: 10 },
    { id: "education", name: "教育", count: 8 },
    { id: "stateowned", name: "国企", count: 4 }
  ]

  const securityLevels = [
    { id: "all", name: "全部", count: 67 },
    { id: "极高", name: "极高稳定", count: 20 },
    { id: "很高", name: "很高稳定", count: 30 },
    { id: "较高", name: "较高稳定", count: 17 }
  ]

  const goldenJobs: GoldenJob[] = [
    {
      id: "1",
      name: "公务员（中央部委）",
      category: "公务员",
      description: "在国家中央部委从事政策制定、行政管理等工作，享有极高的职业稳定性和社会地位。",
      averageSalary: 15000,
      jobSecurity: "极高",
      growthPotential: 85,
      entryBarrier: "高",
      workLifeBalance: 75,
      stabilityScore: 98,
      benefits: [
        "终身职业保障",
        "完善的社会保险",
        "带薪年假制度",
        "职业发展路径清晰",
        "退休保障完善"
      ],
      requirements: [
        "本科及以上学历",
        "通过国家公务员考试",
        "政治素质过硬",
        "专业能力突出"
      ],
      careerPath: [
        "科员 → 副主任科员 → 主任科员",
        "副处长 → 处长 → 副司长",
        "司长 → 副部长 → 部长"
      ],
      topEmployers: [
        "发改委",
        "财政部",
        "商务部",
        "外交部",
        "教育部"
      ],
      relatedMajors: [
        "法学",
        "经济学",
        "公共管理",
        "政治学",
        "国际关系"
      ]
    },
    {
      id: "2",
      name: "银行管理培训生",
      category: "金融",
      description: "在大型银行接受系统培训，快速成长为银行中高层管理人员，享有优厚待遇和发展前景。",
      averageSalary: 18000,
      jobSecurity: "很高",
      growthPotential: 90,
      entryBarrier: "高",
      workLifeBalance: 70,
      stabilityScore: 88,
      benefits: [
        "高薪酬待遇",
        "完善培训体系",
        "快速晋升通道",
        "丰厚年终奖",
        "优质医疗保险"
      ],
      requirements: [
        "985/211院校毕业",
        "金融相关专业",
        "英语六级以上",
        "综合素质优秀"
      ],
      careerPath: [
        "管培生 → 客户经理 → 部门主管",
        "分行副行长 → 分行行长",
        "总行部门总经理"
      ],
      topEmployers: [
        "工商银行",
        "建设银行",
        "农业银行",
        "中国银行",
        "招商银行"
      ],
      relatedMajors: [
        "金融学",
        "经济学",
        "会计学",
        "国际金融",
        "投资学"
      ]
    },
    {
      id: "3",
      name: "互联网大厂技术专家",
      category: "科技",
      description: "在知名互联网公司担任技术专家，负责核心技术研发，享有高薪和股权激励。",
      averageSalary: 35000,
      jobSecurity: "较高",
      growthPotential: 95,
      entryBarrier: "高",
      workLifeBalance: 60,
      stabilityScore: 82,
      benefits: [
        "超高薪酬",
        "股权激励",
        "技术成长快",
        "行业影响力大",
        "创新环境好"
      ],
      requirements: [
        "计算机相关专业",
        "技术能力突出",
        "项目经验丰富",
        "学习能力强"
      ],
      careerPath: [
        "初级工程师 → 高级工程师",
        "技术专家 → 资深专家",
        "技术总监 → CTO"
      ],
      topEmployers: [
        "阿里巴巴",
        "腾讯",
        "字节跳动",
        "百度",
        "美团"
      ],
      relatedMajors: [
        "计算机科学与技术",
        "软件工程",
        "人工智能",
        "数据科学",
        "网络工程"
      ]
    },
    {
      id: "4",
      name: "三甲医院主治医师",
      category: "医疗",
      description: "在知名三甲医院从事临床医疗工作，具有较高的社会地位和稳定的职业发展。",
      averageSalary: 22000,
      jobSecurity: "极高",
      growthPotential: 80,
      entryBarrier: "高",
      workLifeBalance: 65,
      stabilityScore: 92,
      benefits: [
        "社会地位高",
        "职业稳定性强",
        "专业成就感强",
        "收入稳定增长",
        "退休保障好"
      ],
      requirements: [
        "医学院校毕业",
        "执业医师资格",
        "住院医师规培",
        "专业技能精湛"
      ],
      careerPath: [
        "住院医师 → 主治医师",
        "副主任医师 → 主任医师",
        "科室主任 → 院长"
      ],
      topEmployers: [
        "协和医院",
        "301医院",
        "华西医院",
        "湘雅医院",
        "瑞金医院"
      ],
      relatedMajors: [
        "临床医学",
        "口腔医学",
        "中医学",
        "麻醉学",
        "医学影像学"
      ]
    },
    {
      id: "5",
      name: "重点中学教师",
      category: "教育",
      description: "在知名中学从事教学工作，享有稳定的工作环境、较长的假期和良好的社会声誉。",
      averageSalary: 12000,
      jobSecurity: "很高",
      growthPotential: 70,
      entryBarrier: "中",
      workLifeBalance: 85,
      stabilityScore: 90,
      benefits: [
        "工作稳定",
        "假期较长",
        "社会尊重度高",
        "工作环境单纯",
        "退休保障完善"
      ],
      requirements: [
        "师范类专业毕业",
        "教师资格证",
        "学科专业能力强",
        "沟通表达能力好"
      ],
      careerPath: [
        "普通教师 → 骨干教师",
        "教研组长 → 年级主任",
        "副校长 → 校长"
      ],
      topEmployers: [
        "人大附中",
        "北京四中",
        "上海中学",
        "华师大二附中",
        "深圳中学"
      ],
      relatedMajors: [
        "师范类专业",
        "数学与应用数学",
        "汉语言文学",
        "英语",
        "物理学"
      ]
    }
  ]

  const filteredJobs = goldenJobs.filter(job => {
    const matchesSearch = job.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.benefits.some(benefit => benefit.toLowerCase().includes(searchQuery.toLowerCase()))
    
    const matchesCategory = selectedCategory === "all" || 
                           job.category === categories.find(cat => cat.id === selectedCategory)?.name
    
    const matchesSecurity = selectedSecurity === "all" || job.jobSecurity === selectedSecurity
    
    return matchesSearch && matchesCategory && matchesSecurity
  })

  const getSecurityColor = (security: string) => {
    switch (security) {
      case "极高":
        return "bg-green-100 text-green-700 border-green-200"
      case "很高":
        return "bg-blue-100 text-blue-700 border-blue-200"
      case "较高":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      default:
        return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  const getBarrierColor = (barrier: string) => {
    switch (barrier) {
      case "高":
        return "text-red-600"
      case "中":
        return "text-yellow-600"
      case "低":
        return "text-green-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-yellow-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回知识库</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <Trophy className="w-6 h-6 text-amber-600" />
                <h1 className="text-xl font-bold text-gray-800">金饭碗</h1>
              </div>
            </div>
            
            {/* 搜索框 */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索金饭碗职业..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 特色说明 */}
        <div className="bg-gradient-to-r from-amber-100 to-yellow-100 border border-amber-200 rounded-2xl p-6 mb-8">
          <div className="flex items-start space-x-3">
            <Trophy className="w-6 h-6 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-amber-800 mb-2">什么是"金饭碗"职业？</h3>
              <p className="text-amber-700 leading-relaxed">
                "金饭碗"职业通常具有高薪酬、高稳定性、良好发展前景的特点。这些职业不仅提供优厚的经济回报，
                还具有较强的抗风险能力和长期发展潜力。选择这些职业需要相应的教育背景、专业技能和个人素质。
              </p>
            </div>
          </div>
        </div>

        <div className="flex gap-8">
          {/* 左侧筛选 */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 sticky top-24">
              {/* 职业分类 */}
              <div className="mb-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Filter className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold text-gray-800">职业分类</h3>
                </div>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                        selectedCategory === category.id
                          ? "bg-amber-100 text-amber-700 font-medium"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      <span>{category.name}</span>
                      <span className="text-sm text-gray-500">({category.count})</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* 稳定性筛选 */}
              <div>
                <h3 className="font-semibold text-gray-800 mb-4">职业稳定性</h3>
                <div className="space-y-2">
                  {securityLevels.map((level) => (
                    <button
                      key={level.id}
                      onClick={() => setSelectedSecurity(level.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                        selectedSecurity === level.id
                          ? "bg-amber-100 text-amber-700 font-medium"
                          : "text-gray-600 hover:bg-gray-100"
                      }`}
                    >
                      <span>{level.name}</span>
                      <span className="text-sm text-gray-500">({level.count})</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 右侧职业列表 */}
          <div className="flex-1">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                金饭碗职业推荐
              </h2>
              <p className="text-gray-600">
                高薪酬、高稳定性的优质职业选择
              </p>
            </div>

            <div className="space-y-6">
              {filteredJobs.map((job, index) => (
                <div
                  key={job.id}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex items-center space-x-2">
                          <Star className="w-5 h-5 text-amber-500" />
                          <span className="text-lg font-bold text-amber-600">#{index + 1}</span>
                        </div>
                        <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getSecurityColor(job.jobSecurity)}`}>
                          {job.jobSecurity}稳定
                        </span>
                        <span className="px-3 py-1 bg-amber-100 text-amber-700 text-sm font-medium rounded-full">
                          {job.category}
                        </span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 mb-2">
                        {job.name}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-4">
                        {job.description}
                      </p>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400 ml-4" />
                  </div>

                  {/* 关键指标 */}
                  <div className="grid grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center justify-center mb-1">
                        <DollarSign className="w-4 h-4 text-green-600 mr-1" />
                        <span className="text-sm text-gray-600">月薪</span>
                      </div>
                      <div className="text-lg font-bold text-green-600">¥{job.averageSalary.toLocaleString()}</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-center mb-1">
                        <Shield className="w-4 h-4 text-blue-600 mr-1" />
                        <span className="text-sm text-gray-600">稳定性</span>
                      </div>
                      <div className="text-lg font-bold text-blue-600">{job.stabilityScore}分</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center justify-center mb-1">
                        <TrendingUp className="w-4 h-4 text-purple-600 mr-1" />
                        <span className="text-sm text-gray-600">发展潜力</span>
                      </div>
                      <div className="text-lg font-bold text-purple-600">{job.growthPotential}%</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="w-4 h-4 text-orange-600 mr-1" />
                        <span className="text-sm text-gray-600">入行门槛</span>
                      </div>
                      <div className={`text-lg font-bold ${getBarrierColor(job.entryBarrier)}`}>{job.entryBarrier}</div>
                    </div>
                  </div>

                  {/* 核心优势 */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                      <Trophy className="w-4 h-4 text-amber-500 mr-1" />
                      核心优势
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {job.benefits.map((benefit, benefitIndex) => (
                        <span
                          key={benefitIndex}
                          className="px-3 py-1 bg-amber-100 text-amber-700 text-sm rounded-full"
                        >
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 相关专业 */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">相关专业</h4>
                    <div className="flex flex-wrap gap-2">
                      {job.relatedMajors.map((major, majorIndex) => (
                        <span
                          key={majorIndex}
                          className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full"
                        >
                          {major}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* 顶级雇主 */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">顶级雇主</h4>
                    <div className="flex flex-wrap gap-2">
                      {job.topEmployers.map((employer, empIndex) => (
                        <span
                          key={empIndex}
                          className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                        >
                          {employer}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 搜索结果为空时的提示 */}
            {filteredJobs.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">未找到相关职业</h3>
                <p className="text-gray-600">请尝试其他关键词或调整筛选条件</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
