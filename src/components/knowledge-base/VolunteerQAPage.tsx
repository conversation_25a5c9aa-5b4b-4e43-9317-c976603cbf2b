import { useState, useEffect } from "react"
import { ArrowLeft, HelpCircle, Search, ChevronRight, Calendar, Loader2, AlertCircle } from "lucide-react"
import { But<PERSON> } from "../ui/button"
import {
  getVolunteerQAList,
  formatTimestamp,
  getCategoryInfo,
  getCategoryStats,
  type VolunteerQAItem
} from "../../services/volunteerQAApi"

interface VolunteerQAPageProps {
  onBack: () => void
  onNavigateToDetail?: (qa: VolunteerQAItem) => void
}

export function VolunteerQAPage({ onBack, onNavigateToDetail }: VolunteerQAPageProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [qaItems, setQAItems] = useState<VolunteerQAItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [categories, setCategories] = useState<Array<{ id: string; name: string; count: number }>>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const pageSize = 50

  // 加载志愿100问数据
  const loadQAData = async (page: number = 1) => {
    setLoading(true)
    setError(null)

    try {
      const response = await getVolunteerQAList(page, 1000) // 获取所有数据
      setQAItems(response.data.list)
      setTotalCount(response.data.total)

      // 生成分类统计
      const categoryStats = getCategoryStats(response.data.list)
      setCategories(categoryStats)

      console.log('✅ 成功加载志愿100问数据:', response.data.list.length, '条')
    } catch (err) {
      console.error('❌ 加载志愿100问失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
      setQAItems([])
      setTotalCount(0)
      setCategories([])
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时加载数据
  useEffect(() => {
    loadQAData(currentPage)
  }, [currentPage])

  // 处理搜索和筛选
  const filteredQAs = qaItems.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.ds.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = selectedCategory === "all" || item.type === selectedCategory

    return matchesSearch && matchesCategory
  })

  // 处理点击问题项
  const handleQAClick = (qa: VolunteerQAItem) => {
    if (onNavigateToDetail) {
      onNavigateToDetail(qa)
    }
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回知识库</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <HelpCircle className="w-6 h-6 text-green-600" />
                <h1 className="text-xl font-bold text-gray-800">志愿100问</h1>
              </div>
            </div>

            {/* 搜索框 */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="搜索问题..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200"
                />
              </div>
            </div>
          </div>
        </div>
      </header>
      <div className="container mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* 左侧分类筛选 */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 sticky top-24">
              <h3 className="font-semibold text-gray-800 mb-4">问题分类</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full text-left px-3 py-2 rounded-lg transition-colors duration-200 flex items-center justify-between ${
                      selectedCategory === category.id
                        ? "bg-green-100 text-green-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100"
                    }`}
                  >
                    <span>{category.name}</span>
                    <span className="text-sm text-gray-500">({category.count})</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
          {/* 右侧问答列表 */}
          <div className="flex-1">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">
                志愿填报常见问题
              </h2>
              <p className="text-gray-600">
                专家权威解答，助您科学填报志愿
              </p>
            </div>

            {/* 加载状态 */}
            {loading && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-green-600 mr-3" />
                <span className="text-gray-600">加载问题数据中...</span>
              </div>
            )}

            {/* 错误状态 */}
            {error && (
              <div className="text-center py-12">
                <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={() => loadQAData(currentPage)} variant="outline">
                  重试
                </Button>
              </div>
            )}

            {/* 问答列表 */}
            {!loading && !error && (
              <>
                <div className="space-y-4 mb-8">
                  {filteredQAs.map((item) => {
                    const categoryInfo = getCategoryInfo(item.type)
                    return (
                      <div
                        key={item.id}
                        className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 cursor-pointer group"
                        onClick={() => handleQAClick(item)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-3">
                              <span className={`px-3 py-1 text-sm font-medium rounded-full ${categoryInfo.bgColor} ${categoryInfo.color}`}>
                                {categoryInfo.name}
                              </span>
                              <div className="flex items-center space-x-1 text-sm text-gray-500">
                                <Calendar className="w-4 h-4" />
                                <span>{formatTimestamp(item.createTime)}</span>
                              </div>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-green-600 transition-colors">
                              {item.question}
                            </h3>
                            <p className="text-gray-600 leading-relaxed">
                              {item.ds}
                            </p>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-green-600 transition-colors ml-4" />
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* 搜索结果为空时的提示 */}
                {filteredQAs.length === 0 && qaItems.length > 0 && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Search className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 mb-2">未找到相关问题</h3>
                    <p className="text-gray-600">请尝试其他关键词或选择不同的分类</p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
