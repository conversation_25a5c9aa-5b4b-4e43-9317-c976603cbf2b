import { useState, useEffect } from "react"
import { ArrowLeft, AlertTriangle, Calendar, Loader2, AlertCircle, BookOpen, TrendingDown, Shield } from "lucide-react"
import { Button } from "../ui/button"
import { 
  getCautiousMajorDetail, 
  formatTimestamp, 
  getCategoryInfo,
  getRiskLevel,
  extractRiskFactors,
  type CautiousMajorItem,
  type CautiousMajorDetailItem 
} from "../../services/cautiousMajorsApi"

interface CautiousMajorDetailPageProps {
  onBack: () => void
  selectedMajor: CautiousMajorItem | null
}

export function CautiousMajorDetailPage({ onBack, selectedMajor }: CautiousMajorDetailPageProps) {
  const [detailData, setDetailData] = useState<CautiousMajorDetailItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载详情数据
  const loadDetailData = async (questionId: number) => {
    setLoading(true)
    setError(null)

    try {
      const response = await getCautiousMajorDetail(questionId)
      if (response.data.list.length > 0) {
        setDetailData(response.data.list[0])
      } else {
        setError('未找到详细内容')
      }
    } catch (err) {
      console.error('加载详情失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 当选中的专业改变时重新加载数据
  useEffect(() => {
    if (selectedMajor) {
      loadDetailData(selectedMajor.id)
    }
  }, [selectedMajor])

  if (!selectedMajor) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">未选择专业</h3>
          <p className="text-gray-600 mb-4">请先选择一个专业查看详情</p>
          <Button onClick={onBack}>返回列表</Button>
        </div>
      </div>
    )
  }

  const categoryInfo = getCategoryInfo(selectedMajor.type)
  const riskLevel = getRiskLevel(selectedMajor.ds)
  const riskFactors = extractRiskFactors(selectedMajor.ds)

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50">
      {/* 顶部导航 */}
      <header className="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={onBack}
                className="flex items-center space-x-2 hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回列表</span>
              </Button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                <h1 className="text-xl font-bold text-gray-800">慎选专业详情</h1>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* 专业标题卡片 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 mb-8">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-3">
                <span className={`px-3 py-1 text-sm font-medium rounded-full ${categoryInfo.bgColor} ${categoryInfo.color} flex items-center space-x-1`}>
                  <span>{categoryInfo.icon}</span>
                  <span>{categoryInfo.name}</span>
                </span>
                <span className={`px-3 py-1 text-sm font-medium rounded-full ${riskLevel.bgColor} ${riskLevel.color} flex items-center space-x-1`}>
                  <TrendingDown className="w-3 h-3" />
                  <span>{riskLevel.level}</span>
                </span>
                {selectedMajor.createTime && (
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <Calendar className="w-4 h-4" />
                    <span>{formatTimestamp(selectedMajor.createTime)}</span>
                  </div>
                )}
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-3">
                {selectedMajor.question}
              </h1>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-red-800 mb-2">主要风险因素</h3>
                    <div className="flex flex-wrap gap-2">
                      {riskFactors.map((factor, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full"
                        >
                          {factor}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 详细内容 */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
          <div className="flex items-center space-x-2 mb-6">
            <Shield className="w-5 h-5 text-red-600" />
            <h2 className="text-xl font-semibold text-gray-800">详细分析</h2>
          </div>
          
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-red-600 mr-3" />
              <span className="text-gray-600">加载详细内容中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button 
                onClick={() => selectedMajor && loadDetailData(selectedMajor.id)}
                variant="outline"
              >
                重试
              </Button>
            </div>
          ) : detailData ? (
            <div className="prose prose-lg max-w-none">
              <div 
                className="text-gray-800 leading-relaxed [&>h1]:text-2xl [&>h1]:font-bold [&>h1]:mb-4 [&>h2]:text-xl [&>h2]:font-semibold [&>h2]:mb-3 [&>h3]:text-lg [&>h3]:font-medium [&>h3]:mb-2 [&>h4]:text-base [&>h4]:font-medium [&>h4]:mb-2 [&>p]:mb-4 [&>ul]:mb-4 [&>ul]:pl-6 [&>ol]:mb-4 [&>ol]:pl-6 [&>li]:mb-2 [&>li]:list-disc [&>ol>li]:list-decimal [&>hr]:my-6 [&>hr]:border-gray-300 [&>strong]:font-semibold [&>em]:italic [&>table]:w-full [&>table]:border-collapse [&>table]:border [&>table]:border-gray-300 [&>table]:mb-4 [&>th]:border [&>th]:border-gray-300 [&>th]:bg-gray-100 [&>th]:p-2 [&>th]:text-left [&>td]:border [&>td]:border-gray-300 [&>td]:p-2 [&>a]:text-blue-600 [&>a]:underline [&>a:hover]:text-blue-800"
                dangerouslySetInnerHTML={{ __html: detailData.content }}
              />
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">暂无详细内容</h3>
              <p className="text-gray-600">该专业的详细分析正在完善中</p>
            </div>
          )}
        </div>

        {/* 建议和提醒 */}
        <div className="mt-8 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-600" />
            报考建议
          </h3>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="text-gray-700">
              <p className="mb-2">• <strong>慎重考虑</strong>：该专业存在一定的就业风险，建议充分了解行业现状</p>
              <p className="mb-2">• <strong>个人评估</strong>：结合自身兴趣、能力和家庭背景进行综合评估</p>
              <p className="mb-2">• <strong>备选方案</strong>：考虑相关但风险较低的专业作为备选</p>
              <p>• <strong>深入调研</strong>：咨询行业从业者，了解真实的工作环境和发展前景</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
