// 科目选择工具函数
import subjectSelectionsData from '../../data/subjectSelections.json'

/**
 * 科目选择数据类型
 */
interface SubjectSelectionData {
  DataStatus: {
    RequestParameter: string
    StatusCode: number
    StatusDescription: string
    ResponseDateTime: string
    DataTotalCount: number
  }
  Data: {
    [province: string]: {
      [year: string]: Array<{
        name: string
      }>
    }
  }
}

/**
 * 根据省份和年份获取可用的科目选择类型
 */
export function getSubjectSelectionsByProvinceAndYear(
  provinceName: string, 
  year: string
): string[] {
  try {
    const data = subjectSelectionsData as SubjectSelectionData
    const provinceData = data.Data?.[provinceName]
    
    if (provinceData && provinceData[year]) {
      const subjects = provinceData[year].map(item => item.name)
      console.log(`📚 获取${provinceName}${year}年科目类型:`, subjects)
      return subjects
    }
    
    console.warn(`⚠️ 未找到${provinceName}${year}年的科目数据，使用默认推测`)
    return getSubjectSelectionsByYearFallback(year)
  } catch (error) {
    console.error('❌ 获取科目选择数据失败:', error)
    return getSubjectSelectionsByYearFallback(year)
  }
}

/**
 * 根据年份推测科目类型（后备方案）
 */
function getSubjectSelectionsByYearFallback(year: string): string[] {
  const yearNum = parseInt(year)
  
  // 根据高考改革时间线推测科目类型
  if (yearNum >= 2024) {
    // 2024年及以后，大部分省份已完成新高考改革
    return ['物理类', '历史类', '综合']
  } else if (yearNum >= 2021) {
    // 2021-2023年，新旧高考并存
    return ['物理类', '历史类', '理科', '文科', '综合']
  } else {
    // 2020年及以前，主要是传统高考
    return ['理科', '文科']
  }
}

/**
 * 获取所有可用的省份列表
 */
export function getAvailableProvinces(): string[] {
  try {
    const data = subjectSelectionsData as SubjectSelectionData
    return Object.keys(data.Data || {})
  } catch (error) {
    console.error('❌ 获取省份列表失败:', error)
    return [
      '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
      '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
      '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
      '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆'
    ]
  }
}

/**
 * 获取指定省份的所有可用年份
 */
export function getAvailableYearsByProvince(provinceName: string): string[] {
  try {
    const data = subjectSelectionsData as SubjectSelectionData
    const provinceData = data.Data?.[provinceName]
    
    if (provinceData) {
      const years = Object.keys(provinceData).sort((a, b) => parseInt(b) - parseInt(a))
      console.log(`📅 获取${provinceName}可用年份:`, years)
      return years
    }
    
    console.warn(`⚠️ 未找到${provinceName}的年份数据，使用默认年份`)
    return ['2025', '2024', '2023', '2022', '2021', '2020']
  } catch (error) {
    console.error('❌ 获取年份列表失败:', error)
    return ['2025', '2024', '2023', '2022', '2021', '2020']
  }
}

/**
 * 检查指定省份和年份是否有科目数据
 */
export function hasSubjectData(provinceName: string, year: string): boolean {
  try {
    const data = subjectSelectionsData as SubjectSelectionData
    const provinceData = data.Data?.[provinceName]
    return !!(provinceData && provinceData[year])
  } catch (error) {
    console.error('❌ 检查科目数据失败:', error)
    return false
  }
}

/**
 * 获取科目选择的统计信息
 */
export function getSubjectSelectionStats() {
  try {
    const data = subjectSelectionsData as SubjectSelectionData
    const provinces = Object.keys(data.Data || {})
    let totalYears = 0
    let totalSubjects = 0
    const subjectTypes = new Set<string>()
    
    provinces.forEach(province => {
      const provinceData = data.Data[province]
      const years = Object.keys(provinceData)
      totalYears += years.length
      
      years.forEach(year => {
        const subjects = provinceData[year]
        totalSubjects += subjects.length
        subjects.forEach(subject => {
          subjectTypes.add(subject.name)
        })
      })
    })
    
    return {
      totalProvinces: provinces.length,
      totalYears,
      totalSubjects,
      uniqueSubjectTypes: Array.from(subjectTypes),
      dataStatus: data.DataStatus
    }
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error)
    return null
  }
}

console.log('🔧 科目选择工具函数已加载')
