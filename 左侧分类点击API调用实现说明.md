# 左侧分类点击API调用实现说明

## 🎯 问题描述

用户反馈点击左侧菜单没有调用接口，应该调用接口获取数据，传参形式为：
```
/ceemajor/tree?name=&categoryId=医学&educationLevel=本科(普通教育)
```

## 🔍 问题分析

### 原有逻辑问题
1. **只做前端筛选**：点击分类时只是设置`selectedCategory`状态，然后在前端对已有数据进行筛选
2. **数据不完整**：由于API按需加载机制，未点击的分类没有详细的专业数据
3. **用户体验差**：用户点击其他分类时看不到任何专业，因为数据未加载

### 期望的正确流程
1. 用户点击左侧分类（如"医学"）
2. 调用API：`/ceemajor/tree?categoryId=医学&educationLevel=本科(普通教育)`
3. 获取该分类的详细专业数据
4. 更新页面显示该分类的专业列表

## 🔧 实现方案

### 1. 修改数据加载函数

在`src/components/major/MajorSearchPage.tsx`中修改`loadMajorData`函数：

```typescript
// 加载专业数据
const loadMajorData = async (keyword?: string, categoryId?: string) => {
  if (!useApiData) return

  setLoading(true)
  setError(null)

  try {
    console.log('🔍 加载专业数据，教育层次:', selectedEducationLevel, '关键词:', keyword, '分类ID:', categoryId)

    let response
    if (keyword && keyword.trim()) {
      // 如果有搜索关键词，使用搜索API
      response = await searchMajors(keyword.trim(), selectedEducationLevel as ApiEducationLevel)
    } else if (categoryId) {
      // 如果有分类ID，使用getMajorTree API获取该分类的数据
      response = await getMajorTree('', categoryId, selectedEducationLevel)
    } else {
      // 按教育层次获取专业数据
      response = await getAllMajors(selectedEducationLevel as ApiEducationLevel)
    }
    
    // ... 处理响应数据
  } catch (err) {
    // ... 错误处理
  }
}
```

### 2. 新增分类选择处理函数

```typescript
// 分类选择处理
const handleCategorySelect = (category: DisciplineCategory | undefined) => {
  setSelectedCategory(category)
  setSelectedSubCategory(undefined) // 清除子分类选择
  
  if (useApiData) {
    // 调用API获取该分类的数据
    loadMajorData(undefined, category)
  }
}
```

### 3. 更新CategorySidebar调用

```typescript
<CategorySidebar
  selectedCategory={selectedCategory}
  selectedSubCategory={selectedSubCategory}
  onCategorySelect={handleCategorySelect}  // 使用新的处理函数
  onSubCategorySelect={setSelectedSubCategory}
  className="sticky top-24"
  // 传入动态数据
  categories={useApiData ? dynamicCategories.categories : undefined}
  categoryStats={useApiData ? dynamicCategories.categoryStats : undefined}
  subCategories={useApiData ? dynamicCategories.subCategories : undefined}
/>
```

### 4. 优化筛选逻辑

修改教育层次筛选逻辑，当有分类ID时不进行客户端筛选：

```typescript
// 如果没有传教育层次参数，需要在客户端进行筛选
let filteredMajors = flatMajors
if ((!keyword || !keyword.trim()) && !categoryId) {
  // 按教育层次筛选（只在没有分类ID时进行筛选）
  // ... 筛选逻辑
}
```

## 📊 API调用示例

### 点击"医学"分类
```
GET /admin-api/system/metadata/ceemajor/tree?categoryId=医学&educationLevel=本科(普通教育)
```

### 点击"工学"分类
```
GET /admin-api/system/metadata/ceemajor/tree?categoryId=工学&educationLevel=本科(普通教育)
```

### 点击"全部专业"
```
GET /admin-api/system/metadata/ceemajor/all?educationLevel=本科(普通教育)
```

## 🧪 测试功能增强

在`src/components/major/MajorDataTest.tsx`中添加了分类API测试功能：

```typescript
const testCategoryApi = async (categoryId: string) => {
  try {
    console.log(`🧪 测试${categoryId}分类API调用...`)
    
    const response = await getMajorTree('', categoryId, '本科(普通)')
    console.log(`✅ ${categoryId}分类API调用成功:`, response)
    
    // 处理返回的数据
    const flatMajors = flattenMajorTree(response.data)
    const transformedMajors = flatMajors.map(transformApiDataToMajor)
    
    // 更新测试结果显示
    setResult({
      original: response.data,
      categories: [categoryId],
      categoryStats: [{category: categoryId, count: transformedMajors.length}],
      flattened: flatMajors,
      transformed: transformedMajors
    })
  } catch (err) {
    console.error(`❌ ${categoryId}分类API调用失败:`, err)
    setError(`${categoryId}分类API调用失败: ${err instanceof Error ? err.message : '未知错误'}`)
  }
}
```

## 🎨 用户体验改进

### 修改前的用户体验
1. 点击"医学"分类 → 页面显示空白（因为没有医学专业数据）
2. 用户困惑为什么没有专业显示
3. 只有"农学"分类有数据可以显示

### 修改后的用户体验
1. 点击"医学"分类 → 显示加载状态
2. API调用完成 → 显示医学类的所有专业
3. 每个分类都能正确显示对应的专业数据
4. 用户可以浏览所有学科门类的专业

## 🔄 数据流程

```mermaid
graph TD
    A[用户点击分类] --> B[handleCategorySelect]
    B --> C[设置selectedCategory]
    B --> D[清除selectedSubCategory]
    B --> E[调用loadMajorData]
    E --> F[调用getMajorTree API]
    F --> G[获取分类专业数据]
    G --> H[flattenMajorTree处理]
    H --> I[transformApiDataToMajor转换]
    I --> J[更新页面显示]
```

## ✅ 验证要点

1. **API调用正确**：点击分类时调用正确的API端点
2. **参数传递正确**：categoryId和educationLevel参数正确传递
3. **数据处理正确**：返回的数据正确解析和转换
4. **页面更新正确**：专业列表正确更新显示
5. **加载状态正确**：显示加载状态和错误处理
6. **状态管理正确**：分类选择状态正确更新

## 🧪 测试方法

1. **访问测试页面**：
   - 打开 http://localhost:5174
   - 点击"专业数据测试"
   - 点击"测试医学分类API"或"测试工学分类API"
   - 查看控制台输出和API调用结果

2. **实际使用测试**：
   - 返回首页，点击"查专业"
   - 点击左侧不同的学科分类
   - 观察是否调用API并显示对应专业
   - 检查浏览器网络面板的API调用

3. **控制台验证**：
   - 打开浏览器开发者工具
   - 查看Network面板的API调用
   - 验证请求URL和参数是否正确

## 🚀 后续优化建议

1. **缓存机制**：缓存已加载的分类数据，避免重复请求
2. **预加载**：预加载热门分类的数据
3. **错误重试**：API调用失败时提供重试机制
4. **加载优化**：添加骨架屏和更好的加载状态
5. **数据合并**：智能合并多次API调用的结果

现在点击左侧分类应该会正确调用API获取对应分类的专业数据了！
