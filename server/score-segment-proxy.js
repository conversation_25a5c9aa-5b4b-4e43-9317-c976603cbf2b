const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();
const port = 3001;

// 启用CORS
app.use(cors());
app.use(express.json());

// 真实API代理
const REAL_API_BASE = 'https://card.kefeichangduo.top';

// 代理到真实API
app.use('/admin-api', createProxyMiddleware({
  target: REAL_API_BASE,
  changeOrigin: true,
  pathRewrite: {
    '^/admin-api': '/admin-api'
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('代理请求到:', REAL_API_BASE + req.url);
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    // 如果代理失败，返回模拟数据
    if (req.url.includes('/system/score-segment/by-condition')) {
      console.log('代理失败，返回模拟数据');
      return handleMockData(req, res);
    }
    res.status(500).json({ error: '代理服务器错误' });
  }
}));

function handleMockData(req, res) {
  const { year, provincename, subjectselection, minscore, maxscore, batchname } = req.query;
  
  console.log('收到一分一段查询请求:', {
    year,
    provincename,
    subjectselection,
    minscore,
    maxscore,
    batchname
  });

  // 模拟数据
  const mockData = [];
  const startScore = maxscore ? parseInt(maxscore) : 750;
  const endScore = minscore ? parseInt(minscore) : 400;
  
  for (let score = startScore; score >= endScore && mockData.length < 50; score -= 1) {
    const candidateCount = Math.floor(Math.random() * 100) + 1;
    const totalCandidates = (startScore - score + 1) * 50 + Math.floor(Math.random() * 1000);
    
    mockData.push({
      id: mockData.length + 1,
      year: year || '2024',
      provinceName: provincename || '安徽',
      subjectSelection: subjectselection || '物理类',
      examinationScore: score.toString(),
      candidateCount: candidateCount,
      totalCandidates: totalCandidates,
      rankingRange: `${totalCandidates - candidateCount + 1}-${totalCandidates}`,
      admissionBatchName: batchname || '本科批',
      minimumAdmissionScore: '400',
      ranking: totalCandidates.toString(),
      historicalScores: JSON.stringify([
        {
          AcademicYear: 2023,
          ExaminationScore: (score + 10).toString(),
          RankingRange: `${totalCandidates - 1000}-${totalCandidates - 500}`
        }
      ])
    });
  }

  // 返回符合文档格式的响应
  res.json({
    code: 0,
    data: mockData,
    msg: '查询成功'
  });
}

// 备用模拟数据端点
app.get('/mock/admin-api/system/score-segment/by-condition', (req, res) => {
  console.log('使用模拟数据端点');
  handleMockData(req, res);
});

app.listen(port, () => {
  console.log(`一分一段代理服务器运行在 http://localhost:${port}`);
  console.log('真实API代理: /admin-api/* -> https://card.kefeichangduo.top/admin-api/*');
  console.log('模拟数据端点: /mock/admin-api/system/score-segment/by-condition');
  console.log('如果真实API不可用，将自动返回模拟数据');
});
