{"name": "baidu-appbuilder-proxy", "version": "1.0.0", "description": "百度AppBuilder API代理服务器，用于解决CORS跨域问题", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js", "install-deps": "npm install"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "http-proxy-middleware": "^2.0.6", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["proxy", "cors", "baidu", "appbuilder", "api"], "author": "Your Name", "license": "MIT"}