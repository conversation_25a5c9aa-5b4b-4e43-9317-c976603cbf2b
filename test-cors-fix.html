<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨域问题测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>跨域问题修复验证</h1>
    
    <div class="info">
        <h3>测试说明</h3>
        <p>这个页面用于测试一分一段API的跨域问题是否已经解决。</p>
        <p>当前测试的API端点：<code>http://localhost:3001/admin-api/system/score-segment/by-condition</code></p>
    </div>

    <button onclick="testAPI()">测试API调用</button>
    <button onclick="testCORS()">测试CORS头</button>
    <button onclick="clearResults()">清除结果</button>

    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(type, title, content) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h4>${title}</h4><div>${content}</div>`;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testAPI() {
            addResult('info', '开始测试', '正在测试API调用...');
            
            const url = 'http://localhost:3001/admin-api/system/score-segment/by-condition?year=2025&provincename=安徽&subjectselection=物理类&minscore=524&maxscore=524';
            
            try {
                console.log('请求URL:', url);
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);

                if (response.ok) {
                    const data = await response.json();
                    console.log('响应数据:', data);
                    
                    addResult('success', 'API调用成功', `
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>数据条数:</strong> ${data.data ? data.data.length : 0}</p>
                        <p><strong>响应消息:</strong> ${data.msg}</p>
                        <pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>
                    `);
                } else {
                    const errorText = await response.text();
                    addResult('error', 'API调用失败', `
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>错误信息:</strong> ${errorText}</p>
                    `);
                }
            } catch (error) {
                console.error('请求错误:', error);
                addResult('error', '网络错误', `
                    <p><strong>错误类型:</strong> ${error.name}</p>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p>这通常表示存在跨域问题或网络连接问题。</p>
                `);
            }
        }

        async function testCORS() {
            addResult('info', '开始CORS测试', '正在检查CORS头...');
            
            try {
                const response = await fetch('http://localhost:3001/admin-api/system/score-segment/by-condition?year=2025&provincename=安徽&subjectselection=物理类&minscore=524&maxscore=524', {
                    method: 'OPTIONS'
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };

                addResult('success', 'CORS头检查', `
                    <p><strong>Access-Control-Allow-Origin:</strong> ${corsHeaders['Access-Control-Allow-Origin'] || '未设置'}</p>
                    <p><strong>Access-Control-Allow-Methods:</strong> ${corsHeaders['Access-Control-Allow-Methods'] || '未设置'}</p>
                    <p><strong>Access-Control-Allow-Headers:</strong> ${corsHeaders['Access-Control-Allow-Headers'] || '未设置'}</p>
                    <p><strong>Access-Control-Allow-Credentials:</strong> ${corsHeaders['Access-Control-Allow-Credentials'] || '未设置'}</p>
                `);
            } catch (error) {
                addResult('error', 'CORS测试失败', `
                    <p><strong>错误:</strong> ${error.message}</p>
                    <p>无法获取CORS头信息，可能存在跨域配置问题。</p>
                `);
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            addResult('info', '页面加载完成', '准备进行跨域测试...');
        });
    </script>
</body>
</html>
