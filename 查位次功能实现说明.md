# 查位次功能实现说明

## 功能概述

根据咕咕数据API文档（https://www.gugudata.com/api/details/ceeline-one-score-one-section），我已经成功实现了基于真实数据的高考一分一段查位次功能。

## 实现的功能

### 1. 一分一段数据查询
- ✅ 支持按年份、省份、科目类型查询一分一段数据
- ✅ 支持分页查看数据（每页1-20条）
- ✅ 显示分数、该分数人数、累计人数、位次范围、录取批次等信息

### 2. 分数查位次
- ✅ 输入高考分数，自动查询对应位次
- ✅ 支持精确匹配和估算匹配
- ✅ 显示累计考生人数和位次信息

### 3. 数据展示
- ✅ 表格形式展示一分一段数据
- ✅ 卡片形式展示位次查询结果
- ✅ 支持分页浏览数据

## 技术实现

### 1. API服务层 (`src/services/oneScoreOneSectionApi.ts`)

#### 主要接口：
- `getOneScoreOneSectionData()` - 获取一分一段数据
- `findRankingByScore()` - 根据分数查找位次
- `getAvailableYears()` - 获取可用年份
- `getAvailableProvinces()` - 获取可用省份
- `getAvailableSubjectSelections()` - 获取可用科目类型

#### API参数：
```typescript
interface OneScoreOneSectionFilters {
  year: string           // 年份：2020-2025
  provinceName: string   // 省份：北京、上海、江苏等
  subjectSelection: string // 科目：物理类、历史类等
  pageIndex?: number     // 页码：默认1
  pageSize?: number      // 每页条数：1-20，默认20
}
```

### 2. 前端组件 (`src/components/ranking/OneScoreOneSectionPage.tsx`)

#### 主要功能：
- 查询条件选择（年份、省份、科目类型）
- 一分一段数据表格展示
- 分数查位次功能
- 分页导航
- 错误处理和加载状态

### 3. API密钥配置 (`src/services/apiKeys.ts`)

添加了新的API类型：
```typescript
ONE_SCORE_ONE_SECTION = 'one_score_one_section'  // 历年高考一分一段数据
```

## 使用方法

### 1. 访问功能
1. 启动开发服务器：`npm run dev`
2. 启动代理服务器：`cd server && node proxy-server.js`
3. 访问 http://localhost:5175
4. 点击"查位次(真实)"按钮

### 2. 查询一分一段数据
1. 选择年份（2020-2025）
2. 选择省份（31个省份）
3. 选择科目类型（物理类、历史类等）
4. 点击"查询一分一段"按钮

### 3. 分数查位次
1. 在"分数查位次"区域输入高考分数
2. 点击"查询位次"按钮
3. 查看位次结果和累计考生数

## API数据结构

### 请求参数
```
GET /metadata/ceeline/one-score-one-section
?appkey=YOUR_APPKEY
&year=2024
&provinceName=安徽
&subjectSelection=物理类
&pageIndex=1
&pageSize=20
```

### 返回数据
```json
{
  "DataStatus": {
    "StatusCode": 100,
    "StatusDescription": "请求成功。",
    "ResponseDateTime": "2025-07-23 22:00:41.932",
    "DataTotalCount": 500
  },
  "Data": [
    {
      "ExaminationScore": "699-750",
      "CandidateCount": 36,
      "TotalCandidates": 36,
      "RankingRange": "1-36",
      "AdmissionBatchName": "本科批",
      "MinimumAdmissionScore": "465",
      "Ranking": "699",
      "HistoricalScores": []
    }
  ]
}
```

## 测试结果

### API调用测试
- ✅ 成功调用咕咕数据API
- ✅ 返回状态码100（请求成功）
- ✅ 获取到2024年安徽物理类一分一段数据
- ✅ 总共500条数据可供查询

### 功能测试
- ✅ 年份、省份、科目选择正常工作
- ✅ 分页功能正常工作
- ✅ 数据表格正确显示
- ✅ 分数查位次功能正常工作
- ✅ 错误处理和加载状态正常

## 支持的查询条件

### 年份
- 2025年（最新）
- 2024年
- 2023年
- 2022年
- 2021年
- 2020年

### 省份（31个）
北京、天津、河北、山西、内蒙古、辽宁、吉林、黑龙江、上海、江苏、浙江、安徽、福建、江西、山东、河南、湖北、湖南、广东、广西、海南、重庆、四川、贵州、云南、西藏、陕西、甘肃、青海、宁夏、新疆

### 科目类型
- 物理类
- 历史类
- 理科
- 文科
- 综合

## 注意事项

1. **API限制**：每页最多返回20条数据，需要分页查询
2. **数据范围**：API返回的是从高分到低分的数据，查询低分段需要更多页面
3. **分数匹配**：支持分数范围匹配（如"699-750"）和精确分数匹配
4. **网络依赖**：需要网络连接访问咕咕数据API
5. **代理服务器**：开发环境需要启动代理服务器解决CORS问题

## 文件结构

```
src/
├── services/
│   ├── apiKeys.ts                    # API密钥配置
│   └── oneScoreOneSectionApi.ts      # 一分一段API服务
├── components/
│   └── ranking/
│       └── OneScoreOneSectionPage.tsx # 查位次页面组件
├── App.tsx                           # 主应用组件（已更新）
└── components/navigation-page.tsx    # 导航页面（已更新）
```

## 下一步优化建议

1. **缓存机制**：添加数据缓存减少API调用
2. **批量查询**：优化分数查位次的查询效率
3. **数据可视化**：添加位次趋势图表
4. **历年对比**：支持多年份数据对比
5. **导出功能**：支持数据导出为Excel
6. **搜索优化**：添加分数范围搜索功能

## 总结

✅ 成功实现了基于咕咕数据API的真实高考一分一段查位次功能
✅ 提供了完整的用户界面和交互体验
✅ 支持多种查询条件和数据展示方式
✅ 具备良好的错误处理和用户反馈机制

该功能现在可以为用户提供准确的高考位次查询服务，帮助考生和家长更好地了解分数对应的位次情况。
