# 目标设置功能增强说明

## 功能概述

基于咕咕数据的高校录取分数线API（https://www.gugudata.com/api/details/ceecollegeline），我们对目标设置功能进行了重大增强，现在可以根据用户的实际分数为其推荐合适的大学，并显示录取可能性。

## 主要改进

### 1. 集成高校录取分数线数据

- **API集成**：使用咕咕数据的 `ceecollegeline` 接口获取高校历年录取分数线
- **实时数据**：获取2024年最新的录取分数线数据
- **多维度信息**：包括最低分、平均分、最高分、录取位次等

### 2. 用户分数智能分析

- **自动获取**：从用户的考试记录中自动获取最新分数
- **分数展示**：在大学选择界面显示用户当前分数信息
- **基准对比**：将用户分数与各大学录取分数线进行对比

### 3. 录取可能性评估

根据用户分数与历年录取分数线的对比，将大学分为四个等级：

- **录取希望大**（绿色）：用户分数 ≥ 平均分 + 20分
- **有录取希望**（黄色）：用户分数 ≥ 最低分
- **录取希望较小**（橙色）：用户分数 ≥ 最低分 - 20分
- **录取困难**（红色）：用户分数 < 最低分 - 20分

### 4. 智能排序和筛选

- **智能排序**：根据录取可能性自动排序，优先显示录取希望大的大学
- **筛选功能**：用户可以按录取可能性筛选大学列表
- **快速定位**：帮助用户快速找到适合自己分数段的大学

### 5. 丰富的信息展示

每个大学卡片现在显示：
- 基本信息（名称、位置、985/211/双一流标识）
- 录取可能性标签
- 2024年录取分数线（最低分、平均分）
- 录取位次信息（如果有）

## 技术实现

### 核心文件修改

1. **src/components/goal/GoalSettingPage.tsx**
   - 添加录取分数线数据获取逻辑
   - 实现录取可能性计算算法
   - 增强大学列表展示界面
   - 添加筛选和排序功能

2. **src/services/scoreLineApiSimple.ts**
   - 已存在的录取分数线API服务
   - 提供高校录取分数线查询功能

### 关键功能函数

1. **fetchUniversityScoreLine()** - 获取单个大学的录取分数线
2. **calculateAdmissionPossibility()** - 计算录取可能性
3. **calculateAdmissionPossibilityForSort()** - 用于排序的简化版计算
4. 智能筛选和排序逻辑

## 用户体验改进

### 1. 个性化推荐
- 基于用户实际分数提供个性化的大学推荐
- 避免用户浪费时间查看不合适的大学

### 2. 直观的视觉反馈
- 使用颜色编码显示录取可能性
- 清晰的分数线对比信息
- 用户友好的界面设计

### 3. 高效的筛选体验
- 一键筛选不同录取可能性的大学
- 智能排序减少用户搜索时间
- 实时的分数线数据展示

## 数据来源

- **API提供商**：咕咕数据（GuGuData）
- **数据接口**：历年高考高校录取分数线
- **数据范围**：全国高校在各省的录取分数线（2015-2025年）
- **更新频率**：数据持续自动更新

## 使用场景

1. **高三学生**：根据模考成绩选择合适的目标大学
2. **家长指导**：帮助家长了解孩子的录取可能性
3. **志愿填报**：为高考志愿填报提供数据支持
4. **学习规划**：制定合理的学习目标和计划

## 后续优化方向

1. **多年数据对比**：显示多年录取分数线趋势
2. **专业级别分析**：扩展到具体专业的录取分数线
3. **位次分析**：基于位次进行更精确的录取可能性分析
4. **地区偏好**：根据用户地区偏好调整推荐权重
5. **批次分析**：区分不同录取批次的分析

## 注意事项

1. 录取分数线数据仅供参考，实际录取情况可能因多种因素而变化
2. 建议用户结合多次考试成绩进行综合评估
3. 录取可能性评估基于历史数据，不能保证实际录取结果
4. 用户应该同时考虑专业兴趣、地理位置等其他因素

---

通过这些增强功能，目标设置系统现在能够为用户提供更加智能、个性化的大学选择建议，大大提升了用户体验和实用性。
