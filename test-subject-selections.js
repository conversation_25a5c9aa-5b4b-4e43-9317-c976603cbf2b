// 测试科目选择功能的脚本
import { getSubjectSelectionsByProvinceAndYear } from './src/utils/subjectSelectionUtils.js'

console.log('🧪 开始测试科目选择功能...\n')

// 测试用例
const testCases = [
  { province: '安徽', year: '2025', expected: ['物理类', '历史类'] },
  { province: '安徽', year: '2022', expected: ['理科', '文科'] },
  { province: '北京', year: '2025', expected: ['综合'] },
  { province: '北京', year: '2019', expected: ['理科', '文科'] },
  { province: '河北', year: '2024', expected: ['物理类', '历史类'] },
  { province: '河北', year: '2020', expected: ['理科', '文科', '体育文', '体育理'] },
  { province: '山东', year: '2024', expected: ['综合'] },
  { province: '山东', year: '2019', expected: ['理科', '文科'] }
]

// 执行测试
testCases.forEach(({ province, year, expected }, index) => {
  console.log(`📋 测试 ${index + 1}: ${province} ${year}年`)
  
  try {
    const result = getSubjectSelectionsByProvinceAndYear(province, year)
    console.log(`   实际结果: [${result.join(', ')}]`)
    console.log(`   期望结果: [${expected.join(', ')}]`)
    
    const isMatch = JSON.stringify(result.sort()) === JSON.stringify(expected.sort())
    console.log(`   测试结果: ${isMatch ? '✅ 通过' : '❌ 失败'}`)
    
    if (!isMatch) {
      console.log(`   ⚠️  结果不匹配！`)
    }
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`)
  }
  
  console.log('')
})

console.log('🎯 测试完成！')
